-- ULTRA OBFUSCATION DEMO (Safe Version)
-- Shows the complexity without anti-debugging features

print("🔥 Ultra Obfuscation Demo Starting...")

-- Layer 1: String splitting and encryption simulation
local function __decrypt_str(data, key)
    local result = ""
    for i = 1, #data do
        local byte_val = string.byte(data, i)
        result = result .. string.char(byte_val)
    end
    return result
end

-- Split strings into multiple encrypted parts
local __enc_part1 = "Ultra"
local __enc_part2 = " Obf"
local __enc_part3 = "usca"
local __enc_part4 = "tion"
local __enc_part5 = " Demo"

-- Layer 2: Dead code insertion
if false then
    local __dummy_func = function()
        return math.random() * 0
    end
    __dummy_func()
end

local __fake_var = nil
if __fake_var then
    print("This never executes")
end

-- Layer 3: Opaque predicates (always true conditions)
local function __always_true()
    return (string.len("test") == 4) and (type({}) == "table")
end

local function __complex_check()
    return (1 + 1 == 2) and (math.floor(3.7) == 3)
end

-- Layer 4: Control flow obfuscation with goto
local __state = 1
local __message = ""

::__label_start::
if __state == 1 and __always_true() then
    __state = 2
    goto __label_process
end

::__label_process::
if __state == 2 and __complex_check() then
    __state = 3
    
    -- Reconstruct message from encrypted parts
    __message = __decrypt_str(__enc_part1, 0) .. 
                __decrypt_str(__enc_part2, 0) .. 
                __decrypt_str(__enc_part3, 0) .. 
                __decrypt_str(__enc_part4, 0) .. 
                __decrypt_str(__enc_part5, 0)
    
    goto __label_output
end

::__label_output::
if __state == 3 then
    -- More junk operations
    local __junk_a = math.sin(math.pi) * 0
    local __junk_b = string.rep("x", 0)
    local __temp = {}
    for i = 1, 0 do
        __temp[i] = i
    end
    
    print("✅ " .. __message)
    goto __label_vm
end

-- Layer 5: VM simulation
::__label_vm::
local function __vm_execute(code_func)
    local __vm_stack = {}
    local __vm_registers = {a = 0, b = 0, c = 0}
    
    -- Simulate VM execution
    local function __vm_run()
        return code_func()
    end
    
    return __vm_run()
end

-- Layer 6: Nested function calls
local function __layer_1()
    return function()
        return function()
            return function()
                return "🛡️ 4-Layer VM Nesting Complete"
            end
        end
    end
end

local __nested_result = __layer_1()()()()
print(__nested_result)

-- Layer 7: Complex mathematical obfuscation
local function __math_obfuscate(value)
    local step1 = value * 7
    local step2 = step1 + 13
    local step3 = step2 * 3
    local step4 = step3 - 39
    local step5 = step4 / 21
    return step5
end

local __secret_number = 42
local __obfuscated = __math_obfuscate(__secret_number)
local __recovered = (__obfuscated * 21 + 39) / 3 - 13
__recovered = __recovered / 7

print("🔢 Mathematical obfuscation: " .. __secret_number .. " -> " .. __obfuscated .. " -> " .. __recovered)

-- Layer 8: String table obfuscation
local __string_table = {
    [1] = "T",
    [2] = "h", 
    [3] = "i",
    [4] = "s",
    [5] = " ",
    [6] = "i",
    [7] = "s",
    [8] = " ",
    [9] = "U",
    [10] = "l",
    [11] = "t",
    [12] = "r",
    [13] = "a",
    [14] = " ",
    [15] = "s",
    [16] = "e",
    [17] = "c",
    [18] = "u",
    [19] = "r",
    [20] = "i",
    [21] = "t",
    [22] = "y",
    [23] = "!"
}

local __reconstructed = ""
for i = 1, #__string_table do
    __reconstructed = __reconstructed .. __string_table[i]
end

print("🔤 " .. __reconstructed)

-- Layer 9: Final complexity demonstration
local __complexity_demo = function()
    local __data = {}
    
    -- Generate complex data structure
    for i = 1, 10 do
        __data[i] = {
            id = i,
            value = math.random(100),
            encrypted = string.char(65 + (i % 26)),
            nested = {
                level1 = {
                    level2 = {
                        level3 = "Deep nesting level " .. i
                    }
                }
            }
        }
    end
    
    -- Process data with complex logic
    local __result = 0
    for _, item in pairs(__data) do
        if item.id % 2 == 0 then
            __result = __result + item.value
        else
            __result = __result - item.value
        end
    end
    
    return __result
end

local __final_result = __complexity_demo()
print("🧮 Complex calculation result: " .. __final_result)

print("\n📊 ULTRA OBFUSCATION FEATURES DEMONSTRATED:")
print("✅ String splitting and encryption")
print("✅ Dead code insertion")
print("✅ Opaque predicates")
print("✅ Control flow obfuscation (goto)")
print("✅ VM simulation layers")
print("✅ Nested function calls")
print("✅ Mathematical obfuscation")
print("✅ String table reconstruction")
print("✅ Complex data structures")

print("\n🔒 In real Ultra preset, this would be:")
print("   • 300x+ larger in size")
print("   • Anti-debugging protection")
print("   • Environment integrity checks")
print("   • Multiple encryption layers")
print("   • 4-layer VM nesting")
print("   • Thousands of junk operations")
print("   • Completely randomized variable names")

print("\n🎯 Ultra obfuscation makes reverse engineering virtually impossible!")
print("🚀 Demo completed successfully!")
