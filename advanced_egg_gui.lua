if getgenv().EggDisplay_Cleanup then
    getgenv().EggDisplay_Cleanup()
end

local replicatedStorage = game:GetService("ReplicatedStorage")
local collectionService = game:GetService("CollectionService")
local players = game:GetService("Players")
local runService = game:GetService("RunService")
local coreGui = game:GetService("CoreGui")
local tweenService = game:GetService("TweenService")
local userInputService = game:GetService("UserInputService")

local localPlayer = players.LocalPlayer
local currentCamera = workspace.CurrentCamera

local hatchFunction = getupvalue(getupvalue(getconnections(replicatedStorage.GameEvents.PetEggService.OnClientEvent)[1].Function, 1), 2)
local eggModels = getupvalue(hatchFunction, 1)
local eggPets = getupvalue(hatchFunction, 2)

local petRegistry = nil
pcall(function()
    petRegistry = require(replicatedStorage.Data.PetRegistry.PetList)
end)

local eggData = nil
pcall(function()
    eggData = require(replicatedStorage.Data.PetRegistry.PetEggs)
end)

local espCache = {}
local activeEggs = {}
local connections = {}
local selectedEgg = nil

local gui = Instance.new("ScreenGui")
gui.Name = "EggDisplay"
gui.Parent = coreGui

local mainFrame = Instance.new("Frame")
mainFrame.Name = "MainFrame"
mainFrame.Size = UDim2.new(0, 320, 0, 220)
mainFrame.Position = UDim2.new(0, 50, 0, 50)
mainFrame.BackgroundColor3 = Color3.fromRGB(25, 25, 35)
mainFrame.BorderSizePixel = 0
mainFrame.Parent = gui

local corner = Instance.new("UICorner")
corner.CornerRadius = UDim.new(0, 15)
corner.Parent = mainFrame

local gradient = Instance.new("UIGradient")
gradient.Color = ColorSequence.new{
    ColorSequenceKeypoint.new(0, Color3.fromRGB(35, 35, 50)),
    ColorSequenceKeypoint.new(1, Color3.fromRGB(15, 15, 25))
}
gradient.Rotation = 45
gradient.Parent = mainFrame

local titleLabel = Instance.new("TextLabel")
titleLabel.Name = "Title"
titleLabel.Size = UDim2.new(1, 0, 0, 45)
titleLabel.Position = UDim2.new(0, 0, 0, 0)
titleLabel.BackgroundTransparency = 1
titleLabel.Text = "🥚 Advanced Egg Reroller"
titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
titleLabel.TextScaled = true
titleLabel.Font = Enum.Font.GothamBold
titleLabel.Parent = mainFrame

local rerollButton = Instance.new("TextButton")
rerollButton.Name = "RerollButton"
rerollButton.Size = UDim2.new(0.85, 0, 0, 55)
rerollButton.Position = UDim2.new(0.075, 0, 0.25, 0)
rerollButton.BackgroundColor3 = Color3.fromRGB(255, 100, 100)
rerollButton.BorderSizePixel = 0
rerollButton.Text = "🎲 REROLL SELECTED EGG"
rerollButton.TextColor3 = Color3.fromRGB(255, 255, 255)
rerollButton.TextScaled = true
rerollButton.Font = Enum.Font.GothamBold
rerollButton.Parent = mainFrame

local rerollCorner = Instance.new("UICorner")
rerollCorner.CornerRadius = UDim.new(0, 10)
rerollCorner.Parent = rerollButton

local rerollGradient = Instance.new("UIGradient")
rerollGradient.Color = ColorSequence.new{
    ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 120, 120)),
    ColorSequenceKeypoint.new(1, Color3.fromRGB(255, 80, 80))
}
rerollGradient.Rotation = 90
rerollGradient.Parent = rerollButton

local statusLabel = Instance.new("TextLabel")
statusLabel.Name = "Status"
statusLabel.Size = UDim2.new(0.9, 0, 0, 35)
statusLabel.Position = UDim2.new(0.05, 0, 0.55, 0)
statusLabel.BackgroundTransparency = 1
statusLabel.Text = "Click an egg to select it"
statusLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
statusLabel.TextScaled = true
statusLabel.Font = Enum.Font.Gotham
statusLabel.Parent = mainFrame

local eggInfoLabel = Instance.new("TextLabel")
eggInfoLabel.Name = "EggInfo"
eggInfoLabel.Size = UDim2.new(0.9, 0, 0, 45)
eggInfoLabel.Position = UDim2.new(0.05, 0, 0.72, 0)
eggInfoLabel.BackgroundTransparency = 1
eggInfoLabel.Text = ""
eggInfoLabel.TextColor3 = Color3.fromRGB(255, 215, 0)
eggInfoLabel.TextScaled = true
eggInfoLabel.Font = Enum.Font.GothamBold
eggInfoLabel.Parent = mainFrame

local function getObjectFromId(objectId)
    for eggModel in eggModels do
        if eggModel:GetAttribute("OBJECT_UUID") ~= objectId then continue end
        return eggModel
    end
end

local function getRandomPetFromEgg(eggName)
    if not eggData or not eggData[eggName] then return nil end
    
    local eggInfo = eggData[eggName]
    if not eggInfo.RarityData or not eggInfo.RarityData.Items then return nil end
    
    local pets = {}
    local totalWeight = 0
    
    for petName, petData in pairs(eggInfo.RarityData.Items) do
        local weight = petData.ItemOdd or 1
        table.insert(pets, {name = petName, weight = weight})
        totalWeight = totalWeight + weight
    end
    
    if #pets == 0 then return nil end
    
    local random = math.random() * totalWeight
    local currentWeight = 0
    
    for _, pet in ipairs(pets) do
        currentWeight = currentWeight + pet.weight
        if random <= currentWeight then
            return pet.name
        end
    end
    
    return pets[1].name
end

local function getPetWeight(petName)
    if not petRegistry or not petName then 
        return math.random(80, 200) / 100
    end
    
    local petData = petRegistry[petName]
    if not petData then
        return math.random(80, 200) / 100
    end
    
    local weight = petData.SellPrice and (petData.SellPrice / 1000000) or math.random(80, 200) / 100
    return weight
end

local function manualReroll(objectId)
    if not objectId or not espCache[objectId] then return end
    
    local object = getObjectFromId(objectId)
    if not object then return end
    
    local eggName = object:GetAttribute("EggName")
    local randomPet = getRandomPetFromEgg(eggName)
    
    if randomPet then
        local weight = getPetWeight(randomPet)
        
        local frame = espCache[objectId].frame
        local nameLabel = espCache[objectId].nameLabel
        local weightLabel = espCache[objectId].weightLabel
        local glowFrame = espCache[objectId].glowFrame
        
        nameLabel.Text = randomPet
        weightLabel.Text = string.format("⚖️ %.1f KG", weight)
        
        local color = Color3.fromRGB(255, 255, 255)
        if weight >= 6.0 then
            color = Color3.fromRGB(255, 215, 0)
            glowFrame.BackgroundColor3 = Color3.fromRGB(255, 215, 0)
            glowFrame.BackgroundTransparency = 0.6
        elseif weight >= 4.0 then
            color = Color3.fromRGB(255, 165, 0)
            glowFrame.BackgroundColor3 = Color3.fromRGB(255, 165, 0)
            glowFrame.BackgroundTransparency = 0.7
        elseif weight >= 2.0 then
            color = Color3.fromRGB(100, 255, 100)
            glowFrame.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
            glowFrame.BackgroundTransparency = 0.7
        else
            glowFrame.BackgroundTransparency = 1
        end
        
        nameLabel.TextColor3 = color
        weightLabel.TextColor3 = color
        
        local tween = tweenService:Create(frame, TweenInfo.new(0.3, Enum.EasingStyle.Back), {Size = UDim2.new(0, 170, 0, 85)})
        tween:Play()
        tween.Completed:Connect(function()
            tweenService:Create(frame, TweenInfo.new(0.2, Enum.EasingStyle.Back), {Size = UDim2.new(0, 160, 0, 80)}):Play()
        end)
    end
end

rerollButton.MouseButton1Click:Connect(function()
    if selectedEgg then
        manualReroll(selectedEgg)
        
        local buttonTween = tweenService:Create(rerollButton, TweenInfo.new(0.1, Enum.EasingStyle.Quad), {Size = UDim2.new(0.8, 0, 0, 50)})
        buttonTween:Play()
        buttonTween.Completed:Connect(function()
            tweenService:Create(rerollButton, TweenInfo.new(0.1, Enum.EasingStyle.Quad), {Size = UDim2.new(0.85, 0, 0, 55)}):Play()
        end)
    else
        statusLabel.Text = "⚠️ No egg selected!"
        statusLabel.TextColor3 = Color3.fromRGB(255, 100, 100)
        task.wait(2)
        statusLabel.Text = "Click an egg to select it"
        statusLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    end
end)

local dragToggle = nil
local dragSpeed = 0.25
local dragStart = nil
local startPos = nil

local function updateInput(input)
    local delta = input.Position - dragStart
    local position = UDim2.new(startPos.X.Scale, startPos.X.Offset + delta.X, startPos.Y.Scale, startPos.Y.Offset + delta.Y)
    tweenService:Create(mainFrame, TweenInfo.new(dragSpeed), {Position = position}):Play()
end

mainFrame.InputBegan:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseButton1 then
        dragToggle = true
        dragStart = input.Position
        startPos = mainFrame.Position
        input.Changed:Connect(function()
            if input.UserInputState == Enum.UserInputState.End then
                dragToggle = false
            end
        end)
    end
end)

userInputService.InputChanged:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseMovement and dragToggle then
        updateInput(input)
    end
end)

local function UpdateEsp(objectId, petName)
    local object = getObjectFromId(objectId)
    if not object or not espCache[objectId] then return end

    local weight = getPetWeight(petName)

    local frame = espCache[objectId].frame
    local nameLabel = espCache[objectId].nameLabel
    local weightLabel = espCache[objectId].weightLabel
    local glowFrame = espCache[objectId].glowFrame
    local statusLabel = espCache[objectId].statusLabel

    nameLabel.Text = petName or "?"
    weightLabel.Text = string.format("⚖️ %.1f KG", weight)
    statusLabel.Text = "✅ READY"
    statusLabel.TextColor3 = Color3.fromRGB(100, 255, 100)

    local color = Color3.fromRGB(255, 255, 255)
    if weight >= 6.0 then
        color = Color3.fromRGB(255, 215, 0)
        glowFrame.BackgroundColor3 = Color3.fromRGB(255, 215, 0)
        glowFrame.BackgroundTransparency = 0.6
    elseif weight >= 4.0 then
        color = Color3.fromRGB(255, 165, 0)
        glowFrame.BackgroundColor3 = Color3.fromRGB(255, 165, 0)
        glowFrame.BackgroundTransparency = 0.7
    elseif weight >= 2.0 then
        color = Color3.fromRGB(100, 255, 100)
        glowFrame.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
        glowFrame.BackgroundTransparency = 0.7
    else
        glowFrame.BackgroundTransparency = 1
    end

    nameLabel.TextColor3 = color
    weightLabel.TextColor3 = color
end

local function AddEsp(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end

    local eggName = object:GetAttribute("EggName")
    local petName = eggPets[object:GetAttribute("OBJECT_UUID")]
    local objectId = object:GetAttribute("OBJECT_UUID")
    if not objectId then return end

    local frame = Instance.new("Frame")
    frame.Size = UDim2.new(0, 160, 0, 80)
    frame.BackgroundColor3 = Color3.fromRGB(20, 20, 30)
    frame.BorderSizePixel = 0
    frame.Parent = gui

    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 12)
    corner.Parent = frame

    local glowFrame = Instance.new("Frame")
    glowFrame.Size = UDim2.new(1, 6, 1, 6)
    glowFrame.Position = UDim2.new(0, -3, 0, -3)
    glowFrame.BackgroundTransparency = 1
    glowFrame.BorderSizePixel = 0
    glowFrame.ZIndex = frame.ZIndex - 1
    glowFrame.Parent = frame

    local glowCorner = Instance.new("UICorner")
    glowCorner.CornerRadius = UDim.new(0, 15)
    glowCorner.Parent = glowFrame

    local gradient = Instance.new("UIGradient")
    gradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(40, 40, 60)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(20, 20, 30))
    }
    gradient.Rotation = 45
    gradient.Parent = frame

    local eggLabel = Instance.new("TextLabel")
    eggLabel.Size = UDim2.new(1, 0, 0, 22)
    eggLabel.Position = UDim2.new(0, 0, 0, 3)
    eggLabel.BackgroundTransparency = 1
    eggLabel.Text = eggName
    eggLabel.TextColor3 = Color3.fromRGB(150, 150, 150)
    eggLabel.TextScaled = true
    eggLabel.Font = Enum.Font.Gotham
    eggLabel.Parent = frame

    local nameLabel = Instance.new("TextLabel")
    nameLabel.Size = UDim2.new(1, 0, 0, 28)
    nameLabel.Position = UDim2.new(0, 0, 0, 22)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = petName or "🔄 Rolling..."
    nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.GothamBold
    nameLabel.Parent = frame

    local weightLabel = Instance.new("TextLabel")
    weightLabel.Size = UDim2.new(0.6, 0, 0, 22)
    weightLabel.Position = UDim2.new(0, 8, 1, -25)
    weightLabel.BackgroundTransparency = 1
    weightLabel.Text = petName and string.format("⚖️ %.1f KG", getPetWeight(petName)) or "⚖️ ?.? KG"
    weightLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    weightLabel.TextScaled = true
    weightLabel.Font = Enum.Font.Gotham
    weightLabel.Parent = frame

    local statusLabel = Instance.new("TextLabel")
    statusLabel.Size = UDim2.new(0.4, 0, 0, 22)
    statusLabel.Position = UDim2.new(0.6, 0, 1, -25)
    statusLabel.BackgroundTransparency = 1
    statusLabel.Text = petName and "✅ READY" or "⏳ WAIT"
    statusLabel.TextColor3 = petName and Color3.fromRGB(100, 255, 100) or Color3.fromRGB(255, 255, 100)
    statusLabel.TextScaled = true
    statusLabel.Font = Enum.Font.GothamBold
    statusLabel.Parent = frame

    local clickDetector = Instance.new("TextButton")
    clickDetector.Size = UDim2.new(1, 0, 1, 0)
    clickDetector.BackgroundTransparency = 1
    clickDetector.Text = ""
    clickDetector.Parent = frame

    clickDetector.MouseButton1Click:Connect(function()
        selectedEgg = objectId
        statusLabel.Text = "🎯 SELECTED"
        eggInfoLabel.Text = string.format("Selected: %s", eggName)

        for id, cache in pairs(espCache) do
            if id ~= objectId and cache.frame then
                cache.frame.BorderSizePixel = 0
            end
        end

        frame.BorderSizePixel = 3
        frame.BorderColor3 = Color3.fromRGB(255, 100, 100)

        local selectTween = tweenService:Create(frame, TweenInfo.new(0.2, Enum.EasingStyle.Back), {Size = UDim2.new(0, 170, 0, 85)})
        selectTween:Play()
        selectTween.Completed:Connect(function()
            tweenService:Create(frame, TweenInfo.new(0.2, Enum.EasingStyle.Back), {Size = UDim2.new(0, 160, 0, 80)}):Play()
        end)
    end)

    espCache[objectId] = {
        frame = frame,
        nameLabel = nameLabel,
        weightLabel = weightLabel,
        glowFrame = glowFrame,
        statusLabel = statusLabel
    }
    activeEggs[objectId] = object

    if petName then
        UpdateEsp(objectId, petName)
    end
end

local function RemoveEsp(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end

    local objectId = object:GetAttribute("OBJECT_UUID")
    if espCache[objectId] then
        if espCache[objectId].frame then
            espCache[objectId].frame:Destroy()
        end
        espCache[objectId] = nil
    end

    activeEggs[objectId] = nil

    if selectedEgg == objectId then
        selectedEgg = nil
        eggInfoLabel.Text = ""
        statusLabel.Text = "Click an egg to select it"
    end
end

local function UpdateAllEsp()
    for objectId, object in activeEggs do
        if not object or not object:IsDescendantOf(workspace) then
            activeEggs[objectId] = nil
            if espCache[objectId] and espCache[objectId].frame then
                espCache[objectId].frame.Visible = false
            end
            continue
        end

        local cache = espCache[objectId]
        if cache and cache.frame then
            local pos, onScreen = currentCamera:WorldToViewportPoint(object:GetPivot().Position)
            if onScreen then
                cache.frame.Position = UDim2.new(0, pos.X - 80, 0, pos.Y - 40)
                cache.frame.Visible = true
            else
                cache.frame.Visible = false
            end
        end
    end
end

for _, object in collectionService:GetTagged("PetEggServer") do
    task.spawn(AddEsp, object)
end

connections[#connections + 1] = collectionService:GetInstanceAddedSignal("PetEggServer"):Connect(AddEsp)
connections[#connections + 1] = collectionService:GetInstanceRemovedSignal("PetEggServer"):Connect(RemoveEsp)

local old; old = hookfunction(getconnections(replicatedStorage.GameEvents.EggReadyToHatch_RE.OnClientEvent)[1].Function, newcclosure(function(objectId, petName)
    UpdateEsp(objectId, petName)
    return old(objectId, petName)
end))

connections[#connections + 1] = runService.PreRender:Connect(UpdateAllEsp)

getgenv().EggDisplay_Cleanup = function()
    for _, connection in pairs(connections) do
        if connection then
            connection:Disconnect()
        end
    end

    for _, cache in pairs(espCache) do
        if cache and cache.frame then
            cache.frame:Destroy()
        end
    end

    if gui then
        gui:Destroy()
    end

    espCache = {}
    activeEggs = {}
    connections = {}
    selectedEgg = nil
end
