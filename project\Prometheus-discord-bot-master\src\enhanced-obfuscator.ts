import fs from 'fs';
import path from 'path';
import tmp from 'tmp';
import crypto from 'crypto';
import logger from './logger';

/**
 * Enhanced Obfuscation Module
 * Provides additional obfuscation layers beyond the standard Prometheus presets
 */

interface EnhancedOptions {
    antiDebug?: boolean;
    controlFlow?: boolean;
    deadCode?: boolean;
    stringSplitting?: boolean;
    opaquePredicates?: boolean;
    vmNesting?: number;
    constantFolding?: boolean;
    junkInsertion?: boolean;
    strength?: number; // 1-10
}

class EnhancedObfuscator {
    private options: EnhancedOptions;

    constructor(options: EnhancedOptions = {}) {
        this.options = {
            antiDebug: true,
            controlFlow: true,
            deadCode: true,
            stringSplitting: true,
            opaquePredicates: true,
            vmNesting: 3,
            constantFolding: true,
            junkInsertion: true,
            strength: 9,
            ...options
        };
    }

    /**
     * Apply enhanced obfuscation to already obfuscated code
     */
    async enhanceObfuscation(obfuscatedCode: string): Promise<string> {
        logger.log('🔒 Applying enhanced obfuscation layers...');
        
        let enhanced = obfuscatedCode;

        // Layer 1: Anti-debugging protection
        if (this.options.antiDebug) {
            enhanced = this.addAntiDebugProtection(enhanced);
        }

        // Layer 2: String splitting and encryption
        if (this.options.stringSplitting) {
            enhanced = this.applySplitStringObfuscation(enhanced);
        }

        // Layer 3: Dead code insertion
        if (this.options.deadCode) {
            enhanced = this.insertDeadCode(enhanced);
        }

        // Layer 4: Opaque predicates
        if (this.options.opaquePredicates) {
            enhanced = this.addOpaquePredicates(enhanced);
        }

        // Layer 5: Advanced control flow
        if (this.options.controlFlow) {
            enhanced = this.obfuscateControlFlow(enhanced);
        }

        // Layer 6: Junk code insertion
        if (this.options.junkInsertion) {
            enhanced = this.insertJunkCode(enhanced);
        }

        // Layer 7: Multiple VM wrapping
        for (let i = 0; i < (this.options.vmNesting || 1); i++) {
            enhanced = this.wrapInVM(enhanced, i);
        }

        // Layer 8: Final encryption layer
        enhanced = this.applyFinalEncryption(enhanced);

        logger.log(`✅ Enhanced obfuscation complete. Size: ${enhanced.length} chars`);
        return enhanced;
    }

    /**
     * Add anti-debugging and anti-analysis protection
     */
    private addAntiDebugProtection(code: string): string {
        const antiDebugCode = `
-- Anti-debugging protection
local function __check_debug()
    local start_time = os.clock()
    for i = 1, 1000 do end
    local end_time = os.clock()
    if (end_time - start_time) > 0.01 then
        error("Debug detected", 0)
    end
end

local function __check_env()
    if debug or getfenv ~= getfenv then
        error("Environment tampered", 0)
    end
end

__check_debug()
__check_env()

-- Environment integrity check
local __original_pairs = pairs
local __original_next = next
local function __verify_integrity()
    if pairs ~= __original_pairs or next ~= __original_next then
        error("Runtime tampered", 0)
    end
end
__verify_integrity()

`;
        return antiDebugCode + code;
    }

    /**
     * Split strings and encrypt them separately
     */
    private applySplitStringObfuscation(code: string): string {
        // Find string literals and split them
        return code.replace(/"([^"]{4,})"/g, (match, str) => {
            if (str.length < 4) return match;
            
            const parts = this.splitString(str, 2 + Math.floor(Math.random() * 3));
            const encrypted = parts.map(part => this.encryptString(part));
            const varNames = encrypted.map(() => this.generateRandomName());
            
            let result = '';
            encrypted.forEach((enc, i) => {
                result += `local ${varNames[i]} = "${enc}"; `;
            });
            
            const decryptCalls = varNames.map(name => `__decrypt(${name})`).join(' .. ');
            result += `(${decryptCalls})`;
            
            return result;
        });
    }

    /**
     * Insert dead code that looks functional but never executes
     */
    private insertDeadCode(code: string): string {
        const deadCodeSnippets = [
            `if false then local __dummy = function() return math.random() end end`,
            `local __fake_var = nil; if __fake_var then print("never") end`,
            `local function __unused() return 42 end`,
            `local __temp = {}; for i=1,0 do __temp[i] = i end`,
            `if 1 > 2 then error("impossible") end`,
        ];

        const lines = code.split('\n');
        const result = [];
        
        for (let i = 0; i < lines.length; i++) {
            result.push(lines[i]);
            
            // Randomly insert dead code
            if (Math.random() < 0.1) {
                const snippet = deadCodeSnippets[Math.floor(Math.random() * deadCodeSnippets.length)];
                result.push(snippet);
            }
        }
        
        return result.join('\n');
    }

    /**
     * Add opaque predicates (always true/false conditions that look complex)
     */
    private addOpaquePredicates(code: string): string {
        const predicates = [
            '(math.floor(math.random() * 2) == 0 or true)',
            '(string.len("test") == 4)',
            '(type({}) == "table")',
            '(1 + 1 == 2)',
            '(not not true)',
        ];

        return code.replace(/if\s+([^t][^h][^e][^n].*?)\s+then/g, (match, condition) => {
            const predicate = predicates[Math.floor(Math.random() * predicates.length)];
            return `if (${condition}) and ${predicate} then`;
        });
    }

    /**
     * Obfuscate control flow with goto statements and labels
     */
    private obfuscateControlFlow(code: string): string {
        // This is a simplified version - real implementation would be more complex
        const labels = [];
        for (let i = 0; i < 5; i++) {
            labels.push(this.generateRandomName());
        }

        let enhanced = `
local __state = 1
::${labels[0]}::
if __state == 1 then
    __state = 2
    goto ${labels[1]}
end
::${labels[1]}::
if __state == 2 then
    __state = 3
    -- Original code starts here
${code}
    -- Original code ends here
    goto ${labels[2]}
end
::${labels[2]}::
return
`;

        return enhanced;
    }

    /**
     * Insert junk code that performs meaningless operations
     */
    private insertJunkCode(code: string): string {
        const junkSnippets = [
            `local __junk${Math.random().toString(36)} = math.sin(math.pi) * 0`,
            `local __temp${Math.random().toString(36)} = string.rep("x", 0)`,
            `local __dummy${Math.random().toString(36)} = table.concat({})`,
            `math.randomseed(os.time()); math.random()`,
        ];

        const lines = code.split('\n');
        const result = [];
        
        for (const line of lines) {
            result.push(line);
            
            // Randomly insert junk code
            if (Math.random() < 0.05) {
                const junk = junkSnippets[Math.floor(Math.random() * junkSnippets.length)];
                result.push(junk);
            }
        }
        
        return result.join('\n');
    }

    /**
     * Wrap code in additional VM layer
     */
    private wrapInVM(code: string, layer: number): string {
        const vmName = this.generateRandomName();
        const dataName = this.generateRandomName();
        
        return `
local function ${vmName}(${dataName})
    local __vm_state = 0
    local __vm_stack = {}
    local __vm_code = ${dataName}
    
    -- VM execution logic
    local function __vm_exec()
        return load(__vm_code)()
    end
    
    return __vm_exec()
end

return ${vmName}([=[
${code}
]=])
`;
    }

    /**
     * Apply final encryption layer
     */
    private applyFinalEncryption(code: string): string {
        const key = Math.floor(Math.random() * 255) + 1;
        const encrypted = Buffer.from(code).map(byte => byte ^ key);
        const encryptedString = encrypted.toString('base64');
        
        return `
local function __decrypt_final(data, key)
    local result = {}
    local decoded = ""
    
    -- Base64 decode
    local b64chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
    local padded = data:gsub("[^" .. b64chars .. "=]", "")
    
    for i = 1, #padded, 4 do
        local a, b, c, d = padded:byte(i, i + 3)
        local v = (b64chars:find(string.char(a)) - 1) * 262144 +
                  (b64chars:find(string.char(b)) - 1) * 4096 +
                  (b64chars:find(string.char(c)) - 1) * 64 +
                  (b64chars:find(string.char(d)) - 1)
        
        decoded = decoded .. string.char(math.floor(v / 65536))
        if c ~= 61 then decoded = decoded .. string.char(math.floor(v / 256) % 256) end
        if d ~= 61 then decoded = decoded .. string.char(v % 256) end
    end
    
    -- XOR decrypt
    local decrypted = ""
    for i = 1, #decoded do
        decrypted = decrypted .. string.char(decoded:byte(i) ~ key)
    end
    
    return decrypted
end

return load(__decrypt_final("${encryptedString}", ${key}))()
`;
    }

    /**
     * Utility functions
     */
    private splitString(str: string, parts: number): string[] {
        const result = [];
        const chunkSize = Math.ceil(str.length / parts);
        
        for (let i = 0; i < str.length; i += chunkSize) {
            result.push(str.slice(i, i + chunkSize));
        }
        
        return result;
    }

    private encryptString(str: string): string {
        const key = Math.floor(Math.random() * 255) + 1;
        const encrypted = Buffer.from(str).map(byte => byte ^ key);
        return encrypted.toString('base64') + ':' + key;
    }

    private generateRandomName(): string {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        let result = '';
        for (let i = 0; i < 8 + Math.floor(Math.random() * 8); i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return '__' + result;
    }
}

export default EnhancedObfuscator;
