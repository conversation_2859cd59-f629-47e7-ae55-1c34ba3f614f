{"name": "prometheus-discord-bot", "version": "0.2.0", "description": "The Discord Bo<PERSON> for the Prometheus Obfuscator", "main": "index.js", "scripts": {"build": "node esbuild.js", "start": "node index.js", "start:api": "node api.js", "dev": "npm run build && npm run start", "dev:api": "npm run build && npm run start:api", "test": "node test_api.js", "setup": "npm install && npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/levno-710/Prometheus-dicord-bot.git"}, "keywords": ["lua", "obfuscator"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/levno-710/Prometheus-dicord-bot/issues"}, "homepage": "https://github.com/levno-710/Prometheus-dicord-bot#readme", "devDependencies": {"@types/node": "^17.0.31", "@types/express": "^4.17.17", "@types/multer": "^1.4.7", "@types/cors": "^2.8.13", "@typescript-eslint/eslint-plugin": "^5.22.0", "@typescript-eslint/parser": "^5.22.0", "esbuild": "^0.14.38", "esbuild-node-externals": "^1.4.1", "eslint": "^8.14.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.26.0", "typescript": "^4.6.4"}, "dependencies": {"@colors/colors": "^1.5.0", "@types/tmp": "^0.2.3", "@types/uuid": "^8.3.4", "axios": "^0.27.2", "discord.js": "^13.6.0", "dotenv": "^16.0.0", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "tmp": "^0.2.1", "uuid": "^8.3.2"}}