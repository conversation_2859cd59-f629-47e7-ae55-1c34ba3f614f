# 📦 Prometheus Obfuscator API Package

## 🎉 What You're Getting

A complete **Lua obfuscation system** for your Discord bot! This package transforms the original Prometheus Discord bot into a powerful REST API that any Python bot can use.

## 📁 Package Contents

### 🚀 **Quick Setup Files**
- `QUICK_INSTALL.md` - 3-step installation guide
- `start_api.bat` - Windows one-click startup
- `start_api.sh` - Linux/Mac one-click startup
- `docker-compose.yml` - Docker deployment

### 🌐 **API Server** (Node.js/Express)
- `src/api.ts` - Main HTTP API server
- `src/obfuscate.ts` - Obfuscation logic
- `src/logger.ts` - Logging utilities
- `package.json` - Dependencies and scripts
- `esbuild.js` - Build configuration

### 🐍 **Python Integration**
- `bot_integration_simple.py` - **Copy this into your bot!**
- `discord_bot_integration.py` - Complete Discord bot example
- `python_client_example.py` - Full client library
- `requirements.txt` - Python dependencies

### 🧪 **Testing & Validation**
- `test_obfuscation_direct.py` - Core functionality test
- `test_api_curl.bat` - API endpoint testing
- `test_summary.md` - Test results (all passed!)
- Sample obfuscated files

### 📚 **Documentation**
- `README.md` - Complete project overview
- `SETUP_GUIDE.md` - Detailed setup instructions
- `API_README.md` - API documentation
- `QUICK_INSTALL.md` - Fast setup guide

### 🔧 **Core Obfuscation Engine**
- `lua/` - Prometheus obfuscator scripts
- `bin/` - LuaJIT runtime
- All original obfuscation presets

## ✨ **Features You Get**

### 🔒 **4 Obfuscation Levels**
- **Minify**: Basic compression (8x size increase)
- **Weak**: Light obfuscation (8x size increase)
- **Medium**: String encryption + VM (57x size increase)
- **Strong**: Multiple VM layers (101x size increase)

### 🌐 **REST API Endpoints**
- `GET /health` - Check server status
- `GET /presets` - Available obfuscation levels
- `POST /obfuscate` - Upload .lua files
- `POST /obfuscate-text` - Send code directly

### 🤖 **Discord Bot Commands**
- `!obfuscate [preset]` - Obfuscate uploaded files
- `!presets` - Show available options
- `!obf_status` - Check API connectivity

### 🛡️ **Security Features**
- 40KB file size limit
- Input validation
- Error handling
- Timeout protection

## 🚀 **Installation (3 Steps)**

### 1. Start API Server
```bash
# Extract zip, then:
start_api.bat  # Windows
# or
./start_api.sh  # Linux/Mac
# or
docker-compose up prometheus-api  # Docker
```

### 2. Install Python Dependencies
```bash
pip install requests discord.py aiohttp
```

### 3. Add to Your Bot
Copy the code from `bot_integration_simple.py` into your Discord bot!

## 🎯 **What Your Users Get**

- **Professional obfuscation** with 4 security levels
- **Easy file upload** - just attach .lua files
- **Instant results** - obfuscated files sent back immediately
- **Beautiful Discord embeds** with status updates
- **Error handling** with helpful messages

## 📊 **Tested & Verified**

✅ **Core obfuscation**: All presets working perfectly  
✅ **API endpoints**: All responding correctly  
✅ **File uploads**: Working with size validation  
✅ **Error handling**: Graceful failure management  
✅ **Docker deployment**: Container ready  
✅ **Python integration**: Discord bot commands tested  

## 🌐 **Deployment Options**

- **Local development**: Perfect for testing
- **VPS/Server**: Production deployment ready
- **Docker**: One-command container deployment
- **Cloud platforms**: Heroku, Railway, DigitalOcean ready

## 💡 **Why This Package?**

1. **Plug & Play**: Copy one file into your bot
2. **Professional Grade**: Enterprise-level obfuscation
3. **Scalable**: REST API handles multiple bots
4. **Documented**: Complete setup guides included
5. **Tested**: All functionality verified working
6. **Flexible**: Multiple deployment options

## 🎉 **Result**

Your Discord bot becomes a **professional Lua obfuscation service** that can:
- Protect user scripts with military-grade obfuscation
- Handle file uploads seamlessly
- Provide multiple security levels
- Scale to handle many users
- Deploy anywhere (local, VPS, cloud)

**Transform your bot into a premium obfuscation service in minutes!** 🚀
