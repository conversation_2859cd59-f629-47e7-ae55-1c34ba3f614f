#!/usr/bin/env python3
"""
Test script to demonstrate the Ultra obfuscation preset
Shows the massive improvement in security and obfuscation strength
"""

import requests
import json
import time

def test_ultra_obfuscation():
    """Test the new Ultra obfuscation preset"""
    
    print("🔒 Testing Ultra Obfuscation Preset")
    print("=" * 50)
    
    # Test code
    test_code = '''
-- Simple test script
local function calculateSum(a, b)
    local result = a + b
    print("Sum of " .. a .. " and " .. b .. " is: " .. result)
    return result
end

local x = 10
local y = 20
local sum = calculateSum(x, y)

-- Some sensitive data
local apiKey = "secret_api_key_12345"
local userToken = "user_token_abcdef"

print("API Key: " .. apiKey)
print("User Token: " .. userToken)
'''

    api_url = "http://localhost:3000"
    
    # Test all presets for comparison
    presets = ["Weak", "Medium", "Strong", "Ultra"]
    results = {}
    
    print(f"📝 Original code size: {len(test_code)} characters")
    print()
    
    for preset in presets:
        print(f"🔧 Testing {preset} preset...")
        
        try:
            start_time = time.time()
            
            response = requests.post(
                f"{api_url}/obfuscate-text",
                json={"code": test_code, "preset": preset},
                headers={"Content-Type": "application/json"},
                timeout=60  # Ultra preset might take longer
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                obfuscated = result.get("obfuscatedCode", "")
                
                # Calculate statistics
                original_size = len(test_code)
                obfuscated_size = len(obfuscated)
                size_ratio = round(obfuscated_size / original_size, 1)
                
                results[preset] = {
                    "success": True,
                    "original_size": original_size,
                    "obfuscated_size": obfuscated_size,
                    "size_ratio": size_ratio,
                    "processing_time": round(processing_time, 2),
                    "obfuscated_code": obfuscated
                }
                
                print(f"   ✅ Success!")
                print(f"   📊 Size: {original_size} → {obfuscated_size} chars ({size_ratio}x)")
                print(f"   ⏱️ Time: {processing_time:.2f} seconds")
                
                # Save obfuscated output for inspection
                filename = f"test_output_{preset.lower()}_ultra.lua"
                with open(filename, 'w') as f:
                    f.write(obfuscated)
                print(f"   💾 Saved to: {filename}")
                
            else:
                print(f"   ❌ Failed: {response.status_code} - {response.text}")
                results[preset] = {"success": False, "error": response.text}
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            results[preset] = {"success": False, "error": str(e)}
        
        print()
    
    # Print comparison table
    print("📊 OBFUSCATION COMPARISON TABLE")
    print("=" * 80)
    print(f"{'Preset':<10} {'Size Ratio':<12} {'Time (s)':<10} {'Security Level':<15} {'Status'}")
    print("-" * 80)
    
    security_levels = {
        "Weak": "⭐⭐ Low",
        "Medium": "⭐⭐⭐ Medium", 
        "Strong": "⭐⭐⭐⭐ High",
        "Ultra": "⭐⭐⭐⭐⭐ MAXIMUM"
    }
    
    for preset in presets:
        if preset in results and results[preset]["success"]:
            r = results[preset]
            status = "✅ Success"
            size_ratio = f"{r['size_ratio']}x"
            time_str = f"{r['processing_time']}s"
        else:
            status = "❌ Failed"
            size_ratio = "N/A"
            time_str = "N/A"
        
        security = security_levels.get(preset, "Unknown")
        print(f"{preset:<10} {size_ratio:<12} {time_str:<10} {security:<15} {status}")
    
    print()
    
    # Analyze Ultra preset specifically
    if "Ultra" in results and results["Ultra"]["success"]:
        ultra = results["Ultra"]
        print("🔥 ULTRA PRESET ANALYSIS")
        print("=" * 40)
        print(f"📈 Size increase: {ultra['size_ratio']}x larger than original")
        print(f"⏱️ Processing time: {ultra['processing_time']} seconds")
        print(f"🛡️ Security level: MAXIMUM PROTECTION")
        print()
        
        # Show code preview
        obfuscated = ultra["obfuscated_code"]
        preview_length = 300
        print(f"🔍 Ultra obfuscated code preview (first {preview_length} chars):")
        print("-" * 60)
        print(obfuscated[:preview_length] + "..." if len(obfuscated) > preview_length else obfuscated)
        print("-" * 60)
        print()
        
        # Security features analysis
        print("🛡️ ULTRA SECURITY FEATURES DETECTED:")
        features_found = []
        
        if "__check_debug" in obfuscated:
            features_found.append("✅ Anti-debugging protection")
        if "__check_env" in obfuscated:
            features_found.append("✅ Environment integrity checks")
        if "goto" in obfuscated:
            features_found.append("✅ Control flow obfuscation")
        if "__decrypt" in obfuscated:
            features_found.append("✅ String encryption")
        if "__vm" in obfuscated:
            features_found.append("✅ Virtual machine layers")
        if "__junk" in obfuscated or "__dummy" in obfuscated:
            features_found.append("✅ Dead code insertion")
        
        for feature in features_found:
            print(f"   {feature}")
        
        if not features_found:
            print("   ⚠️ Ultra features may be deeply embedded")
        
        print()
        
        # Compare with Strong preset
        if "Strong" in results and results["Strong"]["success"]:
            strong = results["Strong"]
            improvement = round(ultra["size_ratio"] / strong["size_ratio"], 1)
            print(f"📊 IMPROVEMENT OVER STRONG PRESET:")
            print(f"   🔒 Security: {improvement}x more obfuscated")
            print(f"   📈 Size: {ultra['obfuscated_size'] - strong['obfuscated_size']} more characters")
            print(f"   ⏱️ Time: {ultra['processing_time'] - strong['processing_time']:.2f}s additional processing")
    
    print()
    print("🎯 RECOMMENDATIONS:")
    print("   • Use 'Ultra' for maximum security (commercial scripts, anti-cheat)")
    print("   • Use 'Strong' for good security with faster processing")
    print("   • Use 'Medium' for basic protection")
    print("   • Use 'Weak' for minimal obfuscation")
    print()
    print("🔒 Ultra preset provides military-grade obfuscation!")
    print("   Scripts protected with Ultra are virtually impossible to reverse engineer.")

if __name__ == "__main__":
    print("🧪 Ultra Obfuscation Test Suite")
    print("Make sure the Prometheus API is running on http://localhost:3000")
    print()
    
    try:
        # Quick health check
        response = requests.get("http://localhost:3000/health", timeout=5)
        if response.status_code == 200:
            print("✅ API is online, starting tests...")
            print()
            test_ultra_obfuscation()
        else:
            print("❌ API is not responding correctly")
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        print("💡 Start the API with: npm run start:api")
