# 🚀 Prometheus Obfuscator API

Transform your Discord bot into a powerful Lua obfuscation service! This package converts the Prometheus Discord bot into a REST API that any Python bot can use.

## ✨ Features

- 🔧 **4 Obfuscation Levels**: Minify, Weak, Medium, Strong
- 🌐 **REST API**: Easy HTTP integration
- 🐍 **Python Client**: Ready-to-use Discord bot integration
- 🐳 **Docker Support**: One-command deployment
- 📁 **File Upload**: Support for .lua file uploads
- 💬 **Direct Input**: Send code directly via API
- 🛡️ **Security**: 40KB file size limit, input validation

## 🎯 Quick Start

### 1. Start the API Server

**Option A: Node.js**
```bash
npm run setup    # Install dependencies and build
npm run start:api
```

**Option B: Docker (Recommended)**
```bash
docker-compose up prometheus-api
```

**Option C: One-click scripts**
```bash
# Windows
start_api.bat

# Linux/Mac
./start_api.sh
```

### 2. Add to Your Discord Bot

```bash
pip install -r requirements.txt
```

Copy the code from `bot_integration_simple.py` into your Discord bot:

```python
# Add these commands to your bot
@bot.command()
async def obfuscate(ctx, preset="Medium"):
    # Full implementation in bot_integration_simple.py
    pass

@bot.command()
async def presets(ctx):
    # Show available obfuscation presets
    pass
```

### 3. Test It!

- Upload a .lua file and use `!obfuscate Medium`
- Check status with `!obf_status`
- See presets with `!presets`

## 📡 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/health` | Check API status |
| GET | `/presets` | Get available presets |
| POST | `/obfuscate` | Upload .lua file |
| POST | `/obfuscate-text` | Send code directly |

### Example Usage

```bash
# Health check
curl http://localhost:3000/health

# Obfuscate code
curl -X POST http://localhost:3000/obfuscate-text \
  -H "Content-Type: application/json" \
  -d '{"code":"print(\"Hello\")", "preset":"Medium"}'
```

## 🔧 Obfuscation Presets

| Preset | Description | Use Case |
|--------|-------------|----------|
| **Minify** | Basic minification | Reduce file size |
| **Weak** | Light obfuscation | Basic protection |
| **Medium** | String encryption + VM | Moderate security |
| **Strong** | Multiple VM layers | Maximum protection |

## 📁 Files Included

```
📦 prometheus-obfuscator-api/
├── 🚀 Quick Setup
│   ├── start_api.bat/sh           # One-click startup
│   ├── docker-compose.yml         # Docker deployment
│   └── SETUP_GUIDE.md            # Detailed instructions
├── 🌐 API Server
│   ├── src/api.ts                # Main API server
│   ├── src/obfuscate.ts          # Obfuscation logic
│   └── package.json              # Dependencies
├── 🐍 Python Integration
│   ├── bot_integration_simple.py # Copy into your bot
│   ├── discord_bot_integration.py # Complete example
│   ├── python_client_example.py  # Client library
│   └── requirements.txt          # Python dependencies
├── 🧪 Testing
│   ├── test_obfuscation_direct.py # Test core functionality
│   ├── test_api_curl.bat         # API testing
│   └── test_summary.md           # Test results
└── 📚 Documentation
    ├── README.md                 # This file
    ├── API_README.md             # API documentation
    └── SETUP_GUIDE.md            # Setup instructions
```

## 🐳 Docker Deployment

```bash
# Start API only
docker-compose up prometheus-api

# Start with Discord bot
docker-compose --profile discord up

# Background mode
docker-compose up -d prometheus-api
```

## 🌐 Production Deployment

### VPS/Server
```bash
# Install Node.js 16+
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# Deploy
git clone <your-repo>
cd prometheus-obfuscator-api
npm run setup
npm run start:api

# Use PM2 for production
npm install -g pm2
pm2 start api.js --name "prometheus-api"
pm2 startup
pm2 save
```

### Cloud Platforms
- **Heroku**: Use included `Dockerfile`
- **Railway**: Direct GitHub deployment
- **DigitalOcean**: Docker deployment
- **AWS/GCP**: Container services

## 🔧 Configuration

### Environment Variables
```env
API_PORT=3000                    # API server port
DISCORD_TOKEN=your_token_here    # For Discord bot mode
```

### API Settings
- **Port**: 3000 (configurable)
- **File limit**: 40KB
- **Timeout**: 30 seconds
- **CORS**: Enabled

## 🧪 Testing

### Test Core Functionality
```bash
python test_obfuscation_direct.py
```

### Test API Server
```bash
npm run test
# or
test_api_curl.bat
```

### Test Discord Integration
1. Start API server
2. Add commands to your bot
3. Upload a .lua file with `!obfuscate`

## 🚨 Troubleshooting

### API Won't Start
- ✅ Check Node.js version (16+)
- ✅ Run `npm install`
- ✅ Check port 3000 availability
- ✅ Check firewall settings

### Bot Can't Connect
- ✅ Verify API is running: `curl http://localhost:3000/health`
- ✅ Check API URL in bot code
- ✅ Install Python dependencies: `pip install -r requirements.txt`

### Docker Issues
```bash
docker-compose down
docker-compose build --no-cache
docker-compose up
```

## 📊 Performance

- **Weak preset**: ~8x size increase, fast processing
- **Medium preset**: ~57x size increase, moderate processing
- **Strong preset**: ~101x size increase, slower processing
- **File limit**: 40KB (configurable)
- **Concurrent requests**: Supported

## 🔒 Security Features

- Input validation and sanitization
- File size limits
- Timeout protection
- Error handling
- CORS configuration

## 📞 Support

1. Check `SETUP_GUIDE.md` for detailed instructions
2. Review `test_summary.md` for test results
3. Use `!obf_status` command to check API connectivity
4. Check logs: `docker-compose logs prometheus-api`

## 📄 License

ISC License - See original Prometheus project for details.

---

**🎉 Your Discord bot is now ready to obfuscate Lua files with enterprise-grade security!**

Made with ❤️ for the Discord bot community.
