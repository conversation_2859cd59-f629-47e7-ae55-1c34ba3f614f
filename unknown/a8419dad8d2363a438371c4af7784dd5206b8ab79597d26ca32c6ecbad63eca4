prometheus_config.py"""
🔧 Prometheus Obfuscator API Configuration
Easy configuration file for your CHETOS LB Premium Bot

Update these settings when you deploy your Prometheus API server.
"""

# ============================================================================
# PROMETHEUS API CONFIGURATION
# ============================================================================

# 🚀 API Server URL - UPDATE THIS WHEN YOU DEPLOY YOUR API
# Examples:
# - Local development: "http://localhost:3000"
# - VPS/Server: "http://your-server-ip:3000" 
# - Domain: "https://api.yourdomain.com"
# - Heroku: "https://your-app-name.herokuapp.com"
# - Railway: "https://your-app-name.up.railway.app"
# - DigitalOcean: "https://your-droplet-ip:3000"
PROMETHEUS_API_URL = "http://localhost:3000"  # 🔧 CHANGE THIS

# Enable/Disable Prometheus API
# True = Use Prometheus API (recommended)
# False = Use built-in obfuscation only
PROMETHEUS_API_ENABLED = True

# Default obfuscation preset for all scripts
# Options: "Minify", "Weak", "Medium", "Strong", "Ultra"
# - Minify: Basic compression (~1x size, very fast)
# - Weak: Light obfuscation (~8x size, fast)
# - Medium: String encryption + VM (~57x size, moderate)
# - Strong: Multiple VM layers (~101x size, slower but maximum security)
# - Ultra: Enhanced Strong + Anti-debug + Control flow + Dead code (~300x+ size, maximum protection)
PROMETHEUS_DEFAULT_PRESET = "Ultra"

# ============================================================================
# ENHANCED OBFUSCATION SETTINGS
# ============================================================================

# Enable enhanced obfuscation features (requires custom Prometheus build)
ENHANCED_OBFUSCATION_ENABLED = True

# Additional obfuscation layers to apply
ENHANCED_FEATURES = {
    "anti_debug": True,          # Anti-debugging protection
    "control_flow": True,        # Advanced control flow obfuscation
    "dead_code": True,           # Dead code insertion
    "string_splitting": True,    # Split strings into multiple parts
    "opaque_predicates": True,   # Add fake conditional branches
    "vm_nesting": 3,            # Number of VM layers (1-5)
    "constant_folding": True,    # Fold constants into expressions
    "junk_insertion": True,      # Insert junk code
}

# Custom obfuscation strength (1-10, 10 = maximum)
OBFUSCATION_STRENGTH = 9

# API timeout settings (in seconds)
PROMETHEUS_API_TIMEOUT = 30

# Fallback behavior when API is unavailable
# True = Use built-in obfuscation as fallback
# False = Return error if API is unavailable
PROMETHEUS_FALLBACK_ENABLED = True

# ============================================================================
# DEPLOYMENT QUICK CONFIGS
# ============================================================================

# Uncomment one of these sections for quick deployment setup:

# 🏠 LOCAL DEVELOPMENT
# PROMETHEUS_API_URL = "http://localhost:3000"
# PROMETHEUS_API_ENABLED = True

# 🌐 VPS/SERVER DEPLOYMENT
# PROMETHEUS_API_URL = "http://YOUR_SERVER_IP:3000"  # Replace YOUR_SERVER_IP
# PROMETHEUS_API_ENABLED = True

# ☁️ HEROKU DEPLOYMENT
# PROMETHEUS_API_URL = "https://YOUR_APP_NAME.herokuapp.com"  # Replace YOUR_APP_NAME
# PROMETHEUS_API_ENABLED = True

# 🚂 RAILWAY DEPLOYMENT
# PROMETHEUS_API_URL = "https://YOUR_APP_NAME.up.railway.app"  # Replace YOUR_APP_NAME
# PROMETHEUS_API_ENABLED = True

# 🐳 DOCKER DEPLOYMENT
# PROMETHEUS_API_URL = "http://prometheus-api:3000"  # If using docker-compose
# PROMETHEUS_API_ENABLED = True

# ============================================================================
# ADVANCED SETTINGS
# ============================================================================

# Retry settings for API calls
PROMETHEUS_MAX_RETRIES = 3
PROMETHEUS_RETRY_DELAY = 1  # seconds

# Logging settings
PROMETHEUS_LOG_API_CALLS = True
PROMETHEUS_LOG_ERRORS = True

# Performance settings
PROMETHEUS_CACHE_PRESETS = True  # Cache available presets
PROMETHEUS_CACHE_DURATION = 300  # 5 minutes

# ============================================================================
# USAGE INSTRUCTIONS
# ============================================================================

"""
🚀 QUICK SETUP GUIDE:

1. Deploy your Prometheus API server:
   - Extract prometheus-obfuscator-api.zip
   - Run: npm install && npm run build && npm run start:api
   - Or use Docker: docker-compose up prometheus-api

2. Update this file:
   - Change PROMETHEUS_API_URL to your deployed API URL
   - Set PROMETHEUS_API_ENABLED = True
   - Choose your preferred PROMETHEUS_DEFAULT_PRESET

3. Import in your bot:
   - Add: from prometheus_config import *
   - Use the settings in your Config class

4. Test the setup:
   - Use /prometheus-status command in Discord
   - Use /prometheus-test command (admin only)
   - Generate a script to see enhanced obfuscation

🎯 Your bot will automatically use the best available obfuscation!
"""
