#!/usr/bin/env python3
"""
Direct test of the obfuscation functionality without the API server.
This tests the core Lua obfuscation by calling the Lua scripts directly.
"""

import subprocess
import tempfile
import os
import sys

def test_lua_obfuscation():
    """Test the Lua obfuscation directly using the CLI"""
    
    print("🧪 Testing Prometheus Obfuscator directly...")
    
    # Test Lua code
    test_code = """
-- Test Lua script
local function greet(name)
    print("Hello, " .. name .. "!")
    return "Greeting sent to " .. name
end

local message = greet("World")
print(message)

-- Some variables to obfuscate
local secret = "This is a secret message"
local numbers = {1, 2, 3, 4, 5}

for i, num in ipairs(numbers) do
    print("Number " .. i .. ": " .. num)
end
"""
    
    # Create temporary input file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.lua', delete=False) as input_file:
        input_file.write(test_code)
        input_file_path = input_file.name
    
    try:
        # Test different presets
        presets = ['Weak', 'Medium', 'Strong']
        
        for preset in presets:
            print(f"\n🔧 Testing {preset} preset...")
            
            # Create temporary output file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.lua', delete=False) as output_file:
                output_file_path = output_file.name
            
            try:
                # Check if lua executable exists
                lua_path = os.path.join('bin', 'luajit.exe')
                if not os.path.exists(lua_path):
                    print(f"❌ Lua executable not found at {lua_path}")
                    continue
                
                # Run the obfuscation
                cmd = [
                    lua_path,
                    'lua/cli.lua',
                    '--LuaU',
                    '--preset', preset,
                    input_file_path,
                    '--out', output_file_path
                ]
                
                print(f"   Running: {' '.join(cmd)}")
                
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    cwd=os.getcwd()
                )
                
                if result.returncode == 0:
                    # Read the obfuscated output
                    with open(output_file_path, 'r') as f:
                        obfuscated_code = f.read()
                    
                    print(f"   ✅ {preset} obfuscation successful!")
                    print(f"   📊 Original size: {len(test_code)} characters")
                    print(f"   📊 Obfuscated size: {len(obfuscated_code)} characters")
                    print(f"   📄 Preview (first 200 chars):")
                    print(f"   {obfuscated_code[:200]}...")
                    
                    # Save full output for inspection
                    output_filename = f"test_output_{preset.lower()}.lua"
                    with open(output_filename, 'w') as f:
                        f.write(obfuscated_code)
                    print(f"   💾 Full output saved to: {output_filename}")
                    
                else:
                    print(f"   ❌ {preset} obfuscation failed!")
                    print(f"   Error: {result.stderr}")
                    
            except Exception as e:
                print(f"   ❌ Error running {preset} obfuscation: {e}")
            
            finally:
                # Clean up output file
                if os.path.exists(output_file_path):
                    os.unlink(output_file_path)
    
    finally:
        # Clean up input file
        if os.path.exists(input_file_path):
            os.unlink(input_file_path)

def test_presets_file():
    """Test if the presets file is accessible"""
    
    print("\n📋 Testing presets configuration...")
    
    presets_path = os.path.join('lua', 'presets.lua')
    if os.path.exists(presets_path):
        print(f"   ✅ Presets file found: {presets_path}")
        
        # Try to read and parse basic info
        with open(presets_path, 'r') as f:
            content = f.read()
            
        # Count presets
        preset_names = []
        for line in content.split('\n'):
            if '"] = {' in line and '[' in line:
                preset_name = line.split('[')[1].split(']')[0].strip('"')
                preset_names.append(preset_name)
        
        print(f"   📊 Found presets: {', '.join(preset_names)}")
        
    else:
        print(f"   ❌ Presets file not found: {presets_path}")

def test_lua_executable():
    """Test if the Lua executable works"""
    
    print("\n🔍 Testing Lua executable...")
    
    lua_path = os.path.join('bin', 'luajit.exe')
    if os.path.exists(lua_path):
        print(f"   ✅ Lua executable found: {lua_path}")
        
        try:
            # Test basic Lua execution
            result = subprocess.run(
                [lua_path, '-v'],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print(f"   ✅ Lua executable works!")
                print(f"   📊 Version info: {result.stdout.strip()}")
            else:
                print(f"   ❌ Lua executable failed: {result.stderr}")
                
        except Exception as e:
            print(f"   ❌ Error testing Lua executable: {e}")
    else:
        print(f"   ❌ Lua executable not found: {lua_path}")

def main():
    """Run all tests"""
    
    print("🚀 Prometheus Obfuscator Direct Test")
    print("=" * 50)
    
    # Change to the correct directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"📁 Working directory: {os.getcwd()}")
    
    # Run tests
    test_lua_executable()
    test_presets_file()
    test_lua_obfuscation()
    
    print("\n🎯 Test Summary:")
    print("   - If obfuscation worked, the core functionality is ready")
    print("   - You can now build the API server to expose this via HTTP")
    print("   - Use the Python client to connect your Discord bot")

if __name__ == "__main__":
    main()
