return(function(...)local w={"inWydGH=";"AUIKdGi1","z7eeAw==","ETN5EV03inH=";"zB19iq==";"ITuKATIyHrg1ETILETIsHq==";"YV1eYneeMLu8+Tb4Y9z=";"dDH=","3/vgsZ+W6c==","","n0N6dUg1Fc==","z7N5z7u9","n0NKingeETu3dT8=";"ATMedT2=";"gTe3iUrjVLIeY7unVw==","dTI5";"A7I9dVI9zngezUb1";"AGgyQVjB","+hr=";"FaMVMrgRgUjDqUIbi8l=","iGM0zw==","M7iDgreMiDWDzV1qZc==";"dVu9Qc==","AUu5iTNK","AaW6dBq=","n0NBz2==","iUbXdGH=","QGfeC7KYA0MlILWv","88C9MBf2dIqGQsI8","Qw==";"gG15gVjniv18FuuYVLA=";"NcTf+jtSKXCWPbt3YJ7+YdZFzFlJVi2h","ETu3dT8=","qBC7iGclgVWliDM1Yw==";"X/HcFiQZ62==","+3w1imk6+w==","EVj2zVMh","dDpLa5zyZsvxcJKo510Qflc=";"ITMXg2==";"ETNvEaW6dUA=";"+w==","QnfeQnWv","i70eETMk","dDr=";"eySaLzs=","iDEKivq=","i0utCDg/Y0WYiUwGI2==","M9NuYrj6Znun","n0N4iVl=","zIHlQ70/ZUIZCU0v","z1eeIveDE9i3Mr6m+q==";"gDuZZ1El8TE7IuC2q0z=","is0g8sN8qUKk+aW/ic=="}local function v(v)return w[v-((338408-555159)-(-220050))]end for v,Q in ipairs({{497796+-497795;632407+-632354},{((-201402+690810)+445606)-935013;-826497+((525423+534002)-232887)},{40576-(-890072-(-930606)),-1038904+1038957}})do while Q[-820285+820286]<Q[335831+-335829]do w[Q[-653990+653991]],w[Q[457299+-457297]],Q[787175+-787174],Q[(127145-(-253712))-380855]=w[Q[759655-759653]],w[Q[(-163237-295448)+(82865+375821)]],Q[-924479+924480]+((716737+(-1211752-(-369616)))-(-125400)),Q[(-398232+358349)-(-39885)]-((632045+-905990)-(-273946))end end do local v=w local Q=table.insert local P=string.char local M=string.sub local R={d=770771-770744;H=-687874+687882,q=34128-(238033+(-601681-(-397760))),E=-889090-(-889119);I=773634+-773613,["9"]=-596085+596137,["0"]=-490040+490093,L=581994-581959;g=508934+-508917;["2"]=-269892+269940;R=-63578-(-63636),s=(596937-776775)+179874;Q=993183-993157;C=168788+-168776,a=-922328-(-922335);F=(-311791+-611532)+(1412701-489348);z=-584655-(-584679),D=((-1531084-(-624308))+110542)+796237;u=-835075+835080,["/"]=876730-876688,["1"]=381429+-381392,e=814831+(-402043+((394553+((1630778-747388)+-1390833))-299865));x=(-586979+105786)+481204,J=-289664-(-289726),n=-36963+36986;j=6406-6349;Z=81432-81413;l=-754090+754146,b=(-957384+1519555)-562122,T=716173-716167,U=5448-5410,X=-83324+83371,k=581870+-581830;m=-362733-(-362735),["+"]=-744385+744399,["6"]=737029-736988;["4"]=338197+(-859762+521609),M=-379005-((408177+49370)-(160502-(-676063)));V=-964046-(-964068),v=-75264-(-75315);i=165296-(-304025-(-469296)),r=926252+-926248,["3"]=((768881-909777)-(-242567))-101637,t=-721795+721810;W=886208+((-604611-175331)+-106257),K=-868466-(-868511),P=(561100+-509346)-(905234+((-1450017-(-497646))+(-609728-(-708588)))),c=-185636-(-185636),["7"]=-322495-(-322549);o=288152+-288092,w=-123799+123831,["8"]=(((706786+(327102-1008809))+(784046-462561))-44908)-(-822903+1124539),y=-373200+((960523-(708985+(-963916-(-545382))))-(36756-(-260066)));O=-1013113-(-1013176),A=(183550-570148)+(-955263+1341889),Y=600760-(((-240959+1338306)-(-44088))-540693);f=-793372-(-793373),B=818296+-818257;p=((806648+-930919)-693797)+818127;N=-426718-(-426779);G=(-956059+514205)-(-441909),S=(-551722+(730696+190978))-(979356-609414);["5"]=-797116-((-344716+-319430)+-133016),h=-829715-(-829758)}local r=math.floor local D=table.concat local k=string.len local O=type for w=549426+-549425,#v,-320811+320812 do local y=v[w]if O(y)=="string"then local O=k(y)local h={}local i=438498-438497 local b=103169+((-1131460-(-435475))-(-592816))local p=-376509-(-376509)while i<=O do local w=M(y,i,i)local v=R[w]if v then b=b+v*(566047+(-870102-(-990359+(852527-166287))))^((297256+-297253)-p)p=p+(-783966+783967)if p==-68755-(-68759)then p=798135-798135 local w=r(b/(((((323387-(-198736))+(524315+-821224))+-160062)+-670173)-(-670557)))local v=r((b%(925162+-859626))/(148434+(459531-607709)))local M=b%(-927481+927737)Q(h,P(w,v,M))b=-810129+(-856894+1667023)end elseif w=="="then Q(h,P(r(b/(962094-896558))))if i>=O or M(y,i+(-360393+360394),i+(161320-161319))~="="then Q(h,P(r((b%(-346148-(-562766-(-151082))))/((-1253322-(-504700))-(-748878)))))end break end i=i+(691101-691100)end v[w]=D(h)end end end return(function(w,P,M,R,r,D,k,b,c,y,q,T,l,p,O,G,S,i,W,Q,h)i,c,W,S,O,p,l,G,Q,T,y,b,q,h=660514+-660514,function(w,v)local P=b(v)local M=function(M,R,r,D,k,O)return Q(w,{M;R,r;D,k,O},v,P)end return M end,function(w)y[w]=y[w]-(-158064-(-158065))if(692596-207390)-485206==y[w]then y[w],O[w]=nil,nil end end,function(w,v)local P=b(v)local M=function()return Q(w,{},v,P)end return M end,{},function(w)local v,Q=(343864+(-499128-(-912544)))+-757279,w[((-365604-5089)+((538083+(-310322+-553693))+274217))+422409]while Q do y[Q],v=y[Q]-(1026763-1026762),(295428+-295427)+v if 488087+-488087==y[Q]then y[Q],O[Q]=nil,nil end Q=w[v]end end,function(w,v)local P=b(v)local M=function(...)return Q(w,{...},v,P)end return M end,function(w,v)local P=b(v)local M=function(M,R,r)return Q(w,{M,R;r},v,P)end return M end,function(Q,M,R,r)local i,V,o,Z,z,Y,x,E,U,e,X,H,F,l,L,s,j,K,N,f,B,n,d,a,u,m,g,J,y,p,A,t,k,b while Q do if Q<9450276-(-274357+1222852)then if Q<3969360-(-963806)then if Q<2125855-(-267008)then if Q<-136179-(-986416)then if Q<(((((63348+(-1325789-(-809848)))-(-836118))+1804391)-1046464)-901195)+436646 then if Q<501691-(256145-160506)then if Q<123304-(-74441)then e=g==K m=e Q=-72706+8176345 else i=O[R[548476-548474]]b=O[R[178051-178048]]y=i==b k=y Q=466936+331791 end else j=-581448-(-581449)O[i]=f V=O[Y]N=V+j A=H[N]Z=g+A A=128148-127892 Q=Z%A N=O[F]A=K+N N=((117995+469240)+(-422173+-1152194))+987388 g=Q Z=A%N K=Z Q=-130297+8244851 end else if Q<162563-(1035532+-1682959)then Q=k and 12782460-227176 or 12817772-(-232444-(-1044931))else Q=true i=h()l=h()b=v(875249-871944)y=M O[i]=Q k=w[b]b=v(-256185+259515)Q=k[b]b=h()X=T((-77130+-543948)+7093852,{l})L=v(159234+-155933)p=h()O[b]=Q Q=q(8294575-920035,{})O[p]=Q Q=false O[l]=Q B=w[L]L=B(X)Q=L and((12230652-425719)-(-808996-231649))-(-1016010)or-781352+11250542 k=L end end else if Q<(-362910+(((-214136-(-526055))-(-558526))+-834207))+1920949 then if Q<(-1004249+2909365)-(1150293-(-252809-(-827143-(-71349+-369367))))then if Q<78339+805004 then Q=5869014-596893 else l=(145202-262677)-(-117477)i=O[R[-855747-(-855748)]]p=-947141-(-947142)b=i(p,l)i=(-1150102-(-1019245+874497))+1005355 y=b==i Q=y and((539827+93786)+-1402022)+1567136 or 470150+-183984 k=y end else Q=4597297-(-405890)end else if Q<2087083-238056 then V=-507815+507817 N=H[V]Q=11632580-((-267860-539167)-(-232176-298196))V=O[E]A=N==V f=A else b=O[R[88757-88751]]i=b==y k=i Q=(-616731+10806824)-(-251073)end end end else if Q<523921+3645316 then if Q<2996251-(-559822)then if Q<-363133+3382172 then if Q<3722458-920478 then b=1215490-299939 i=v(-985858-(-475405+(-821019-(-307240))))y=i^b k=(-192978+(-705382+384083))+14719995 Q=k-y k=v(591177+-587844)y=Q Q=k/y k={Q}Q=w[v(548731-545391)]else Q=10637874-(-518990)t=v(155191-(-546995+698862))o=w[t]k=o end else Q=553394+12717658 end else if Q<955130+3139001 then n=v(351603+-348272)J=v(922450+-919123)Q=w[J]z=w[n]J=Q(z)Q=v(-744711+748059)w[Q]=J Q=2056611-709518 else Q=7580+12890687 end end else if Q<-678088+5436000 then if Q<-360084+4758378 then Q={}l=(387940+524924)+(-295070+35184371471038)O[R[(-62008-(-970142))+((-839819+538330)-606643)]]=Q k=O[R[693344+-693341]]X=v(1031049+(-1354914-(-327170)))p=k k=i%l O[R[-284597+((1265918-(167618+327492))-486207)]]=k Q=-731665+12431965 L=-312030+312285 B=i%L L=35937-35935 l=B+L O[R[-602837-(-602842)]]=l L=w[X]X=v(-272832+276135)B=L[X]t=755160+(-284176-470983)L=B(y)g=t B=v(322553+-319203)b[i]=B X=564972-564971 o=L B=-581319-(-581405)t=-390488+390488 K=g<t t=X-g else F=not x s=s+n u=s<=z u=F and u F=s>=z F=x and F u=F or u F=15274760-275385 Q=u and F u=11404675-((835178+-809198)-(-130276))Q=Q or u end else if Q<3937620-(-847105)then f=O[i]k=f Q=f and 10003837-402256 or-424632+(939212+11529204)else L=nil p=nil Q=12857100-(-41167)B=nil end end end end else if Q<6847957-409544 then if Q<839+5574981 then if Q<374500+4763599 then if Q<4078617-((-462556+((827407-299482)-450387))+-525089)then if Q<-750346+5699830 then A=26044+-26043 Z=H[A]Q=-103922+562879 f=Z else Q=true Q=(13963188-(-171701))-(-432935)end else Q=true Q=Q and(-324567+((-1135461-(-1047565))+144947))+9715645 or(450241+-1097848)+((14608488-(-555741))-(-450699-427883))end else if Q<-247137+5534859 then Q=true Q=Q and-41720+6934554 or 373672+(((-619384+888699)-737698)+5445942)else Q=w[v(-775538-(-778856))]k={}end end else if Q<5296272-(-576832)then if Q<-525617+6164971 then if Q<4914585-(84086-778280)then J=v(-674946+678251)X=t e=w[J]J=v(-593125-((78416+13833)-688719))m=e[J]Q=857991+(-686198+11528507)e=m(y,X)m=O[R[-149916-(-149922)]]J=m()s=e+J u=s+B s=-689781-(-690037)X=nil d=u%s s=b[i]J=-318156+318157 B=d e=B+J m=p[e]u=s..m b[i]=u else b=b+l i=b<=p L=not B i=L and i L=b>=p L=B and L i=L or i L=15271248-(124045+(-423320+-590060))Q=i and L i=12540545-(593822+-116222)Q=Q or i end else z=-563765-(-563765)s=#d u=s==z Q=u and-66106+13253113 or(-699208-(-152354))+16831023 end else if Q<735108+5662686 then b=v((589931+198032)+-784643)i=w[b]b=v((49843+-368498)-(-321997))y=i[b]b=O[R[-557583+557584]]Q=w[v(-897625+(680538-(-448945+228519)))]i={y(b)}k={P(i)}else X=g u=v(854266+-850954)a=v(-152008-(-155330))A=v(-848846+(-474534+1326686))N=33239465827578-(-251542-78104)d=w[u]E=7913039512719-1047539 n=O[i]F=O[p]Y=F(a,E)z=n[Y]a=O[i]E=O[p]H=E(A,N)Y=a[H]F=Y..K n=X..F K=nil X=nil Q=(-672108-(-191642))+10524185 s=z..n u=d(s)end end end else if Q<(580877-(-375758))+6335049 then if Q<7017328-296579 then if Q<238508+6408589 then if Q<6140297-(-1326458-(-992853))then Q=true O[R[(-513745-(-133096))-(-380650)]]=Q k={}Q=w[v(-170550+173857)]else b=(264635+-1270539)-(-1005905)i=O[R[970805+-970802]]y=i~=b Q=y and 15296920-(136372+(-476748+-320875))or 14753364-(-1730544-(-913470))end else y=O[R[749795-749794]]k=#y y=(1444801-414054)+(-1503421-(-472674))Q=k==y Q=Q and 607303+((14498488-(-684946))-(-764782))or 98394+6076828 end else if Q<7211372-241260 then k=v(((359301-(-406535))-364017)+(910471+-1308942))y=v((-221463-533939)-(-758733))Q=w[k]k=w[y]y=v(-367752-(-371083))w[y]=Q y=v(-966224-(-1474134-(-504562)))w[y]=k y=O[R[288181+-288180]]i=y()Q=6018186-746065 else Q=true Q=Q and 10309353-561153 or(-740743+787126)+14521441 end end else if Q<7067502-(-1610776-(-883740))then if Q<8379434-987724 then k=v(35199-31858)Q=w[k]y=v((-1068933-(-160858))-(22661-934082))k=Q(y)Q=w[v((1071321-(1313990-931268))-685278)]k={}else k={}B=nil i=W(i)L=nil p=W(p)l=nil b=nil Q=w[v(-748916+((-1330678-(-443861+(-945534-(-647415))))+1340948))]end else if Q<9092539-(79178-(-905543))then O[i]=m Q=O[i]Q=Q and(-847248-(-136760))+(874018+2882812)or-902092+5873183 else n=W(n)x=W(x)Y=W(Y)E=W(E)F=W(F)Q=839502+10746426 H=nil a=W(a)end end end end end else if Q<12677223-((111567+-641050)+812035)then if Q<10385480-(-292728)then if Q<(-499650+-292741)+10416883 then if Q<-1034377+10569754 then if Q<-387182+(9703045-222737)then if Q<316339+8653299 then y=M[890794+-890793]X=-45257+(29323832369527-(-968406))k=v((100776-399678)-(-302214))o=v(-60557-(-63874))Q=w[k]L=v(-456759+460108)p=O[R[-128083-(-128084)]]l=O[R[275740+-275738]]B=l(L,X)b=p[B]B=O[R[-129578-(-129579)]]L=O[R[(-635133+-392039)+(503011+524163)]]t=29755713369189-412983 X=L(o,t)l=B[X]p=y..l i=b..p l=v(-720821-(-724146))B=26262021567691-(-930619)k=Q(i)i=O[R[480362-480361]]b=O[R[(-1283489-(-397975))+(832270+53246)]]p=b(l,B)k=i[p]Q=k..y k={Q}Q=w[v(-104194+107503)]else k=13851055-(-647753)b=16322545-700909 i=v((-356736+461214)+-101162)y=i^b Q=k-y y=Q k=v(-598198-(-601533))Q=k/y k={Q}Q=w[v(-21497-(-24835))]end else J=(29579+82887)-112465 z=933076-(687248+245822)Q=O[L]e=Q(J,z)z=v((129253-(-222410))+-348315)Q=v((((((-632126-(-14384+-212039))-236219)+991638)+1569314)-886526)+-1029156)w[Q]=e J=w[z]z=445067+-445065 Q=J>z Q=Q and 4038170-145292 or(-678346+96792)+10172282 end else if Q<10379342-(266468+511941)then z=v(-372168-(-851971-(-476455)))Q=w[z]z=v(686694-683363)w[z]=Q Q=((-80366+355371)-(432515+-854102))+650501 else Z=Q V=242543+(-843623-(-601081))N=H[V]V=false A=N==V f=A Q=A and-919273+2539724 or-726126+(12391332-(-244029))end end else if Q<-278633+10502082 then if Q<-668023+10540595 then if Q<(-302100+10483194)-451598 then y=v((-539138+95521)+446958)b=-457744+(612823-155079)Q=w[y]i=O[R[(583218-(-218237))+(-400157+-401290)]]y=Q(i,b)Q=((-177814-(-213719))+12410920)-(-130287)else Q=(4978344-553280)-(-578123)end else g,K=o(t,g)Q=g and-416113+6848442 or 7444140-(-83984)end else if Q<((10020742-(-1036856))-(-64446-(-128543)))-(397254+(-826555-(-968325)))then O[R[-521025-(-521030)]]=k y=nil Q=751633+11253652 else K=v(-843334-(-301367-545287))B=k L=v((-185814-824872)-(-1013996))k=w[L]L=v(515619+-512308)Q=k[L]X=v(305782+(605838+-908300))L=h()O[L]=Q k=w[X]X=v(99851-96499)Q=k[X]X=Q g=w[K]o=g t=Q Q=g and(610303+-843751)+16841805 or 297084+14859252 end end end else if Q<(-351463+12277015)-(-654793+830241)then if Q<265230+11242841 then if Q<380648+10824564 then if Q<11247709-262768 then e=O[i]Q=e and(-609227-(-919096))+-279479 or(461258+(182739-507707))+7967349 m=e else o=h()g=627619-(572150-(-55404))J=v(649145+-645818)u=q(-180193+(3382533-560593),{})O[o]=k t=-1017387+1017390 Q=O[L]k=Q(t,g)Q=-442506+442506 t=h()g=Q Q=-543870+543870 K=Q O[t]=k d=v((199987-(-68284))-264970)k=w[d]d={k(u)}Q={P(d)}d=Q k=443014+-443012 Q=d[k]k=v(-336793-(-340137))u=Q Q=w[k]s=O[b]e=w[J]J=e(u)e=v((142988-590783)+451118)m=s(J,e)s={m()}k=Q(P(s))s=h()Q=-81402+11667330 O[s]=k k=756299+-756298 m=O[t]e=m m=-87002+87003 J=m m=409503-409503 z=J<m m=k-J end else Q=15939813-(-344356)z=-923472-(-923472)s=#d u=s==z end else if Q<(13141493-718846)-736978 then m=m+J n=not z k=m<=e k=n and k n=m>=e n=z and n k=n or k n=793841+12193611 Q=k and n k=10735142-(-29287)Q=Q or k else d=not K t=t+g X=t<=o X=d and X d=t>=o d=K and d X=d or X d=6065356-458789 Q=X and d X=5116357-(-413598-(-692253))Q=Q or X end end else if Q<(1229607-305354)+(955523+(755800+9405219))then if Q<261024+11724445 then Q=Z k=f Q=-738013+(12263333-(-518464))else Q=O[R[455221+-455214]]Q=Q and-469067+(9315031-(-868777))or 12916840-339728 end else if Q<(-830575+11901124)-(-974624)then Q=(8558508-428707)-15247 O[i]=k else Q=O[R[-673216+673226]]i=O[R[-278005+(1262528-(47741-(-936771)))]]y[Q]=i Q=O[R[-761380+761392]]i={Q(y)}k={P(i)}Q=w[v(-893652+896954)]end end end end else if Q<16119021-(150665-(-872947))then if Q<-975040+14162866 then if Q<(13205486-71333)-(-494611+713208)then if Q<-18094+12644212 then if Q<397003+(11481283-(-685127))then k=v(-633961+(-327117-(-781774-182648)))Q=w[k]y=O[R[(-557479-314307)+871790]]o=S(((192970-982147)+10500428)-728015,{})p=v(-140663-(-143990))X=v(((-1217796-(-1170050-(-851967)))+(1172155-(-727073)))+-996214)b=w[p]L=w[X]X={L(o)}B={P(X)}L=(((771890+68157)-840008)+836667)+-836704 l=B[L]p=b(l)b=v(269244-265921)i=y(p,b)y={i()}k=Q(P(y))y=k i=O[R[240495+-240490]]k=i Q=i and 1647295-(-732176)or 10762028-320862 else Q={}b=O[R[518417+-518408]]i=100686-100685 y=Q p=b b=311831+-311830 Q=5320101-(-291774)l=b b=905755-905755 B=l<b b=i-l end else Q=w[v(-522415+525730)]k={i}end else if Q<131213+(-259499+13120674)then n=h()O[n]=m x=v(((-880447+1237234)-169529)+-183948)k=w[x]U=517126+-507126 x=v(933542+-930231)Q=k[x]F=575133-575033 x=82190+-82189 k=Q(x,F)F=-149682+149682 x=h()Y=-236179-(-236434)E=-802782-(-802783)O[x]=k Q=O[L]k=Q(F,Y)F=h()j=801813+-801813 Y=-890983+890984 O[F]=k H=(-937890-(-323450))+614442 Q=O[L]a=O[x]k=Q(Y,a)Y=h()O[Y]=k k=O[L]A=v(-760731-(-764058))a=k(E,H)k=-923463+923464 H=v(69210+-65882)Q=a==k a=h()O[a]=Q k=v((109921-(-60474))+-167072)Q=v(1034266-1030958)Z=w[A]N=O[L]Q=u[Q]V={N(j,U)}A=Z(P(V))Z=v(-592028+(-760464+1355820))f=A..Z E=H..f Q=Q(u,k,E)E=h()H=v(-855528+858829)f=G(598844+289533,{L;n;t,b;i,s,a,E;x,Y;F,o})O[E]=Q k=w[H]H={k(f)}Q={P(H)}H=Q Q=O[a]Q=Q and 3880211-(-902219)or 16206968-598742 else b=nil s=h()u={}n=h()b=q(8475674-(-271450),{i,p})L=nil O[s]=u B=nil z=T(7582971-887119,{s;o,t,l})u=h()O[u]=z g=nil z={}O[n]=z a=v((-586488+1298022)-708183)d=nil x=v(117962+(198297-312955))A=nil H=v(-587874+591174)z=w[x]E=O[n]F={}Y={[a]=E,[H]=A}X=nil x=z(F,Y)z=c(-452605+14365026,{n;s;K;o,t,u})O[i]=x s=W(s)n=W(n)u=W(u)O[p]=z Q=827723+9215996 g=-174639+(1019927+33559325204356)t=W(t)L=O[i]l=W(l)t=v(751502+-748170)K=W(K)o=W(o)X=O[p]o=X(t,g)B=L[o]l=b(B)K=-41305-(-41310)L=v(-456079+459391)g=21513958859944-501693 B=w[L]L=B(l)t=v(33658-30339)L=O[i]X=O[p]o=X(t,g)B=L[o]t=-33052-(-33055)o=-889276-(-84744-(677197-(-127337)))g=231356-(821825-590473)X=1046917+-1046916 L={X,o;t;g,K}o=v(73435+(-612268+542162))X=w[o]K={X(L)}t=K[-828813+828815]o=K[-792778+792779]g=K[-243224-(-585506-(-342279))]end end else if Q<(-394917+14580769)-24460 then if Q<(-219621+(18586-(-841295)))+13258463 then if Q<(-525928+704406)+13101472 then u=nil B=nil p=W(p)g=nil l=W(l)s=W(s)K=nil b=W(b)b=nil p=h()t=W(t)o=W(o)X=nil L=W(L)X=v(654454+-651134)i=W(i)d=nil i=nil O[p]=i B=v(-590432+593742)d={}L=v(-491009-(-494319))i=h()Q=5499130-967482 O[i]=b l=w[B]B=v(-53775+57089)b=l[B]l=h()O[l]=b o=v(-1020352+1023657)B=w[L]L=v(-180079+183390)t=h()b=B[L]L=w[X]s=-872749+873005 X=v((1015344+(-607075+(-573029-(-662251))))+-494149)B=L[X]g={}X=w[o]o=v((-266894-56178)+326415)L=X[o]u=(-757302-172414)-(645872+-1575589)K=h()z=s o=h()X=-738663-(-738663)O[o]=X X=825201+-825199 s=-520059+520060 O[t]=X X={}O[K]=g g=-813250+813250 n=s s=-944638+944638 x=n<s s=u-n else Q=10543393-(-580215-(-858759+204341))B=O[l]k=B end else Q=O[R[806088+-806087]]i=M[540302-(630956+-90656)]y=M[(323571-304103)-19467]b=Q Q=b[i]Q=Q and((4959580-572075)-(-639749))-858289 or((((1014640+-850117)-(-567718))+4061895)-(-189054))-606808 end else if Q<(801486+-1328225)+15148407 then Q=q(((-890338-(495049-761417))+1079354)-(-427708),{p})e={Q()}Q=w[v(-358620+361967)]k={P(e)}else Q=-601837+5133485 u=s F=u d[u]=F u=nil end end end else if Q<-548447+16680724 then if Q<((-18403+-381292)+-6613)+15995204 then if Q<15633144-101858 then if Q<14597570-(-1320795-(-636148))then k=o Q=t Q=o and 10752992-(-403872)or-743255+(-678995+4389005)else Q=w[v((97823+70407)+-164893)]k={}end else i=O[R[849103+-849100]]b=-869022-(-869259)Q=103476+6378146 y=i*b i=881423-(-234315+1115481)k=y%i O[R[(99191-(-588809))-687997]]=k end else if Q<16592832-(-208276+1188922)then Z=O[i]Q=Z and 331312+4606419 or-134434-(-593391)f=Z else i=O[R[382602+-382599]]b=-99887+(973064-873145)o=-66988-(-66990)y=i%b p=O[R[((507715-831498)-(-816508))+-492721]]L=O[R[50303+-50301]]u=O[R[(240702-(-122431))-(694793-(((676261+-712529)+-347273)-(-481151-234053)))]]d=u-y u=538044+-538012 g=855915+-855902 K=d/u Q=5655658-(-519564)t=g-K X=o^t B=L/X l=p(B)X=912976-912975 p=695894+(-58818+4294330220)b=l%p l=537346+-537344 p=l^y g=750732-750476 i=b/p p=O[R[-577745+577749]]L=i%X X=4295754159-786863 B=L*X l=p(B)p=O[R[424695-424691]]B=p(i)b=l+B l=853276-(-450142+(811475-(-426407)))o=411273+-411017 p=b%l B=b-p L=-504731-(-570267)l=B/L L=(293253+-243692)+-49305 B=p%L y=nil X=p-B L=X/o o=324784-324528 X=l%o t=l-X l=nil o=t/g t={B,L,X;o}B=nil b=nil O[R[195385-195384]]=t X=nil o=nil i=nil L=nil p=nil end end else if Q<((693358+(-653971+1230263))-267509)+(15434492-65288)then if Q<16778488-518436 then i=b X=329028+-329028 Q=O[R[347714-347713]]o=50796-(-289788+340329)L=Q(X,o)y[i]=L Q=5960557-348682 i=nil else z=#d s=((-1012459+1428835)-(-821332+561511))+-676196 u=b(s,z)s=B(d,u)F=((-521431+849994)-449255)+120693 z=O[K]x=s-F n=L(x)u=nil z[s]=n s=nil Q=-4230+5797437 end else if Q<17174392-604506 then b=-986783-(-986820)i=O[R[784235-784233]]y=i*b i=-982281+30409204293536 k=y+i y=-989007+35184373077839 i=156988+-156987 Q=k%y O[R[856508-(-294269+1150775)]]=Q y=O[R[(789387+-217464)-571920]]Q=15035089-(-535349)k=y~=i else Q=14171901-(-767586-216849)d=v(502942-((469624-3843)-(-33841)))K=w[d]d=v(291056-287732)g=K[d]o=g end end end end end end end Q=#r return P(k)end,function(w,v)local P=b(v)local M=function(M)return Q(w,{M},v,P)end return M end,{},function(w)for v=(-565882-(-797695))-231812,#w,(-189217-245445)-(-434663)do y[w[v]]=y[w[v]]+(-498159-(-498160))end if M then local Q=M(true)local P=r(Q)P[v(-713042-(-716393))],P[v(11248+((770772-12814)-765893))],P[v((-856005+1847491)-988150)]=w,p,function()return-500590-844834 end return Q else return R({},{[v(-479069+482382)]=p,[v(-542976+546327)]=w;[v(529402+-526066)]=function()return-2029702-(-684278)end})end end,function(w,v)local P=b(v)local M=function(M,R,r,D)return Q(w,{M;R,r,D},v,P)end return M end,function()i=i+(-448576+448577)y[i]=-756508+756509 return i end return(l(392642+425533,{}))(P(k))end)(getfenv and getfenv()or _ENV,unpack or table[v(740903-737579)],newproxy,setmetatable,getmetatable,select,{...})end)(...)