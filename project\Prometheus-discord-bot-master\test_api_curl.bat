@echo off
echo 🧪 Testing Prometheus Obfuscator API with curl...
echo.

echo 1️⃣ Testing health check...
curl -s http://localhost:3000/health
echo.
echo.

echo 2️⃣ Testing presets endpoint...
curl -s http://localhost:3000/presets
echo.
echo.

echo 3️⃣ Testing text obfuscation...
curl -s -X POST http://localhost:3000/obfuscate-text ^
  -H "Content-Type: application/json" ^
  -d "{\"code\":\"print('Hello from API!')\",\"preset\":\"Weak\"}"
echo.
echo.

echo ✅ API tests completed!
echo 💡 If you see JSON responses above, the API is working correctly.
pause
