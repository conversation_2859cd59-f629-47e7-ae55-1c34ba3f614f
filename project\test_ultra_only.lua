-- Test script for Ultra obfuscation only
local function greet(name)
    print("Hello, " .. name .. "!")
    return "Greeting sent to " .. name
end

local message = greet("Ultra Test")
print(message)

-- Some variables to obfuscate
local secret = "This is a secret message"
local numbers = {1, 2, 3, 4, 5}

for i, num in ipairs(numbers) do
    print("Number " .. i .. ": " .. num)
end

-- Function with complex logic
local function complexCalculation(x, y)
    local result = 0
    for i = 1, x do
        result = result + (i * y)
    end
    return result
end

local finalResult = complexCalculation(5, 3)
print("Final result: " .. finalResult)
