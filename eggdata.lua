-- Script Path: game:GetService("ReplicatedStorage").Data.PetRegistry.PetEggs
-- Took 0.03s to decompile.
-- Executor: <PERSON> (1.0.0)

local v1 = {}
local v2 = {
    ["Color"] = Color3.fromRGB(163, 120, 45),
    ["HatchTime"] = 14400
}
local v3 = {}
local v4 = {}
local v5 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v4["Silver Monkey"] = v5
local v6 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v4.Cow = v6
local v7 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v4["Sea Otter"] = v7
local v8 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v4.Turtle = v8
local v9 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v4["Polar Bear"] = v9
v3.Items = v4
v2.RarityData = v3
v1["Legendary Egg"] = v2
local v10 = {
    ["Color"] = Color3.fromRGB(33, 84, 185),
    ["HatchTime"] = 7200
}
local v11 = {}
local v12 = {}
local v13 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v12["Orange Tabby"] = v13
local v14 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v12.Monkey = v14
local v15 = {
    ["ItemOdd"] = 15,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v12["Spotted Deer"] = v15
local v16 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v12.Rooster = v16
local v17 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v12.Pig = v17
v11.Items = v12
v10.RarityData = v11
v1["Rare Egg"] = v10
local v18 = {
    ["Color"] = Color3.fromRGB(211, 167, 129),
    ["HatchTime"] = 1200
}
local v19 = {}
local v20 = {}
local v21 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v20["Black Bunny"] = v21
local v22 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v20.Cat = v22
local v23 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v20.Deer = v23
local v24 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v20.Chicken = v24
v19.Items = v20
v18.RarityData = v19
v1["Uncommon Egg"] = v18
local v25 = {
    ["Color"] = Color3.fromRGB(255, 255, 255),
    ["HatchTime"] = 600,
    ["Icon"] = "rbxassetid://88557327752325"
}
local v26 = {}
local v27 = {}
local v28 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v27.Dog = v28
local v29 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v27["Golden Lab"] = v29
local v30 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v27.Bunny = v30
v26.Items = v27
v25.RarityData = v26
v1["Common Egg"] = v25
local v31 = {
    ["Color"] = Color3.fromRGB(255, 204, 0),
    ["HatchTime"] = 18400,
    ["Icon"] = "rbxassetid://83210224500991"
}
local v32 = {}
local v33 = {}
local v34 = {
    ["ItemOdd"] = 36,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v33["Grey Mouse"] = v34
local v35 = {
    ["ItemOdd"] = 27,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v33.Squirrel = v35
local v36 = {
    ["ItemOdd"] = 27,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v33["Brown Mouse"] = v36
local v37 = {
    ["ItemOdd"] = 8.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v33["Red Giant Ant"] = v37
local v38 = {
    ["ItemOdd"] = 1.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v33["Red Fox"] = v38
v32.Items = v33
v31.RarityData = v32
v1["Mythical Egg"] = v31
local v39 = {
    ["Color"] = Color3.fromRGB(213, 255, 134),
    ["HatchTime"] = 28800,
    ["Icon"] = "rbxassetid://83970205286930"
}
local v40 = {}
local v41 = {}
local v42 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v41.Dragonfly = v42
local v43 = {
    ["ItemOdd"] = 4,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v41["Praying Mantis"] = v43
local v44 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v41["Giant Ant"] = v44
local v45 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v41.Snail = v45
local v46 = {
    ["ItemOdd"] = 40,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v41.Caterpillar = v46
v40.Items = v41
v39.RarityData = v40
v1["Bug Egg"] = v39
local v47 = {
    ["Color"] = Color3.fromRGB(213, 255, 134),
    ["HatchTime"] = 30
}
local v48 = {}
local v49 = {}
local v50 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v49.Dragonfly = v50
local v51 = {
    ["ItemOdd"] = 4,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v49["Praying Mantis"] = v51
local v52 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v49.Caterpillar = v52
local v53 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v49["Giant Ant"] = v53
local v54 = {
    ["ItemOdd"] = 40,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v49.Snail = v54
v48.Items = v49
v47.RarityData = v48
v1["Exotic Bug Egg"] = v47
local v55 = {
    ["Color"] = Color3.fromRGB(170, 170, 255),
    ["HatchTime"] = 15000
}
local v56 = {}
local v57 = {}
local v58 = {
    ["ItemOdd"] = 0.1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57.Raccoon = v58
local v59 = {
    ["ItemOdd"] = 3,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57["Night Owl"] = v59
local v60 = {
    ["ItemOdd"] = 7,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57["Echo Frog"] = v60
local v61 = {
    ["ItemOdd"] = 15,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57.Frog = v61
local v62 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57.Mole = v62
local v63 = {
    ["ItemOdd"] = 40,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57.Hedgehog = v63
v56.Items = v57
v55.RarityData = v56
v1["Night Egg"] = v55
local v64 = {
    ["Color"] = Color3.fromRGB(255, 170, 0),
    ["HatchTime"] = 15000,
    ["Icon"] = "rbxassetid://100313281527054"
}
local v65 = {}
local v66 = {}
local v67 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66["Queen Bee"] = v67
local v68 = {
    ["ItemOdd"] = 4,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66["Petal Bee"] = v68
local v69 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66["Bear Bee"] = v69
local v70 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66["Honey Bee"] = v70
local v71 = {
    ["ItemOdd"] = 65,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66.Bee = v71
v65.Items = v66
v64.RarityData = v65
v1["Bee Egg"] = v64
local v72 = {
    ["Color"] = Color3.fromRGB(255, 0, 0),
    ["HatchTime"] = 15000,
    ["Icon"] = "rbxassetid://112867748937791"
}
local v73 = {}
local v74 = {}
local v75 = {
    ["ItemOdd"] = 0.25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v74["Disco Bee"] = v75
local v76 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v74.Butterfly = v76
local v77 = {
    ["ItemOdd"] = 13.75,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v74.Moth = v77
local v78 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v74["Tarantula Hawk"] = v78
local v79 = {
    ["ItemOdd"] = 55,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v74.Wasp = v79
v73.Items = v74
v72.RarityData = v73
v1["Anti Bee Egg"] = v72
local v80 = {
    ["Color"] = Color3.fromRGB(255, 0, 0),
    ["HatchTime"] = 30,
    ["Icon"] = "rbxassetid://112867748937791"
}
local v81 = {}
local v82 = {}
local v83 = {
    ["ItemOdd"] = 0.25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v82["Disco Bee"] = v83
local v84 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v82.Butterfly = v84
local v85 = {
    ["ItemOdd"] = 13.75,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v82.Moth = v85
local v86 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v82["Tarantula Hawk"] = v86
local v87 = {
    ["ItemOdd"] = 55,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v82.Wasp = v87
v81.Items = v82
v80.RarityData = v81
v1["Premium Anti Bee Egg"] = v80
local v88 = {
    ["Color"] = Color3.fromRGB(170, 170, 255),
    ["HatchTime"] = 30
}
local v89 = {}
local v90 = {}
local v91 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v90.Raccoon = v91
local v92 = {
    ["ItemOdd"] = 3,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v90["Night Owl"] = v92
local v93 = {
    ["ItemOdd"] = 7,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v90["Echo Frog"] = v93
local v94 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v90.Frog = v94
local v95 = {
    ["ItemOdd"] = 16,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v90.Mole = v95
local v96 = {
    ["ItemOdd"] = 35,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v90.Hedgehog = v96
v89.Items = v90
v88.RarityData = v89
v1["Premium Night Egg"] = v88
local v97 = {
    ["Color"] = Color3.fromRGB(255, 255, 0),
    ["HatchTime"] = 1200,
    ["Icon"] = "rbxassetid://119578067639695"
}
local v98 = {}
local v99 = {}
local v100 = {
    ["ItemOdd"] = 50,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v99.Starfish = v100
local v101 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v99.Crab = v101
local v102 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v99.Seagull = v102
v98.Items = v99
v97.RarityData = v98
v1["Common Summer Egg"] = v97
local v103 = {
    ["Color"] = Color3.fromRGB(170, 255, 255),
    ["HatchTime"] = 14400,
    ["Icon"] = "rbxassetid://86932815278005"
}
local v104 = {}
local v105 = {}
local v106 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v105.Flamingo = v106
local v107 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v105.Toucan = v107
local v108 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v105["Sea Turtle"] = v108
local v109 = {
    ["ItemOdd"] = 15,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v105.Orangutan = v109
local v110 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v105.Seal = v110
v104.Items = v105
v103.RarityData = v104
v1["Rare Summer Egg"] = v103
local v111 = {
    ["Color"] = Color3.fromRGB(255, 205, 50),
    ["HatchTime"] = 24000,
    ["Icon"] = "rbxassetid://105428605316441"
}
local v112 = {}
local v113 = {}
local v114 = {
    ["ItemOdd"] = 40,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v113.Ostrich = v114
local v115 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v113.Peacock = v115
local v116 = {
    ["ItemOdd"] = 21,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v113.Capybara = v116
local v117 = {
    ["ItemOdd"] = 8,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v113["Scarlet Macaw"] = v117
local v118 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v113["Mimic Octopus"] = v118
v112.Items = v113
v111.RarityData = v112
v1["Paradise Egg"] = v111
local v119 = {
    ["Color"] = Color3.fromRGB(255, 205, 119),
    ["HatchTime"] = 15000,
    ["Icon"] = "rbxassetid://76221946588708"
}
local v120 = {}
local v121 = {}
local v122 = {
    ["ItemOdd"] = 45,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v121.Meerkat = v122
local v123 = {
    ["ItemOdd"] = 34.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v121["Sand Snake"] = v123
local v124 = {
    ["ItemOdd"] = 15,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v121.Axolotl = v124
local v125 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v121["Hyacinth Macaw"] = v125
local v126 = {
    ["ItemOdd"] = 0.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v121["Fennec Fox"] = v126
v120.Items = v121
v119.RarityData = v120
v1["Oasis Egg"] = v119
local v127 = {
    ["Color"] = Color3.fromRGB(255, 205, 119),
    ["HatchTime"] = 30,
    ["Icon"] = "rbxassetid://76221946588708"
}
local v128 = {}
local v129 = {}
local v130 = {
    ["ItemOdd"] = 45,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v129.Meerkat = v130
local v131 = {
    ["ItemOdd"] = 34.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v129["Sand Snake"] = v131
local v132 = {
    ["ItemOdd"] = 15,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v129.Axolotl = v132
local v133 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v129["Hyacinth Macaw"] = v133
local v134 = {
    ["ItemOdd"] = 0.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v129["Fennec Fox"] = v134
v128.Items = v129
v127.RarityData = v128
v1["Premium Oasis Egg"] = v127
local v135 = {
    ["Color"] = Color3.fromRGB(85, 170, 127),
    ["HatchTime"] = 15000,
    ["Icon"] = "rbxassetid://71688868083095"
}
local v136 = {}
local v137 = {}
local v138 = {
    ["ItemOdd"] = 35,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v137.Raptor = v138
local v139 = {
    ["ItemOdd"] = 32.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v137.Triceratops = v139
local v140 = {
    ["ItemOdd"] = 28,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v137.Stegosaurus = v140
local v141 = {
    ["ItemOdd"] = 3,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v137.Pterodactyl = v141
local v142 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v137.Brontosaurus = v142
local v143 = {
    ["ItemOdd"] = 0.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v137["T-Rex"] = v143
v136.Items = v137
v135.RarityData = v136
v1["Dinosaur Egg"] = v135
local v144 = {
    ["Color"] = Color3.fromRGB(85, 170, 0),
    ["HatchTime"] = 15000,
    ["Icon"] = "rbxassetid://85559070844097"
}
local v145 = {}
local v146 = {}
local v147 = {
    ["ItemOdd"] = 35,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v146.Parasaurolophus = v147
local v148 = {
    ["ItemOdd"] = 32.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v146.Iguanodon = v148
local v149 = {
    ["ItemOdd"] = 28,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v146.Pachycephalosaurus = v149
local v150 = {
    ["ItemOdd"] = 3,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v146.Dilophosaurus = v150
local v151 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v146.Ankylosaurus = v151
local v152 = {
    ["ItemOdd"] = 0.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v146.Spinosaurus = v152
v145.Items = v146
v144.RarityData = v145
v1["Primal Egg"] = v144
local v153 = {
    ["Color"] = Color3.fromRGB(85, 170, 0),
    ["HatchTime"] = 30,
    ["Icon"] = "rbxassetid://85559070844097"
}
local v154 = {}
local v155 = {}
local v156 = {
    ["ItemOdd"] = 34,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v155.Parasaurolophus = v156
local v157 = {
    ["ItemOdd"] = 32.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v155.Iguanodon = v157
local v158 = {
    ["ItemOdd"] = 28,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v155.Pachycephalosaurus = v158
local v159 = {
    ["ItemOdd"] = 3,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v155.Dilophosaurus = v159
local v160 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v155.Ankylosaurus = v160
local v161 = {
    ["ItemOdd"] = 0.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v155.Spinosaurus = v161
local v162 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 1, 1 },
        ["HugeChance"] = 0
    }
}
v155["Egg/Rainbow Premium Primal Egg"] = v162
v154.Items = v155
v153.RarityData = v154
v1["Premium Primal Egg"] = v153
local v163 = {
    ["Color"] = Color3.fromRGB(255, 255, 255),
    ["HatchTime"] = 15000,
    ["Icon"] = "rbxassetid://109901373212167"
}
local v164 = {}
local v165 = {}
local v166 = {
    ["ItemOdd"] = 40,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v165["Shiba Inu"] = v166
local v167 = {
    ["ItemOdd"] = 31,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v165.Nihonzaru = v167
local v168 = {
    ["ItemOdd"] = 20.82,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v165.Tanuki = v168
local v169 = {
    ["ItemOdd"] = 4.6,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v165.Tanchozuru = v169
local v170 = {
    ["ItemOdd"] = 3.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v165.Kappa = v170
local v171 = {
    ["ItemOdd"] = 0.08,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v165.Kitsune = v171
v164.Items = v165
v163.RarityData = v164
v1["Zen Egg"] = v163
local v172 = {
    ["Color"] = Color3.fromRGB(85, 0, 0),
    ["HatchTime"] = 15000,
    ["Icon"] = "rbxassetid://109901373212167"
}
local v173 = {}
local v174 = {}
local v175 = {
    ["ItemOdd"] = 40,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v174["Maneki-neko"] = v175
local v176 = {
    ["ItemOdd"] = 32.2,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v174.Tsuchinoko = v176
local v177 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v174.Kodama = v177
local v178 = {
    ["ItemOdd"] = 4,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v174.Raiju = v178
local v179 = {
    ["ItemOdd"] = 3.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v174.Mizuchi = v179
local v180 = {
    ["ItemOdd"] = 0.3,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v174["Corrupted Kitsune"] = v180
v173.Items = v174
v172.RarityData = v173
v1["Corrupted Zen Egg"] = v172
local v181 = {
    ["Color"] = Color3.fromRGB(85, 170, 0),
    ["HatchTime"] = 30,
    ["Icon"] = "rbxassetid://73988542242362"
}
local v182 = {}
local v183 = {}
local v184 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v183["Rainbow Parasaurolophus"] = v184
local v185 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v183["Rainbow Iguanodon"] = v185
local v186 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v183["Rainbow Pachycephalosaurus"] = v186
local v187 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v183["Rainbow Dilophosaurus"] = v187
local v188 = {
    ["ItemOdd"] = 8,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v183["Rainbow Ankylosaurus"] = v188
local v189 = {
    ["ItemOdd"] = 7,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v183["Rainbow Spinosaurus"] = v189
v182.Items = v183
v181.RarityData = v182
v1["Rainbow Premium Primal Egg"] = v181
local v190 = {
    ["Color"] = Color3.fromRGB(255, 255, 255),
    ["HatchTime"] = 300000000
}
local v191 = {}
local v192 = {}
local v193 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Silver Monkey"] = v193
local v194 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Kiwi = v194
local v195 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Cow = v195
local v196 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Sea Otter"] = v196
local v197 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Turtle = v197
local v198 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Polar Bear"] = v198
local v199 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Orange Tabby"] = v199
local v200 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Moon Cat"] = v200
local v201 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Monkey = v201
local v202 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Spotted Deer"] = v202
local v203 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Rooster = v203
local v204 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Pig = v204
local v205 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Black Bunny"] = v205
local v206 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Cat = v206
local v207 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Deer = v207
local v208 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Chicken = v208
local v209 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Dog = v209
local v210 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Golden Lab"] = v210
local v211 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Bunny = v211
local v212 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Dragonfly = v212
local v213 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Praying Mantis"] = v213
local v214 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Caterpillar = v214
local v215 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Giant Ant"] = v215
local v216 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Snail = v216
local v217 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Raccoon = v217
local v218 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Owl = v218
local v219 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Night Owl"] = v219
local v220 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Echo Frog"] = v220
local v221 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Frog = v221
local v222 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Mole = v222
local v223 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Hedgehog = v223
local v224 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Blood Hedgehog"] = v224
local v225 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Blood Kiwi"] = v225
local v226 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Blood Owl"] = v226
local v227 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Chicken Zombie"] = v227
local v228 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Grey Mouse"] = v228
local v229 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Squirrel = v229
local v230 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Brown Mouse"] = v230
local v231 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Red Giant Ant"] = v231
local v232 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Red Fox"] = v232
local v233 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Queen Bee"] = v233
local v234 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Bear Bee"] = v234
local v235 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Petal Bee"] = v235
local v236 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Bee = v236
local v237 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Disco Bee"] = v237
local v238 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Butterfly = v238
local v239 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Tarantula Hawk"] = v239
local v240 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Moth = v240
local v241 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Wasp = v241
local v242 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Cooked Owl"] = v242
local v243 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Pack Bee"] = v243
local v244 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Starfish = v244
local v245 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Crab = v245
local v246 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Seagull = v246
local v247 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Flamingo = v247
local v248 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Toucan = v248
local v249 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Sea Turtle"] = v249
local v250 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Orangutan = v250
local v251 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Seal = v251
local v252 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Ostrich = v252
local v253 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Peacock = v253
local v254 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Capybara = v254
local v255 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Scarlet Macaw"] = v255
local v256 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Mimic Octopus"] = v256
local v257 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Meerkat = v257
local v258 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Sand Snake"] = v258
local v259 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Axolotl = v259
local v260 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Hyacinth Macaw"] = v260
local v261 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Fennec Fox"] = v261
local v262 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Hamster = v262
local v263 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Raptor = v263
local v264 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Triceratops = v264
local v265 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Stegosaurus = v265
local v266 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Pterodactyl = v266
local v267 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Brontosaurus = v267
local v268 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["T-Rex"] = v268
local v269 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Iguanodon = v269
local v270 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Parasaurolophus "] = v270
local v271 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Pachycephalosaurus = v271
local v272 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Ankylosaurus = v272
local v273 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Dilophosaurus = v273
local v274 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Spinosaurus = v274
local v275 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Parasaurolophus"] = v275
local v276 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Iguanodon"] = v276
local v277 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Pachycephalosaurus"] = v277
local v278 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Dilophosaurus"] = v278
local v279 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Ankylosaurus"] = v279
local v280 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Spinosaurus"] = v280
local v281 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Shiba Inu"] = v281
local v282 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Nihonzaru = v282
local v283 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Tanuki = v283
local v284 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Tanchozuru = v284
local v285 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Kappa = v285
local v286 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Kitsune = v286
local v287 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Koi = v287
local v288 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Football = v288
local v289 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Maneki-neko"] = v289
local v290 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Tsuchinoko = v290
local v291 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Kodama = v291
local v292 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Raiju = v292
local v293 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Mizuchi = v293
local v294 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Corrupted Kitsune"] = v294
local v295 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Maneki-neko"] = v295
local v296 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Kodama"] = v296
local v297 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Corrupted Kitsune"] = v297
local v298 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Golden Bee"] = v298
local v299 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Red Dragon"] = v299
local v300 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Firefly = v300
v191.Items = v192
v190.RarityData = v191
v1["Fake Egg"] = v190
return v1
-- Script Path: game:GetService("ReplicatedStorage").Data.PetRegistry.PetList
-- Took 0.05s to decompile.
-- Executor: Swift (1.0.0)

local v1 = game:GetService("ReplicatedStorage")
local v2 = game:GetService("ServerStorage")
local v3 = game:GetService("RunService")
local v4 = v1:WaitForChild("Assets")
local v5 = v3:IsServer() and v2:WaitForChild("PetAssets") or Instance.new("Folder")
local v6 = v4:WaitForChild("Animations"):WaitForChild("PetAnimations")
local v7 = {
    ["Dog"] = {
        ["Description"] = "Digging Buddy: Occasionally digs up a random seed",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Dog"),
        ["DefaultHunger"] = 1000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://135018170520317",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 55000,
        ["Rarity"] = "Common",
        ["Passives"] = { "Digging Buddy" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.Dog.Idle,
            ["Walk"] = v6.Dog.Walk,
            ["Dig"] = v6.Dog.Dig
        },
        ["States"] = {}
    },
    ["Golden Lab"] = {
        ["Description"] = "Digging Friend: Occasionally digs up a random seed at a higher chance",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Dog"),
        ["Variant"] = "Golden Lab",
        ["DefaultHunger"] = 1200,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://99376934607716",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 55000,
        ["Rarity"] = "Common",
        ["Passives"] = { "Digging Friend" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.Dog.Idle,
            ["Walk"] = v6.Dog.Walk,
            ["Dig"] = v6.Dog.Dig
        },
        ["States"] = {}
    },
    ["Bunny"] = {
        ["Description"] = "Carrot Chomper: Runs to carrots, eats them, and grants bonus sheckles (more than normal value)",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Bunny"),
        ["DefaultHunger"] = 1100,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://85830855120751",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 55000,
        ["Rarity"] = "Common",
        ["Passives"] = { "Carrot Chomper" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.Bunny.Idle,
            ["Walk"] = v6.Bunny.Walk,
            ["Chomp"] = v6.Bunny.Chomp
        },
        ["States"] = {}
    },
    ["Black Bunny"] = {
        ["Description"] = "Carrot Devourer: Runs to carrots, eats them, and grants bonus sheckles (more than normal value)",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Bunny"),
        ["Variant"] = "Black Bunny",
        ["DefaultHunger"] = 1300,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://86614624778104",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 165000,
        ["Rarity"] = "Uncommon",
        ["Passives"] = { "Carrot Chomper" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.Bunny.Idle,
            ["Walk"] = v6.Bunny.Walk,
            ["Chomp"] = v6.Bunny.Chomp
        },
        ["States"] = {}
    },
    ["Cat"] = {
        ["Description"] = "Cat Nap: Cat naps in a random spot in your farm, emitting an aura that boosts nearby fruit size",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Cat"),
        ["DefaultHunger"] = 1400,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://136444015144013",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 12,
        ["SellPrice"] = 165000,
        ["Rarity"] = "Uncommon",
        ["Passives"] = { "Cat Nap" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.Cat.Idle,
            ["Walk"] = v6.Cat.Walk,
            ["Nap"] = v6.Cat.Nap
        },
        ["States"] = {}
    },
    ["Deer"] = {
        ["Description"] = "Forester: When harvesting berry plants, there is a chance the fruit will remain",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Deer"),
        ["DefaultHunger"] = 2500,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.009,
        ["Icon"] = "rbxassetid://91926785467809",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 12,
        ["SellPrice"] = 165000,
        ["Rarity"] = "Uncommon",
        ["Passives"] = { "Forester" },
        ["YHeightScaler"] = 0.65,
        ["Animations"] = {
            ["Idle"] = v6.Deer.Idle,
            ["Walk"] = v6.Deer.Walk,
            ["Stomp"] = v6.Deer.Stomp
        },
        ["States"] = {}
    },
    ["Chicken"] = {
        ["Description"] = "Eggcelerator: Decreases the time needed to hatch other eggs",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Chicken"),
        ["DefaultHunger"] = 3400,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://108080824427369",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 165000,
        ["Rarity"] = "Uncommon",
        ["Passives"] = { "Eggcelerator" },
        ["YHeightScaler"] = 0.15,
        ["Animations"] = {
            ["Idle"] = v6.Chicken.Idle,
            ["Walk"] = v6.Chicken.Walk
        },
        ["States"] = {}
    },
    ["Orange Tabby"] = {
        ["Description"] = "Orange Tabby: Orange Tabby naps in a random spot in your farm, emitting an aura that boosts nearby fruit size",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Cat"),
        ["Variant"] = "Orange Tabby",
        ["DefaultHunger"] = 1500,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://103360220936666",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 12,
        ["SellPrice"] = 660000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Lazy Nap" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.Cat.Idle,
            ["Walk"] = v6.Cat.Walk,
            ["Nap"] = v6.Cat.Nap
        },
        ["States"] = {}
    },
    ["Spotted Deer"] = {
        ["Description"] = "Forester: When harvesting berry plants, there is a chance the fruit will remain",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Spotted Deer"),
        ["DefaultHunger"] = 2500,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.009,
        ["Icon"] = "rbxassetid://126439207915258",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 12,
        ["SellPrice"] = 660000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Spotted Forester" },
        ["YHeightScaler"] = 0.65,
        ["Animations"] = {
            ["Idle"] = v6.Deer.Idle,
            ["Walk"] = v6.Deer.Walk,
            ["Stomp"] = v6.Deer.Stomp
        },
        ["States"] = {}
    },
    ["Rooster"] = {
        ["Description"] = "Eggcelerator: Decreases the time needed to hatch other eggs",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Rooster"),
        ["DefaultHunger"] = 4000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://137107493326109",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 650000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Better Eggcelerator" },
        ["YHeightScaler"] = 0.15,
        ["Animations"] = {
            ["Idle"] = v6.Chicken.Idle,
            ["Walk"] = v6.Chicken.Walk
        },
        ["States"] = {}
    },
    ["Monkey"] = {
        ["Description"] = "Cheeky Refund: 3% chance to get your fruit back when you sell it",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Monkey"),
        ["DefaultHunger"] = 7400,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://113881196210664",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 660000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Cheeky Refund" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.Monkey.Idle,
            ["Walk"] = v6.Monkey.Walk,
            ["Steal"] = v6.Monkey.Steal
        },
        ["States"] = {}
    },
    ["Pig"] = {
        ["Description"] = "Fertilizer Frenzy: Occasionally releases a fertilizing AOE boosting plant size and mutation chance",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Pig"),
        ["DefaultHunger"] = 5000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.008,
        ["Icon"] = "rbxassetid://134476443266448",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 650000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Fertilizer Frenzy" },
        ["YHeightScaler"] = 0.6,
        ["Animations"] = {
            ["Idle"] = v6.Pig.Idle,
            ["Walk"] = v6.Pig.Walk,
            ["Frenzy"] = v6.Pig.Frenzy
        },
        ["States"] = {}
    },
    ["Silver Monkey"] = {
        ["Description"] = "Cheeky Refund: 3% chance to get your fruit back when you sell it",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Monkey"),
        ["Variant"] = "Silver Monkey",
        ["DefaultHunger"] = 8000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://136985272620600",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Premium Cheeky Refund" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.Monkey.Idle,
            ["Walk"] = v6.Monkey.Walk,
            ["Steal"] = v6.Monkey.Steal
        },
        ["States"] = {}
    },
    ["Turtle"] = {
        ["Description"] = "Turtle Tinkerer: Slowing aura that makes sprinklers last longer",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Turtle"),
        ["DefaultHunger"] = 10000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.004,
        ["Icon"] = "rbxassetid://92906330087175",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 5,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Turtle Tinkerer" },
        ["YHeightScaler"] = -0.32,
        ["Animations"] = {
            ["Idle"] = v6.Turtle.Idle,
            ["Walk"] = v6.Turtle.Walk
        },
        ["States"] = {}
    },
    ["Cow"] = {
        ["Description"] = "Milk of the Land: Fertilizing aura that boosts nearby plant growth speed ",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Cow"),
        ["DefaultHunger"] = 9500,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://118832676475537",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Legendary",
        ["YHeightScaler"] = 0.5,
        ["Passives"] = { "Milk of the Land" },
        ["Animations"] = {
            ["Idle"] = v6.Cow.Idle,
            ["Walk"] = v6.Cow.Walk
        },
        ["States"] = {}
    },
    ["Sea Otter"] = {
        ["Description"] = "Water Spray: Water\'s plants randomly like a watering can",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Sea Otter"),
        ["DefaultHunger"] = 30000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://94422445572440",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Water Spray" },
        ["YHeightScaler"] = -0.25,
        ["Animations"] = {
            ["Idle"] = v6.Otter.Idle,
            ["Walk"] = v6.Otter.Walk,
            ["Spray"] = v6.Otter.Spray
        }
    },
    ["Polar Bear"] = {
        ["Description"] = "Polar Express: Occasionally sets a random nearby fruit cold, turning it into Chilled with a small chance for Frozen",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Polar Bear"),
        ["DefaultHunger"] = 20000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://72209118254193",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Polar Express" },
        ["YHeightScaler"] = 0.35,
        ["Animations"] = {
            ["Idle"] = v6.Bear.Idle,
            ["Walk"] = v6.Bear.Walk,
            ["Roar"] = v6.Bear.Roar
        }
    },
    ["Caterpillar"] = {
        ["Description"] = "Leaf Lover Passive: Boost nearby Leafy plants growth rate",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Caterpillar"),
        ["DefaultHunger"] = 25000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.0065,
        ["Icon"] = "rbxassetid://119651461526366",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 6,
        ["SellPrice"] = 50000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Leaf Lover" },
        ["YHeightScaler"] = -0.55,
        ["Animations"] = {
            ["Idle"] = v6.Caterpillar.Idle,
            ["Walk"] = v6.Caterpillar.Walk
        }
    },
    ["Snail"] = {
        ["Description"] = "Slow and Steady: Increased lucky harvest chance",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Snail"),
        ["DefaultHunger"] = 12000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, 3.141592653589793),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://80970021440625",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 3,
        ["SellPrice"] = 50000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Slow and Steady" },
        ["YHeightScaler"] = 0,
        ["Animations"] = {
            ["Idle"] = v6.Snail.Idle,
            ["Walk"] = v6.Snail.Walk
        },
        ["States"] = {}
    },
    ["Giant Ant"] = {
        ["Description"] = "For the Blue Colony: Small chance to duplicate harvested plant & Prehistoric Harvester: Increased chance to duplicate harvested candy type plant",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Giant Ant"),
        ["DefaultHunger"] = 18000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, 3.141592653589793),
        ["ModelScalePerLevel"] = 0.003,
        ["Icon"] = "rbxassetid://71413253805996",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 5,
        ["SellPrice"] = 60000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "For the Blue Colony", "Candy Harvester" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.Ant.Idle,
            ["Walk"] = v6.Ant.Walk,
            ["Grab"] = v6.Ant.Grab
        }
    },
    ["Praying Mantis"] = {
        ["Description"] = "Zen Zone: Prays, then gives plants in AOE Buff that increases the chance of gold fruit from plants",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Praying Mantis"),
        ["DefaultHunger"] = 55000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, 3.141592653589793),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://121485029406440",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 60000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Zen Zone" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v6.Mantis.Idle,
            ["Walk"] = v6.Mantis.Walk,
            ["Pray"] = v6.Mantis.Pray
        }
    },
    ["Dragonfly"] = {
        ["Description"] = "Transmutation: Every now and then turns a fruit to gold",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Dragonfly"),
        ["DefaultHunger"] = 100000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 0),
        ["ModelScalePerLevel"] = 0.008,
        ["Icon"] = "rbxassetid://118484611393651",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 70000000,
        ["Rarity"] = "Divine",
        ["Passives"] = { "Transmutation" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.Dragonfly.Idle,
            ["Walk"] = v6.Dragonfly.Walk
        }
    },
    ["Panda"] = {
        ["Description"] = "Bamboozle: Waddles to bamboo, eats it, and grants bonus sheckles (more than normal value)",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Panda"),
        ["DefaultHunger"] = 20000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://107090327345246",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Bamboozle" },
        ["YHeightScaler"] = 0.35,
        ["Animations"] = {
            ["Idle"] = v6.Panda.Idle,
            ["Walk"] = v6.Panda.Walk,
            ["Chomp"] = v6.Panda.Chomp
        }
    },
    ["Hedgehog"] = {
        ["Description"] = "Prickly Lover: Makes nearby prickly fruit grow bigger",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Hedgehog"),
        ["DefaultHunger"] = 30000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://83544966481425",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 7,
        ["SellPrice"] = 1000000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Prickly Lover" },
        ["YHeightScaler"] = 0.01,
        ["Animations"] = {
            ["Idle"] = v6.Hedgehog.Idle,
            ["Walk"] = v6.Hedgehog.Walk,
            ["Curl"] = v6.Hedgehog.Curl,
            ["CurlLoop"] = v6.Hedgehog.CurlLoop
        }
    },
    ["Kiwi"] = {
        ["Description"] = "Nocturnal Nursery: Occasionally reduces the hatch time of the egg with the most hatch time left",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Kiwi"),
        ["DefaultHunger"] = 50000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://104651906442347",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 1000000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Nocturnal Nursery" },
        ["YHeightScaler"] = 0.9,
        ["Animations"] = {
            ["Idle"] = v6.Kiwi.Idle,
            ["Walk"] = v6.Kiwi.Walk,
            ["Nurse"] = v6.Kiwi.Nurse
        }
    },
    ["Mole"] = {
        ["Description"] = "Treasure Hunter: Will occasionally dig down to find gear or sheckles",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Mole"),
        ["DefaultHunger"] = 50000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, 0),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://79089804794269",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 6,
        ["SellPrice"] = 2000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Treasure Hunter" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v6.Mole.Idle,
            ["Walk"] = v6.Mole.Walk,
            ["DigDown"] = v6.Mole.DigDown,
            ["DigUp"] = v6.Mole.DigUp,
            ["DigWalk"] = v6.Mole.DigWalk
        }
    },
    ["Frog"] = {
        ["Description"] = "Croak: Will occasionally advance a nearby plant\'s growth by 24 hours",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Frog"),
        ["DefaultHunger"] = 50000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://96930166899467",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 3000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Croak" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v6.Frog.Idle,
            ["Walk"] = v6.Frog.Walk,
            ["Croak"] = v6.Frog.Croak
        }
    },
    ["Echo Frog"] = {
        ["Description"] = "Echo Croak: Will occasionally advance a nearby plant\'s growth by 24 hours",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Echo Frog"),
        ["DefaultHunger"] = 50000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://102271225890686",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 5000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Echo Croak" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v6.Frog.Idle,
            ["Walk"] = v6.Frog.Walk,
            ["Croak"] = v6.Frog.Croak
        }
    },
    ["Raccoon"] = {
        ["Description"] = "Rascal: Occasionally steals (duplicates) fruit from other player\'s plot and hands it to you",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Raccoon"),
        ["DefaultHunger"] = 45000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://136232391555861",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 20000000,
        ["Rarity"] = "Divine",
        ["Passives"] = { "Rascal" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v6.Raccoon.Idle,
            ["Walk"] = v6.Raccoon.Walk,
            ["Steal"] = v6.Raccoon.Steal
        }
    },
    ["Night Owl"] = {
        ["Description"] = "King of the Night: Grants bonus experience per second gain to all active pets.",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Night Owl"),
        ["DefaultHunger"] = 50000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://101760640498094",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 15000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "King of the Night", "Movement Variation" },
        ["YHeightScaler"] = 0.5,
        ["Animations"] = {
            ["Idle"] = v6.Owl.Idle,
            ["Walk"] = v6.Owl.Walk,
            ["Fly"] = v6.Owl.Fly,
            ["FlyUp"] = v6.Owl.FlyUp,
            ["FlyDown"] = v6.Owl.FlyDown
        }
    },
    ["Owl"] = {
        ["Description"] = "Prince of the Night: Grants bonus experience per second gain to all active pets.",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Owl"),
        ["DefaultHunger"] = 50000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://138016343005291",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 15000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Prince of the Night", "Movement Variation" },
        ["YHeightScaler"] = 0.5,
        ["Animations"] = {
            ["Idle"] = v6.Owl.Idle,
            ["Walk"] = v6.Owl.Walk,
            ["Fly"] = v6.Owl.Fly,
            ["FlyUp"] = v6.Owl.FlyUp,
            ["FlyDown"] = v6.Owl.FlyDown
        }
    },
    ["Grey Mouse"] = {
        ["Description"] = "Whisker Wisdom: Occasionally gains bonus experience & Scamper: Increase player movement speed",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Grey Mouse"),
        ["DefaultHunger"] = 15000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://116310390398341",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 5500000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Whisker Wisdom", "Scamper" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.Mouse.Idle,
            ["Walk"] = v6.Mouse.Walk,
            ["Think"] = v6.Mouse.Think
        },
        ["States"] = {}
    },
    ["Squirrel"] = {
        ["Description"] = "Seed Stash: Grants a chance to not consume a use when using the reclaimer & Nutty Apology: Gains additional XP per second",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Squirrel"),
        ["DefaultHunger"] = 15000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://96950434895806",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 5500000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Seed Stash", "Nutty Apology" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.Squirrel.Idle,
            ["Walk"] = v6.Squirrel.Walk
        },
        ["States"] = {}
    },
    ["Brown Mouse"] = {
        ["Description"] = "Whiskier Wisdom: Occasionally gains bonus experience & Cheese Hop: Increase player jump height",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Grey Mouse"),
        ["Variant"] = "Brown Mouse",
        ["DefaultHunger"] = 15000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://94641319183999",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 5500000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Whiskier Wisdom", "Cheese Hop" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.Mouse.Idle,
            ["Walk"] = v6.Mouse.Walk,
            ["Think"] = v6.Mouse.Think
        },
        ["States"] = {}
    },
    ["Red Giant Ant"] = {
        ["Description"] = "For the Red Colony: Small chance to duplicate harvested plant & Fruit Harvester: Increased chance to duplicate harvested fruit type plant",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Giant Ant"),
        ["Variant"] = "Red Giant Ant",
        ["DefaultHunger"] = 15000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, 3.141592653589793),
        ["ModelScalePerLevel"] = 0.003,
        ["Icon"] = "rbxassetid://89449712431551",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 5,
        ["SellPrice"] = 10500000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "For the Red Colony", "Fruit Harvester" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.Ant.Idle,
            ["Walk"] = v6.Ant.Walk,
            ["Grab"] = v6.Ant.Grab
        }
    },
    ["Red Fox"] = {
        ["Description"] = "Scoundrel: Every <Cooldown>m, goes to another player\'s plot and tries to steal a seed from a random plant. The rarer the plant, the harder it is to succeed!",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Red Fox"),
        ["DefaultHunger"] = 35000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://116662854190616",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 16,
        ["SellPrice"] = 20500000,
        ["Rarity"] = "Divine",
        ["Passives"] = { "Scoundrel" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v6.Fox.Idle,
            ["Walk"] = v6.Fox.Walk,
            ["Steal"] = v6.Fox.Steal
        }
    },
    ["Chicken Zombie"] = {
        ["Description"] = "Zombify: Occasionally has a chance to zombify a nearby random fruit & Eggcelerator: Decreases the time needed to hatch other eggs",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Chicken Zombie"),
        ["DefaultHunger"] = 35000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://108581559611673",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 1000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Zombify", "Eggcelerator" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.ChickenZombie.Idle,
            ["Walk"] = v6.ChickenZombie.Walk,
            ["Zombify"] = v6.ChickenZombie.Zombify
        },
        ["States"] = {}
    },
    ["Blood Hedgehog"] = {
        ["Description"] = "Sanguine Spike: Makes nearby prickly fruit have increased variant chance and grow bigger",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Blood Hedgehog"),
        ["DefaultHunger"] = 30000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://76471191139414",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 7,
        ["SellPrice"] = 33000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Sanguine Spike", "Prickly Blessing" },
        ["YHeightScaler"] = 0.01,
        ["Animations"] = {
            ["Idle"] = v6.Hedgehog.Idle,
            ["Walk"] = v6.Hedgehog.Walk,
            ["Curl"] = v6.Hedgehog.Curl,
            ["CurlLoop"] = v6.Hedgehog.CurlLoop
        }
    },
    ["Blood Kiwi"] = {
        ["Description"] = "Crimson Cradle: Occasionally reduces the egg hatch time and boosts egg hatch speed",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Blood Kiwi"),
        ["DefaultHunger"] = 45000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://87343374343285",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 30000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Crimson Cradle", "Better Eggcelerator" },
        ["YHeightScaler"] = 0.9,
        ["Animations"] = {
            ["Idle"] = v6.Kiwi.Idle,
            ["Walk"] = v6.Kiwi.Walk,
            ["Nurse"] = v6.Kiwi.Nurse
        }
    },
    ["Blood Owl"] = {
        ["Description"] = "Monarch of Midnight: Grants bonus experience per second gain to all active pets",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Blood Owl"),
        ["DefaultHunger"] = 50000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://81262783747840",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 70000000,
        ["Rarity"] = "Divine",
        ["Passives"] = { "Monarch of Midnight", "Movement Variation" },
        ["YHeightScaler"] = 0.5,
        ["Animations"] = {
            ["Idle"] = v6.Owl.Idle,
            ["Walk"] = v6.Owl.Walk,
            ["Fly"] = v6.Owl.Fly,
            ["FlyUp"] = v6.Owl.FlyUp,
            ["FlyDown"] = v6.Owl.FlyDown
        }
    },
    ["Moon Cat"] = {
        ["Description"] = "Moon Nap: Moon cat naps in a random spot in your farm, and boosts nearby fruit size & Moon Harvest: Grants chance for Night type plants to replant when harvested ",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Cat"),
        ["Variant"] = "Moon Cat",
        ["DefaultHunger"] = 2400,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://72392850111255",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 25000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Moon Nap", "Moon Harvest" },
        ["YHeightScaler"] = 0.4,
        ["Animations"] = {
            ["Idle"] = v6.Cat.Idle,
            ["Walk"] = v6.Cat.Walk,
            ["Nap"] = v6.Cat.Nap
        },
        ["States"] = {}
    },
    ["Bee"] = {
        ["Description"] = "Pollinator: Occasionally pollinates fruit",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Bee"),
        ["DefaultHunger"] = 25000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, -3.141592653589793),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://114116135745614",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 400000,
        ["Rarity"] = "Uncommon",
        ["Passives"] = { "Pollinator" },
        ["YHeightScaler"] = 0.2,
        ["Animations"] = {
            ["Idle"] = v6.Bee.Idle,
            ["Walk"] = v6.Bee.Walk,
            ["Pollinate"] = v6.Bee.Pollinate
        }
    },
    ["Honey Bee"] = {
        ["Description"] = "Beeter Pollinator: Occasionally pollinates fruit",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Bee"),
        ["Variant"] = "Honey Bee",
        ["DefaultHunger"] = 25000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, -3.141592653589793),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://134999468799162",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 800000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Beeter Pollinator" },
        ["YHeightScaler"] = 0.2,
        ["Animations"] = {
            ["Idle"] = v6.Bee.Idle,
            ["Walk"] = v6.Bee.Walk,
            ["Pollinate"] = v6.Bee.Pollinate
        }
    },
    ["Petal Bee"] = {
        ["Description"] = "Pollinator: Occasionally pollinates fruit & Flower Harvest: Harvested flowers have a chance to stay",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Bee"),
        ["Variant"] = "Petal Bee",
        ["DefaultHunger"] = 25000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, -3.141592653589793),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://137924182648564",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 1000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Pollinator", "Flower Harvest" },
        ["YHeightScaler"] = 0.03,
        ["Animations"] = {
            ["Idle"] = v6.Bee.Idle,
            ["Walk"] = v6.Bee.Walk,
            ["Pollinate"] = v6.Bee.Pollinate
        }
    },
    ["Bear Bee"] = {
        ["Description"] = "Wanna-Bee: Occasionally tries to pollinate fruit, but it just ends up being Honey-Glazed",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Bear Bee"),
        ["DefaultHunger"] = 45000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://97465846056354",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 4000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Wanna-Bee" },
        ["YHeightScaler"] = 0.35,
        ["Animations"] = {
            ["Idle"] = v6.BearBee.Idle,
            ["Walk"] = v6.BearBee.Walk,
            ["Sit"] = v6.BearBee.Sit
        }
    },
    ["Queen Bee"] = {
        ["Description"] = "Queen Pollinator: Occasionally pollinates fruit instantly & For the Queen: Occasionally refrehes the pet with the highest cooldown ability",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Queen Bee"),
        ["DefaultHunger"] = 65000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, -2.530727415391778),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://127281358672581",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 5,
        ["SellPrice"] = 8000000,
        ["Rarity"] = "Divine",
        ["Passives"] = { "Queen Pollinator", "For the Queen" },
        ["YHeightScaler"] = 10,
        ["Animations"] = {
            ["Idle"] = v6.QueenBee.Idle,
            ["Walk"] = v6.QueenBee.Walk,
            ["Spin"] = v6.QueenBee.Spin
        }
    },
    ["Wasp"] = {
        ["Description"] = "Wasp Pollinator: Occasionally pollinates fruit & Stinger: Occasionally stings pet with highest cooldown advancing cooldown",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Wasp"),
        ["DefaultHunger"] = 28000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://72767862942880",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 12,
        ["SellPrice"] = 1000000,
        ["Rarity"] = "Uncommon",
        ["Passives"] = { "Wasp Pollinator", "Stinger" },
        ["YHeightScaler"] = 0.2,
        ["Animations"] = {
            ["Idle"] = v6.Wasp.Idle,
            ["Walk"] = v6.Wasp.Walk,
            ["Pollinate"] = v6.Wasp.Pollinate,
            ["Sting"] = v6.Wasp.Sting
        }
    },
    ["Tarantula Hawk"] = {
        ["Description"] = "Wasp Pollinator: Occasionally pollinates fruit & Tarantula Stinger: Occasionally stings pet with highest cooldown advancing cooldown",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Tarantula Hawk"),
        ["DefaultHunger"] = 28000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://126203792467378",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 12,
        ["SellPrice"] = 2000000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Pollinator", "Tarantula Stinger" },
        ["YHeightScaler"] = 0.2,
        ["Animations"] = {
            ["Idle"] = v6.Wasp.Idle,
            ["Walk"] = v6.Wasp.Walk,
            ["Pollinate"] = v6.Wasp.Pollinate,
            ["Sting"] = v6.Wasp.Sting
        }
    },
    ["Moth"] = {
        ["Description"] = "Silksong: Sings to a random pet and magically restore its hunger",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Moth"),
        ["DefaultHunger"] = 25000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://134180528391091",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 4000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Silksong" },
        ["YHeightScaler"] = 0.2,
        ["Animations"] = {
            ["Idle"] = v6.Moth.Idle,
            ["Walk"] = v6.Moth.Walk,
            ["Sing"] = v6.Moth.Sing
        }
    },
    ["Butterfly"] = {
        ["Description"] = "Rainbow Flutter: Occasionally flies to a fruit with 4+ mutations, removes all mutations from it and converts it into rainbow. Ignores favorited fruit",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Butterfly"),
        ["DefaultHunger"] = 26000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://119048229505161",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 8000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Rainbow Flutter" },
        ["YHeightScaler"] = 0.2,
        ["Animations"] = {
            ["Idle"] = v6.Butterfly.Idle,
            ["Walk"] = v6.Butterfly.Walk,
            ["Flutter"] = v6.Butterfly.Flutter
        }
    },
    ["Disco Bee"] = {
        ["Description"] = "Disco Disco: Occasionally has a chance to turn a nearby fruit into Disco",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Disco Bee"),
        ["DefaultHunger"] = 25000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, -3.141592653589793),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://139406192899443",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 12000000,
        ["Rarity"] = "Divine",
        ["Passives"] = { "Disco Disco" },
        ["YHeightScaler"] = 0.03,
        ["Animations"] = {
            ["Idle"] = v6.DiscoBee.Idle,
            ["Walk"] = v6.DiscoBee.Walk,
            ["Dance"] = v6.DiscoBee.Dance
        }
    },
    ["Cooked Owl"] = {
        ["Description"] = "Let Him Cook: Occasionaly burns or cook a random nearby fruit & King of the Grill: Grants bonus experience per second gain to all active pets. Also very tasty!",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Cooked Owl"),
        ["ToolWeldOffset"] = true,
        ["DefaultHunger"] = 50000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://78954652883059",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 3000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Let Him Cook", "King of the Grill", "Movement Variation" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v6.CookedOwl.Idle,
            ["Walk"] = v6.CookedOwl.Walk,
            ["Fly"] = v6.CookedOwl.Fly,
            ["FlyUp"] = v6.CookedOwl.FlyUp,
            ["FlyDown"] = v6.CookedOwl.FlyDown
        }
    },
    ["Pack Bee"] = {
        ["Description"] = "Pack Bee: Increases backpack size by 25 and occasionally pollinates nearby fruit",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Pack Bee"),
        ["DefaultHunger"] = 25000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, -3.141592653589793),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://105775306251306",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 2500000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Pollinator", "Pack Bee" },
        ["YHeightScaler"] = 0.5,
        ["Animations"] = {
            ["Idle"] = v6.Bee.Idle,
            ["Walk"] = v6.Bee.Walk
        }
    },
    ["Starfish"] = {
        ["Description"] = "You\'re a Star: Gains additional XP per second",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Starfish"),
        ["DefaultHunger"] = 1500,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://120520383369074",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 0.25,
        ["SellPrice"] = 50000,
        ["Rarity"] = "Common",
        ["Passives"] = { "You\'re a Star" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v6.Starfish.Idle,
            ["Walk"] = v6.Starfish.Walk
        },
        ["States"] = {}
    },
    ["Crab"] = {
        ["Description"] = "Pinch Pocket: Occasionally goes to another player and pinches them and grants you a small amount of sheckles",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Crab"),
        ["DefaultHunger"] = 3000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://73259620945174",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 4,
        ["SellPrice"] = 60000,
        ["Rarity"] = "Common",
        ["Passives"] = { "Pinch Pocket" },
        ["YHeightScaler"] = 0.8,
        ["Animations"] = {
            ["Idle"] = v6.Crab.Idle,
            ["Walk"] = v6.Crab.Walk,
            ["Pinch"] = v6.Crab.Pinch
        },
        ["States"] = {}
    },
    ["Seagull"] = {
        ["Description"] = "Scavenger: Shoveling plants have a chance to drop the equivalent seed. Does not work on fruits",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Seagull"),
        ["DefaultHunger"] = 3500,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://125267211322255",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 7,
        ["SellPrice"] = 70000,
        ["Rarity"] = "Common",
        ["Passives"] = { "Movement Variation" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v6.Seagull.Idle,
            ["Walk"] = v6.Seagull.Walk,
            ["Fly"] = v6.Seagull.Fly,
            ["FlyUp"] = v6.Seagull.FlyUp,
            ["FlyDown"] = v6.Seagull.FlyDown
        },
        ["States"] = {}
    },
    ["Toucan"] = {
        ["Description"] = "Tropical Lover: Makes all nearby Tropical type plants have increased variant chance and grow bigger",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Toucan"),
        ["DefaultHunger"] = 9000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://118598422473758",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 7,
        ["SellPrice"] = 880000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Tropical Lover", "Tropical Blessing", "Movement Variation" },
        ["YHeightScaler"] = 0.9,
        ["Animations"] = {
            ["Idle"] = v6.Toucan.Idle,
            ["Walk"] = v6.Toucan.Walk,
            ["Fly"] = v6.Toucan.Fly,
            ["FlyUp"] = v6.Toucan.FlyUp,
            ["FlyDown"] = v6.Toucan.FlyDown
        }
    },
    ["Flamingo"] = {
        ["Description"] = "Flamboyance: Occasionally stands on one legs and all nearby plants will grow incredibly fast",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Flamingo"),
        ["DefaultHunger"] = 14000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://122573642551827",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 880000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Flamboyance" },
        ["YHeightScaler"] = 2.4,
        ["Animations"] = {
            ["Idle"] = v6.Flamingo.Idle,
            ["Walk"] = v6.Flamingo.Walk,
            ["Stand"] = v6.Flamingo.Stand
        }
    },
    ["Sea Turtle"] = {
        ["Description"] = "Shell Share: Occasionally shares its wisdom to a random active pet granting bonus experience & Water Splash: Occasionally has a chance to Wet a nearby fruit",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Sea Turtle"),
        ["DefaultHunger"] = 22200,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.004,
        ["Icon"] = "rbxassetid://136324651089948",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 1.5,
        ["SellPrice"] = 880000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Shell Share", "Water Splash" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v6.SeaTurtle.Idle,
            ["Walk"] = v6.SeaTurtle.Walk,
            ["Flip"] = v6.SeaTurtle.Flip,
            ["Splash"] = v6.SeaTurtle.Splash
        },
        ["States"] = {}
    },
    ["Seal"] = {
        ["Description"] = "Seal the Deal: When selling pets, has a small chance to get the pet back as its egg equivalent",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Seal"),
        ["DefaultHunger"] = 17000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://70977930937021",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 6,
        ["SellPrice"] = 880000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Seal the Deal" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v6.Seal.Idle,
            ["Walk"] = v6.Seal.Walk
        },
        ["States"] = {}
    },
    ["Orangutan"] = {
        ["Description"] = "Helping Hands: When crafting, each material has a chance for it not to be consumed",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Orangutan"),
        ["DefaultHunger"] = 55000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://91252752916705",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 6,
        ["SellPrice"] = 880000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Helping Hands" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v6.Orangutan.Idle,
            ["Walk"] = v6.Orangutan.Walk
        },
        ["States"] = {}
    },
    ["Peacock"] = {
        ["Description"] = "Utter Beauty: Occasionally fans its feathers and all nearby pets will advance ability cooldowns",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Peacock"),
        ["DefaultHunger"] = 19000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://79434662175672",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Utter Beauty" },
        ["YHeightScaler"] = 0.8,
        ["Animations"] = {
            ["Idle"] = v6.Peacock.Idle,
            ["Walk"] = v6.Peacock.Walk,
            ["Fan"] = v6.Peacock.Fan
        },
        ["States"] = {}
    },
    ["Capybara"] = {
        ["Description"] = "Chill Zone: Nearby pets\' hunger will not go down and they will gain additional xp per second",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Capybara"),
        ["DefaultHunger"] = 30000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://109096250560950",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 5,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Chill Zone" },
        ["YHeightScaler"] = 1.5,
        ["Animations"] = {
            ["Idle"] = v6.Capybara.Idle,
            ["Walk"] = v6.Capybara.Walk
        }
    },
    ["Scarlet Macaw"] = {
        ["Description"] = "Verdant Bird: Occasionally has a chance to mutate nearby fruits Verdant",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Scarlet Macaw"),
        ["DefaultHunger"] = 12000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://103592675269053",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 7,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Verdant Bird", "Movement Variation" },
        ["YHeightScaler"] = 0.75,
        ["Animations"] = {
            ["Idle"] = v6.ScarletMacaw.Idle,
            ["Walk"] = v6.ScarletMacaw.Walk,
            ["Fly"] = v6.ScarletMacaw.Fly,
            ["FlyUp"] = v6.ScarletMacaw.FlyUp,
            ["FlyDown"] = v6.ScarletMacaw.FlyDown
        }
    },
    ["Ostrich"] = {
        ["Description"] = "Eggsperience: Grants pets hatched from eggs an age bonus",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Ostrich"),
        ["DefaultHunger"] = 20000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://85113894132517",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8.5,
        ["SellPrice"] = 4400000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Eggsperience" },
        ["YHeightScaler"] = 2.4,
        ["Animations"] = {
            ["Idle"] = v6.Ostrich.Idle,
            ["Walk"] = v6.Ostrich.Walk
        }
    },
    ["Mimic Octopus"] = {
        ["Description"] = "Mimicry: Mimics and copies an ability from another pet and performs its ability",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Mimic Octopus"),
        ["DefaultHunger"] = 25000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://118831959038511",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 10000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Mimicry" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v6.MimicOctopus.Idle,
            ["Walk"] = v6.MimicOctopus.Walk,
            ["Mimicry"] = v6.MimicOctopus.Mimicry
        },
        ["States"] = {}
    },
    ["Meerkat"] = {
        ["Description"] = "Lookout: Occasionally goes to another pet and does a lookout for it. That pet advances cooldown. Has a chance chance to do it again after each lookout.",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Meerkat"),
        ["DefaultHunger"] = 22000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://111346621796974",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 1100000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Lookout" },
        ["YHeightScaler"] = 1,
        ["Animations"] = {
            ["Idle"] = v6.Meerkat.Idle,
            ["Walk"] = v6.Meerkat.Walk,
            ["Stand"] = v6.Meerkat.Stand
        }
    },
    ["Sand Snake"] = {
        ["Description"] = "Coiled Commerce: Buying from the seed/gear shop has a small chance to duplicate the bought item!",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Sand Snake"),
        ["DefaultHunger"] = 28000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://110203289397199",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 6,
        ["SellPrice"] = 1100000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Coiled Commerce" },
        ["YHeightScaler"] = 0.1,
        ["Animations"] = {
            ["Idle"] = v6.Snake.Idle,
            ["Walk"] = v6.Snake.Walk
        }
    },
    ["Axolotl"] = {
        ["Description"] = "Summer Regeneration: Summer type fruits have a chance to stay after collecting!",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Axolotl"),
        ["DefaultHunger"] = 22000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://115096754077953",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 5,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Summer Regeneration" },
        ["YHeightScaler"] = 0.5,
        ["Animations"] = {
            ["Idle"] = v6.Axolotl.Idle,
            ["Walk"] = v6.Axolotl.Walk
        }
    },
    ["Hyacinth Macaw"] = {
        ["Description"] = "Wiltproof Bird: Occasionally has a chance to mutate nearby fruits Wiltproof",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Hyacinth Macaw"),
        ["DefaultHunger"] = 12000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://118586950763516",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 7,
        ["SellPrice"] = 4400000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Cloudtouched Bird", "Movement Variation" },
        ["YHeightScaler"] = 0.75,
        ["Animations"] = {
            ["Idle"] = v6.ScarletMacaw.Idle,
            ["Walk"] = v6.ScarletMacaw.Walk,
            ["Fly"] = v6.ScarletMacaw.Fly,
            ["FlyUp"] = v6.ScarletMacaw.FlyUp,
            ["FlyDown"] = v6.ScarletMacaw.FlyDown
        }
    },
    ["Fennec Fox"] = {
        ["Description"] = "Sly: Occasionally goes to another player\'s random fruit, has a chance to copy 1 mutation from it and applies it to a random fruit you own.",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Fennec Fox"),
        ["DefaultHunger"] = 35000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://106568248173155",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 16,
        ["SellPrice"] = 7500000,
        ["Rarity"] = "Divine",
        ["Passives"] = { "Sly" },
        ["YHeightScaler"] = 1.1,
        ["Animations"] = {
            ["Idle"] = v6.FennecFox.Idle,
            ["Walk"] = v6.FennecFox.Walk,
            ["Steal"] = v6.FennecFox.Steal
        }
    },
    ["Hamster"] = {
        ["Description"] = "Hamster Wheel: Occasionally runs in a hamster wheel and grants increased crafting speed for a duration",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Hamster"),
        ["DefaultHunger"] = 15000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://95041454621458",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Hamster Wheel" },
        ["YHeightScaler"] = 0.5,
        ["Animations"] = {
            ["Idle"] = v6.Hamster.Idle,
            ["Walk"] = v6.Hamster.Walk,
            ["Run"] = v6.Hamster.Run
        }
    },
    ["Bald Eagle"] = {
        ["Description"] = "Wings of Freedom: Every 7:04m, takes flight and spreads its wings. All eggs advanced their hatch time by 70.4s. There\'s a 7.04% chance for the effect to be multiplied",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Bald Eagle"),
        ["DefaultHunger"] = 15000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://100494018595907",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 5000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Wings of Freedom", "Movement Variation" },
        ["YHeightScaler"] = 2,
        ["Animations"] = {
            ["Idle"] = v6.Eagle.Idle,
            ["Walk"] = v6.Eagle.Walk,
            ["Fly"] = v6.Eagle.Fly,
            ["FlyUp"] = v6.Eagle.FlyUp,
            ["FlyDown"] = v6.Eagle.FlyDown,
            ["Spin"] = v6.Eagle.Spin
        }
    },
    ["Raptor"] = {
        ["Description"] = "Clever Claws: Small chance fruit gets Amber mutation after collecting! & Raptor Dance: Player has increased movement speed",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Raptor"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://133649762905181",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 12,
        ["SellPrice"] = 5000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Clever Claws", "Raptor Dance" },
        ["YHeightScaler"] = 0,
        ["Animations"] = {
            ["Idle"] = v6.Raptor.Idle,
            ["Walk"] = v6.Raptor.Walk
        }
    },
    ["Stegosaurus"] = {
        ["Description"] = "Prehistoric Doubling: Small chance to duplicate harvested fruit & Prehistoric Harvester: Increased chance to duplicate harvested prehistoric type plant",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Stegosaurus"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://115750504063562",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 5000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Prehistoric Doubling", "Prehistoric Harvester" },
        ["YHeightScaler"] = 0,
        ["Animations"] = {
            ["Idle"] = v6.Stegosaurus.Idle,
            ["Walk"] = v6.Stegosaurus.Walk
        }
    },
    ["Triceratops"] = {
        ["Description"] = "Tri-Horn: Rams into random plants and advances their growth",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Triceratops"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://133031079193526",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 5000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Tri-Horn" },
        ["YHeightScaler"] = 0,
        ["Animations"] = {
            ["Idle"] = v6.Triceratops.Idle,
            ["Walk"] = v6.Triceratops.Walk,
            ["Run"] = v6.Triceratops.Run,
            ["Ram"] = v6.Triceratops.Ram
        }
    },
    ["Pterodactyl"] = {
        ["Description"] = "Sky Reptile: Occasionally applies Windstruck mutation to multiple nearby fruits! & Air Time: Player has increased jump height",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Pterodactyl"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://132325249273328",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 10000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Sky Reptile", "Air Time", "Movement Variation" },
        ["YHeightScaler"] = 1,
        ["Animations"] = {
            ["Idle"] = v6.Pterodactyl.Idle,
            ["Walk"] = v6.Pterodactyl.Walk,
            ["Flap"] = v6.Pterodactyl.Flap,
            ["Fly"] = v6.Pterodactyl.Fly,
            ["FlyUp"] = v6.Pterodactyl.FlyUp,
            ["FlyDown"] = v6.Pterodactyl.FlyDown
        }
    },
    ["Brontosaurus"] = {
        ["Description"] = "Giant Incubator: Pets hatched from eggs have higher base weight",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Brontosaurus"),
        ["DefaultHunger"] = 80000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://138431192706334",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 5,
        ["SellPrice"] = 20000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Giant Incubator" },
        ["YHeightScaler"] = 0,
        ["Animations"] = {
            ["Idle"] = v6.Brontosaurus.Idle,
            ["Walk"] = v6.Brontosaurus.Walk
        }
    },
    ["Radioactive Stegosaurus"] = {
        ["Description"] = "Developer RemorsEcoDe\'s pet",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Radioactive Stegosaurus"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://115750504063562",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 5000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Radioactive Lizard" },
        ["YHeightScaler"] = 0,
        ["Animations"] = {
            ["Idle"] = v6.Stegosaurus.Idle,
            ["Walk"] = v6.Stegosaurus.Walk
        }
    },
    ["T-Rex"] = {
        ["Description"] = "Apex Predator: Occasionally eats a random mutation from a fruit in your garden then roars and applies that mutation to other fruits in your garden.",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("T-Rex"),
        ["DefaultHunger"] = 60000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://72024850228702",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 40000000,
        ["Rarity"] = "Divine",
        ["Passives"] = { "Apex Predator" },
        ["YHeightScaler"] = 0.05,
        ["Animations"] = {
            ["Idle"] = v6.Trex.Idle,
            ["Walk"] = v6.Trex.Walk,
            ["Eat"] = v6.Trex.Eat,
            ["Roar"] = v6.Trex.Roar
        }
    },
    ["Parasaurolophus"] = {
        ["Description"] = "Crowbar Head: Occasionally, goes to the cosmetic crate with the highest time and reduces time to open!",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Parasaurolophus"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://77060347493123",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 5000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Crowbar Head" },
        ["YHeightScaler"] = 0.05,
        ["Animations"] = {
            ["Idle"] = v6.Parasaurolophus.Idle,
            ["Walk"] = v6.Parasaurolophus.Walk,
            ["Crowbar"] = v6.Parasaurolophus.Crowbar
        }
    },
    ["Iguanodon"] = {
        ["Description"] = "Dino Herd: Grants bonus experience per second gain to all Dinosaur type active pets",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Iguanodon"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://132997806707299",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 5000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Dino Herd" },
        ["YHeightScaler"] = 0.05,
        ["Animations"] = {
            ["Idle"] = v6.Iguanodon.Idle,
            ["Walk"] = v6.Iguanodon.Walk
        }
    },
    ["Pachycephalosaurus"] = {
        ["Description"] = "Crafty Dome: Grants a small chance to duplicate the crafted item.",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Pachycephalosaurus"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://98967783170808",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 5000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Crafty Dome" },
        ["YHeightScaler"] = 0.05,
        ["Animations"] = {
            ["Idle"] = v6.Pachycephalosaurus.Idle,
            ["Walk"] = v6.Pachycephalosaurus.Walk
        }
    },
    ["Dilophosaurus"] = {
        ["Description"] = "Frilled Reptile: Occasionally opens its frills and spits out venom! The venom spreads to other random pets, advancing cooldown OR granting bonus xp",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Dilophosaurus"),
        ["DefaultHunger"] = 30000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://88442192911950",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 10000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Frilled Reptile" },
        ["YHeightScaler"] = 0.05,
        ["Animations"] = {
            ["Idle"] = v6.Dilophosaurus.Idle,
            ["Walk"] = v6.Dilophosaurus.Walk,
            ["Frill"] = v6.Dilophosaurus.Frill
        }
    },
    ["Ankylosaurus"] = {
        ["Description"] = "Armored Defender: When a player steals a fruit from you, grants a chance you get the stolen fruit as well.",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Ankylosaurus"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://128962631009648",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 20000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Armored Defender" },
        ["YHeightScaler"] = 0.05,
        ["Animations"] = {
            ["Idle"] = v6.Ankylosaurus.Idle,
            ["Walk"] = v6.Ankylosaurus.Walk
        }
    },
    ["Spinosaurus"] = {
        ["Description"] = "Occasionally, devours a random mutation from random fruits in your garden each, roars and applies it to 1 other random fruit in your garden!",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Spinosaurus"),
        ["DefaultHunger"] = 25000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://78132119445447",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 40000000,
        ["Rarity"] = "Divine",
        ["Passives"] = { "Food Chain" },
        ["YHeightScaler"] = 0.05,
        ["Animations"] = {
            ["Idle"] = v6.Spinosaurus.Idle,
            ["Walk"] = v6.Spinosaurus.Walk,
            ["Eat"] = v6.Spinosaurus.Eat,
            ["Roar"] = v6.Spinosaurus.Roar
        }
    },
    ["Rainbow Parasaurolophus"] = {
        ["Description"] = "Crowbar Head: Occasionally, goes to the cosmetic crate with the highest time and reduces time to open!",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Rainbow Parasaurolophus"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://116062422658499",
        ["OddsIcon"] = "rbxassetid://125458280085952",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 10000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Rainbow Crowbar Head" },
        ["YHeightScaler"] = 0.05,
        ["Animations"] = {
            ["Idle"] = v6.Parasaurolophus.Idle,
            ["Walk"] = v6.Parasaurolophus.Walk,
            ["Crowbar"] = v6.Parasaurolophus.Crowbar
        }
    },
    ["Rainbow Iguanodon"] = {
        ["Description"] = "Dino Herd: Grants bonus experience per second gain to all Dinosaur type active pets",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Rainbow Iguanodon"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://70960389100537",
        ["OddsIcon"] = "rbxassetid://94572344496310",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 10000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Rainbow Dino Herd" },
        ["YHeightScaler"] = 0.05,
        ["Animations"] = {
            ["Idle"] = v6.Iguanodon.Idle,
            ["Walk"] = v6.Iguanodon.Walk
        }
    },
    ["Rainbow Pachycephalosaurus"] = {
        ["Description"] = "Crafty Dome: Grants a small chance to duplicate the crafted item.",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Rainbow Pachycephalosaurus"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://71353461716145",
        ["OddsIcon"] = "rbxassetid://104196332322283",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 10000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Rainbow Crafty Dome" },
        ["YHeightScaler"] = 0.05,
        ["Animations"] = {
            ["Idle"] = v6.Pachycephalosaurus.Idle,
            ["Walk"] = v6.Pachycephalosaurus.Walk
        }
    },
    ["Rainbow Dilophosaurus"] = {
        ["Description"] = "Frilled Reptile: Occasionally opens its frills and spits out venom! The venom spreads to other random pets, advancing cooldown OR granting bonus xp",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Rainbow Dilophosaurus"),
        ["DefaultHunger"] = 30000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://114260890723408",
        ["OddsIcon"] = "rbxassetid://92472270160849",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 20000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Rainbow Frilled Reptile" },
        ["YHeightScaler"] = 0.05,
        ["Animations"] = {
            ["Idle"] = v6.Dilophosaurus.Idle,
            ["Walk"] = v6.Dilophosaurus.Walk,
            ["Frill"] = v6.Dilophosaurus.Frill
        }
    },
    ["Rainbow Ankylosaurus"] = {
        ["Description"] = "Armored Defender: When a player steals a fruit from you, grants a chance you get the stolen fruit as well.",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Rainbow Ankylosaurus"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://108330251202915",
        ["OddsIcon"] = "rbxassetid://96359333884841",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 40000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Rainbow Armored Defender" },
        ["YHeightScaler"] = 0.05,
        ["Animations"] = {
            ["Idle"] = v6.Ankylosaurus.Idle,
            ["Walk"] = v6.Ankylosaurus.Walk
        }
    },
    ["Rainbow Spinosaurus"] = {
        ["Description"] = "Occasionally, devours a random mutation from random fruits in your garden each, roars and applies it to 1 other random fruit in your garden!",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Rainbow Spinosaurus"),
        ["DefaultHunger"] = 25000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://98134533729834",
        ["OddsIcon"] = "rbxassetid://132599364727436",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 80000000,
        ["Rarity"] = "Divine",
        ["Passives"] = { "Rainbow Food Chain" },
        ["YHeightScaler"] = 0.05,
        ["Animations"] = {
            ["Idle"] = v6.Spinosaurus.Idle,
            ["Walk"] = v6.Spinosaurus.Walk,
            ["Eat"] = v6.Spinosaurus.Eat,
            ["Roar"] = v6.Spinosaurus.Roar
        }
    },
    ["Shiba Inu"] = {
        ["Description"] = "Man\'s Best Tomodachi: Occasionally digs up a random seed at a higher chance",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Shiba Inu"),
        ["DefaultHunger"] = 2000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://109495487067387",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 50000,
        ["Rarity"] = "Uncommon",
        ["Passives"] = { "Man\'s Best Tomodachi" },
        ["YHeightScaler"] = 1.5,
        ["Animations"] = {
            ["Idle"] = v6.ShibaInu.Idle,
            ["Walk"] = v6.ShibaInu.Walk,
            ["Play"] = v6.ShibaInu.Play,
            ["Dig"] = v6.ShibaInu.Dig
        },
        ["States"] = {}
    },
    ["Nihonzaru"] = {
        ["Description"] = "Bathe Time: As long as you have a Hot Spring in your garden: bathes in it and boosts all other pet\'s passives by a small amount",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Nihonzaru"),
        ["DefaultHunger"] = 8000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://83549828969544",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 250000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Bathe Time" },
        ["YHeightScaler"] = 1,
        ["Animations"] = {
            ["Idle"] = v6.Nihonzaru.Idle,
            ["Walk"] = v6.Nihonzaru.Walk,
            ["Bathe"] = v6.Nihonzaru.Bathe
        },
        ["States"] = {}
    },
    ["Tanuki"] = {
        ["Description"] = "Mischief: Occasionally causes mischief doing random actions in your garden",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Tanuki"),
        ["DefaultHunger"] = 15000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://86675114036925",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 8,
        ["SellPrice"] = 550000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Mischief" },
        ["YHeightScaler"] = 0.8,
        ["Animations"] = {
            ["Idle"] = v6.Tanuki.Idle,
            ["Walk"] = v6.Tanuki.Walk,
            ["Mischief"] = v6.Tanuki.Mischief
        },
        ["States"] = {}
    },
    ["Tanchozuru"] = {
        ["Description"] = "Balance and Harmony: Occasionally channels tranquility and has a chance to mutate nearby fruits into Tranquil",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Tanchozuru"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://102876710012857",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 7,
        ["SellPrice"] = 10000000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Balance and Harmony" },
        ["YHeightScaler"] = 0.01,
        ["Animations"] = {
            ["Idle"] = v6.Tanchozuru.Idle,
            ["Walk"] = v6.Tanchozuru.Walk,
            ["Wings"] = v6.Tanchozuru.Wings
        },
        ["States"] = {}
    },
    ["Kappa"] = {
        ["Description"] = "Water Spirit: Occasionally sprays water on all nearby fruits, mutating them to wet, and has a chance to convert Wet mutations already on fruit to Bloodlit",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Kappa"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://139605839925044",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 25000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Water Spirit" },
        ["YHeightScaler"] = 1.1,
        ["Animations"] = {
            ["Idle"] = v6.Kappa.Idle,
            ["Walk"] = v6.Kappa.Walk,
            ["Magic"] = v6.Kappa.Magic
        },
        ["States"] = {}
    },
    ["Kitsune"] = {
        ["Description"] = "Nine-Tailed Myth: Occasionally goes to other player\'s fruit, mutates with Chakra or Foxfire Chakra and steals (duplicates) hands it to you",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Kitsune"),
        ["DefaultHunger"] = 49999,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://135428084676433",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 99999999,
        ["Rarity"] = "Prismatic",
        ["Passives"] = { "Nine-Tailed Myth" },
        ["YHeightScaler"] = 0.01,
        ["Animations"] = {
            ["Idle"] = v6.Kitsune.Idle,
            ["Walk"] = v6.Kitsune.Walk,
            ["Spin"] = v6.Kitsune.Spin
        },
        ["States"] = {}
    },
    ["Koi"] = {
        ["Description"] = "Fish of Fortune: Grants a chance to recover an egg when hatching. Can be different colors",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Koi"),
        ["DefaultHunger"] = 2500,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://112819490327372",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 6,
        ["SellPrice"] = 10000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Fish of Fortune" },
        ["YHeightScaler"] = 1.5,
        ["Animations"] = {
            ["Idle"] = v6.Koi.Idle,
            ["Walk"] = v6.Koi.Walk,
            ["Splash"] = v6.Koi.Splash
        },
        ["States"] = {}
    },
    ["Football"] = {
        ["Description"] = "Touchdown: Occasionally runs to the Gear Shop or Seed Shop (whichever is farther) and does a touchdown which awards you with sheckles or a Watering Can",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Football"),
        ["DefaultHunger"] = 870,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://97868319630227",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 7,
        ["SellPrice"] = 650000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Touchdown" },
        ["YHeightScaler"] = 1,
        ["Animations"] = {
            ["Idle"] = v6.Football.Idle,
            ["Walk"] = v6.Football.Walk,
            ["Touchdown"] = v6.Football.Touchdown
        },
        ["States"] = {}
    },
    ["Maneki-neko"] = {
        ["Description"] = "Fortune Cat:\tOccasionally does a wave of good luck and grants increased chance to get your fruit back after selling it",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Maneki-neko"),
        ["DefaultHunger"] = 1777,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://116049439913569",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 7,
        ["SellPrice"] = 50000,
        ["Rarity"] = "Uncommon",
        ["Passives"] = { "Fortune Cat" },
        ["YHeightScaler"] = 0.8,
        ["Animations"] = {
            ["Idle"] = v6.Cat.Idle,
            ["Walk"] = v6.Cat.Walk,
            ["Wave"] = v6.Neko.Wave
        },
        ["States"] = {}
    },
    ["Tsuchinoko"] = {
        ["Description"] = "Fat Snake: Increased lucky harvest chance!",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Tsuchinoko"),
        ["DefaultHunger"] = 28000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://104799415228364",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 3,
        ["SellPrice"] = 250000,
        ["Rarity"] = "Rare",
        ["Passives"] = { "Fat Snake" },
        ["YHeightScaler"] = 0.01,
        ["Animations"] = {
            ["Idle"] = v6.Tsuchinoko.Idle,
            ["Walk"] = v6.Tsuchinoko.Walk
        },
        ["States"] = {}
    },
    ["Kodama"] = {
        ["Description"] = "Tree Spirit: Collecting Zen type fruits have a chance to mutate with Tranquil.",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Kodama"),
        ["DefaultHunger"] = 22000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://123076615425263",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 550000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Tree Spirit" },
        ["YHeightScaler"] = 1.2,
        ["Animations"] = {
            ["Idle"] = v6.Kodama.Idle,
            ["Walk"] = v6.Kodama.Walk,
            ["Dance"] = v6.Kodama.Dance
        },
        ["States"] = {}
    },
    ["Corrupted Kodama"] = {
        ["Description"] = "Corrupted Tree Spirit: Collecting Zen type fruits have a chance to mutate with Corrupt.",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Corrupted Kodama"),
        ["DefaultHunger"] = 22000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://131755084333898",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 550000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Corrupted Tree Spirit" },
        ["YHeightScaler"] = 1.2,
        ["Animations"] = {
            ["Idle"] = v6.Kodama.Idle,
            ["Walk"] = v6.Kodama.Walk,
            ["Dance"] = v6.Kodama.Dance
        },
        ["States"] = {}
    },
    ["Raiju"] = {
        ["Description"] = "Lightning Beast: Occasionally devours a fruit with Shocked for bonus value, spits a chain lightning that mutates fruit with Static or Shocked if its a Thunderstorm",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Raiju"),
        ["DefaultHunger"] = 42000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://111087166681850",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 10000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Lightning Beast" },
        ["YHeightScaler"] = 0.01,
        ["Animations"] = {
            ["Idle"] = v6.Raiju.Idle,
            ["Walk"] = v6.Raiju.Walk,
            ["Eat"] = v6.Raiju.Eat,
            ["Spit"] = v6.Raiju.Spit
        },
        ["States"] = {}
    },
    ["Mizuchi"] = {
        ["Description"] = "Water Diety: When selling fruits with Wet/Drenched mutation, there is chance a random mutation from that fruit will be applied to a fruit in your garden!",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Mizuchi"),
        ["DefaultHunger"] = 40000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://91185943483703",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flying",
        ["MovementSpeed"] = 6,
        ["SellPrice"] = 25000000,
        ["Rarity"] = "Divine",
        ["Passives"] = { "Water Diety" },
        ["YHeightScaler"] = 1,
        ["Animations"] = {
            ["Idle"] = v6.Mizuchi.Idle,
            ["Walk"] = v6.Mizuchi.Walk
        },
        ["States"] = {}
    },
    ["Corrupted Kitsune"] = {
        ["Description"] = "Nine-Tailed Curse: Occasionally, Removes 9 mutations from 9 different fruit. Applies Corrupted Chakra with a very rare chance for Corrupted Foxfire Chakra to 1 random fruit.",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Corrupted Kitsune"),
        ["DefaultHunger"] = 49999,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://73884777977436",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 99999999,
        ["Rarity"] = "Prismatic",
        ["Passives"] = { "Nine-Tailed Curse" },
        ["YHeightScaler"] = 0.01,
        ["Animations"] = {
            ["Idle"] = v6.Kitsune.Idle,
            ["Walk"] = v6.Kitsune.Walk,
            ["Spin"] = v6.Kitsune.Spin
        },
        ["States"] = {}
    },
    ["Rainbow Maneki-neko"] = {
        ["Description"] = "Rainbow Fortune Cat:\tOccasionally does a wave of good luck and grants increased chance to get your fruit back after selling it",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Rainbow Maneki-neko"),
        ["DefaultHunger"] = 1777,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://127837229475229",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 7,
        ["SellPrice"] = 100000,
        ["Rarity"] = "Uncommon",
        ["Passives"] = { "Rainbow Fortune Cat" },
        ["YHeightScaler"] = 0.8,
        ["Animations"] = {
            ["Idle"] = v6.Cat.Idle,
            ["Walk"] = v6.Cat.Walk,
            ["Wave"] = v6.Neko.Wave
        },
        ["States"] = {}
    },
    ["Rainbow Kodama"] = {
        ["Description"] = "Rainbow Tree Spirit: Collecting Zen type fruits have a chance to mutate with Tranquil.",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Rainbow Kodama"),
        ["DefaultHunger"] = 22000,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://130281044212300",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 1100000,
        ["Rarity"] = "Legendary",
        ["Passives"] = { "Rainbow Tree Spirit" },
        ["YHeightScaler"] = 1.2,
        ["Animations"] = {
            ["Idle"] = v6.Kodama.Idle,
            ["Walk"] = v6.Kodama.Walk,
            ["Dance"] = v6.Kodama.Dance
        },
        ["States"] = {}
    },
    ["Rainbow Corrupted Kitsune"] = {
        ["Description"] = "Rainbow Nine-Tailed Curse: Occasionally, Removes 9 mutations from 9 different fruit. Applies Corrupted Chakra with a very rare chance for Corrupted Foxfire Chakra to 1 random fruit.",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Rainbow Corrupted Kitsune"),
        ["DefaultHunger"] = 49999,
        ["WeldOffset"] = CFrame.Angles(0, 0, 0),
        ["ToolWeldOffset"] = true,
        ["TwoHanded"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://112845542239850",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Grounded",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 199,
        999,
        998,
        ["Rarity"] = "Prismatic",
        ["Passives"] = { "Rainbow Nine-Tailed Curse" },
        ["YHeightScaler"] = 0.01,
        ["Animations"] = {
            ["Idle"] = v6.Kitsune.Idle,
            ["Walk"] = v6.Kitsune.Walk,
            ["Spin"] = v6.Kitsune.Spin
        },
        ["States"] = {}
    },
    ["Firefly"] = {
        ["Description"] = "Lightning Bug: Occasionally strikes a random nearby fruit, with a small chance of turning it Shocked",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Firefly"),
        ["DefaultHunger"] = 25000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966),
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://131310748087635",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 3300000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Lightning Bug" },
        ["YHeightScaler"] = 0.5,
        ["Animations"] = {
            ["Idle"] = v6.Firefly.Idle,
            ["Walk"] = v6.Firefly.Walk
        }
    },
    ["Red Dragon"] = {
        ["Description"] = "Scorched Soil: Occasionally sets a random nearby fruit ablaze, turning it into Burnt",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Red Dragon"),
        ["DefaultHunger"] = 10000,
        ["WeldOffset"] = CFrame.Angles(0, -1.5707963267948966, 0),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.004,
        ["Icon"] = "rbxassetid://140223014467344",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 10,
        ["SellPrice"] = 100000,
        ["Rarity"] = "Common",
        ["Passives"] = { "Scorched Soil" },
        ["YHeightScaler"] = 0.2,
        ["Animations"] = {}
    },
    ["Golden Bee"] = {
        ["Description"] = "Pollinator: Occasionally pollinates fruit & Golden Harvest: Harvested fruit has a chance to turn to gold",
        ["Actions"] = {},
        ["Model"] = v5:FindFirstChild("Bee"),
        ["Variant"] = "Golden Bee",
        ["DefaultHunger"] = 25000,
        ["WeldOffset"] = CFrame.Angles(0, 1.5707963267948966, -3.141592653589793),
        ["ToolWeldOffset"] = true,
        ["ModelScalePerLevel"] = 0.005,
        ["Icon"] = "rbxassetid://125658111834879",
        ["HungerFruitMultipliers"] = {},
        ["MovementType"] = "Flight",
        ["MovementSpeed"] = 9,
        ["SellPrice"] = 1000000,
        ["Rarity"] = "Mythical",
        ["Passives"] = { "Pollinator", "Golden Harvest" },
        ["YHeightScaler"] = 0.03,
        ["Animations"] = {
            ["Idle"] = v6.Bee.Idle,
            ["Walk"] = v6.Bee.Walk,
            ["Pollinate"] = v6.Bee.Pollinate
        }
    }
}
for v8, v9 in v7 do
    if not v9.Model and v3:IsServer() then
        warn(v8, "has no attached model defaulting to Dog model")
        v9.Model = v5:FindFirstChild("Dog")
    end
    if v9.Model and v9.Model.Name ~= v8 then
        local v10 = v9.Model:Clone()
        v10.Name = v8
        v10.Parent = v5
    end
    v9.YHeightScaler = v9.YHeightScaler or 0
    v9.States = v9.States or {}
end
return v7
