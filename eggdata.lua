-- Script Path: game:GetService("ReplicatedStorage").Data.PetRegistry.PetEggs
-- Took 0.03s to decompile.
-- Executor: <PERSON> (1.0.0)

local v1 = {}
local v2 = {
    ["Color"] = Color3.fromRGB(163, 120, 45),
    ["HatchTime"] = 14400
}
local v3 = {}
local v4 = {}
local v5 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v4["Silver Monkey"] = v5
local v6 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v4.Cow = v6
local v7 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v4["Sea Otter"] = v7
local v8 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v4.Turtle = v8
local v9 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v4["Polar Bear"] = v9
v3.Items = v4
v2.RarityData = v3
v1["Legendary Egg"] = v2
local v10 = {
    ["Color"] = Color3.fromRGB(33, 84, 185),
    ["HatchTime"] = 7200
}
local v11 = {}
local v12 = {}
local v13 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v12["Orange Tabby"] = v13
local v14 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v12.Monkey = v14
local v15 = {
    ["ItemOdd"] = 15,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v12["Spotted Deer"] = v15
local v16 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v12.Rooster = v16
local v17 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v12.Pig = v17
v11.Items = v12
v10.RarityData = v11
v1["Rare Egg"] = v10
local v18 = {
    ["Color"] = Color3.fromRGB(211, 167, 129),
    ["HatchTime"] = 1200
}
local v19 = {}
local v20 = {}
local v21 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v20["Black Bunny"] = v21
local v22 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v20.Cat = v22
local v23 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v20.Deer = v23
local v24 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v20.Chicken = v24
v19.Items = v20
v18.RarityData = v19
v1["Uncommon Egg"] = v18
local v25 = {
    ["Color"] = Color3.fromRGB(255, 255, 255),
    ["HatchTime"] = 600,
    ["Icon"] = "rbxassetid://88557327752325"
}
local v26 = {}
local v27 = {}
local v28 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v27.Dog = v28
local v29 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v27["Golden Lab"] = v29
local v30 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v27.Bunny = v30
v26.Items = v27
v25.RarityData = v26
v1["Common Egg"] = v25
local v31 = {
    ["Color"] = Color3.fromRGB(255, 204, 0),
    ["HatchTime"] = 18400,
    ["Icon"] = "rbxassetid://83210224500991"
}
local v32 = {}
local v33 = {}
local v34 = {
    ["ItemOdd"] = 36,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v33["Grey Mouse"] = v34
local v35 = {
    ["ItemOdd"] = 27,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v33.Squirrel = v35
local v36 = {
    ["ItemOdd"] = 27,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v33["Brown Mouse"] = v36
local v37 = {
    ["ItemOdd"] = 8.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v33["Red Giant Ant"] = v37
local v38 = {
    ["ItemOdd"] = 1.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v33["Red Fox"] = v38
v32.Items = v33
v31.RarityData = v32
v1["Mythical Egg"] = v31
local v39 = {
    ["Color"] = Color3.fromRGB(213, 255, 134),
    ["HatchTime"] = 28800,
    ["Icon"] = "rbxassetid://83970205286930"
}
local v40 = {}
local v41 = {}
local v42 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v41.Dragonfly = v42
local v43 = {
    ["ItemOdd"] = 4,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v41["Praying Mantis"] = v43
local v44 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v41["Giant Ant"] = v44
local v45 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v41.Snail = v45
local v46 = {
    ["ItemOdd"] = 40,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v41.Caterpillar = v46
v40.Items = v41
v39.RarityData = v40
v1["Bug Egg"] = v39
local v47 = {
    ["Color"] = Color3.fromRGB(213, 255, 134),
    ["HatchTime"] = 30
}
local v48 = {}
local v49 = {}
local v50 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v49.Dragonfly = v50
local v51 = {
    ["ItemOdd"] = 4,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v49["Praying Mantis"] = v51
local v52 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v49.Caterpillar = v52
local v53 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v49["Giant Ant"] = v53
local v54 = {
    ["ItemOdd"] = 40,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v49.Snail = v54
v48.Items = v49
v47.RarityData = v48
v1["Exotic Bug Egg"] = v47
local v55 = {
    ["Color"] = Color3.fromRGB(170, 170, 255),
    ["HatchTime"] = 15000
}
local v56 = {}
local v57 = {}
local v58 = {
    ["ItemOdd"] = 0.1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57.Raccoon = v58
local v59 = {
    ["ItemOdd"] = 3,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57["Night Owl"] = v59
local v60 = {
    ["ItemOdd"] = 7,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57["Echo Frog"] = v60
local v61 = {
    ["ItemOdd"] = 15,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57.Frog = v61
local v62 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57.Mole = v62
local v63 = {
    ["ItemOdd"] = 40,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v57.Hedgehog = v63
v56.Items = v57
v55.RarityData = v56
v1["Night Egg"] = v55
local v64 = {
    ["Color"] = Color3.fromRGB(255, 170, 0),
    ["HatchTime"] = 15000,
    ["Icon"] = "rbxassetid://100313281527054"
}
local v65 = {}
local v66 = {}
local v67 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66["Queen Bee"] = v67
local v68 = {
    ["ItemOdd"] = 4,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66["Petal Bee"] = v68
local v69 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66["Bear Bee"] = v69
local v70 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66["Honey Bee"] = v70
local v71 = {
    ["ItemOdd"] = 65,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v66.Bee = v71
v65.Items = v66
v64.RarityData = v65
v1["Bee Egg"] = v64
local v72 = {
    ["Color"] = Color3.fromRGB(255, 0, 0),
    ["HatchTime"] = 15000,
    ["Icon"] = "rbxassetid://112867748937791"
}
local v73 = {}
local v74 = {}
local v75 = {
    ["ItemOdd"] = 0.25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v74["Disco Bee"] = v75
local v76 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v74.Butterfly = v76
local v77 = {
    ["ItemOdd"] = 13.75,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v74.Moth = v77
local v78 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v74["Tarantula Hawk"] = v78
local v79 = {
    ["ItemOdd"] = 55,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v74.Wasp = v79
v73.Items = v74
v72.RarityData = v73
v1["Anti Bee Egg"] = v72
local v80 = {
    ["Color"] = Color3.fromRGB(255, 0, 0),
    ["HatchTime"] = 30,
    ["Icon"] = "rbxassetid://112867748937791"
}
local v81 = {}
local v82 = {}
local v83 = {
    ["ItemOdd"] = 0.25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v82["Disco Bee"] = v83
local v84 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v82.Butterfly = v84
local v85 = {
    ["ItemOdd"] = 13.75,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v82.Moth = v85
local v86 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v82["Tarantula Hawk"] = v86
local v87 = {
    ["ItemOdd"] = 55,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v82.Wasp = v87
v81.Items = v82
v80.RarityData = v81
v1["Premium Anti Bee Egg"] = v80
local v88 = {
    ["Color"] = Color3.fromRGB(170, 170, 255),
    ["HatchTime"] = 30
}
local v89 = {}
local v90 = {}
local v91 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v90.Raccoon = v91
local v92 = {
    ["ItemOdd"] = 3,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v90["Night Owl"] = v92
local v93 = {
    ["ItemOdd"] = 7,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v90["Echo Frog"] = v93
local v94 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v90.Frog = v94
local v95 = {
    ["ItemOdd"] = 16,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v90.Mole = v95
local v96 = {
    ["ItemOdd"] = 35,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v90.Hedgehog = v96
v89.Items = v90
v88.RarityData = v89
v1["Premium Night Egg"] = v88
local v97 = {
    ["Color"] = Color3.fromRGB(255, 255, 0),
    ["HatchTime"] = 1200,
    ["Icon"] = "rbxassetid://119578067639695"
}
local v98 = {}
local v99 = {}
local v100 = {
    ["ItemOdd"] = 50,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v99.Starfish = v100
local v101 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v99.Crab = v101
local v102 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v99.Seagull = v102
v98.Items = v99
v97.RarityData = v98
v1["Common Summer Egg"] = v97
local v103 = {
    ["Color"] = Color3.fromRGB(170, 255, 255),
    ["HatchTime"] = 14400,
    ["Icon"] = "rbxassetid://86932815278005"
}
local v104 = {}
local v105 = {}
local v106 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v105.Flamingo = v106
local v107 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v105.Toucan = v107
local v108 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v105["Sea Turtle"] = v108
local v109 = {
    ["ItemOdd"] = 15,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v105.Orangutan = v109
local v110 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v105.Seal = v110
v104.Items = v105
v103.RarityData = v104
v1["Rare Summer Egg"] = v103
local v111 = {
    ["Color"] = Color3.fromRGB(255, 205, 50),
    ["HatchTime"] = 24000,
    ["Icon"] = "rbxassetid://105428605316441"
}
local v112 = {}
local v113 = {}
local v114 = {
    ["ItemOdd"] = 40,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v113.Ostrich = v114
local v115 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v113.Peacock = v115
local v116 = {
    ["ItemOdd"] = 21,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v113.Capybara = v116
local v117 = {
    ["ItemOdd"] = 8,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v113["Scarlet Macaw"] = v117
local v118 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v113["Mimic Octopus"] = v118
v112.Items = v113
v111.RarityData = v112
v1["Paradise Egg"] = v111
local v119 = {
    ["Color"] = Color3.fromRGB(255, 205, 119),
    ["HatchTime"] = 15000,
    ["Icon"] = "rbxassetid://76221946588708"
}
local v120 = {}
local v121 = {}
local v122 = {
    ["ItemOdd"] = 45,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v121.Meerkat = v122
local v123 = {
    ["ItemOdd"] = 34.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v121["Sand Snake"] = v123
local v124 = {
    ["ItemOdd"] = 15,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v121.Axolotl = v124
local v125 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v121["Hyacinth Macaw"] = v125
local v126 = {
    ["ItemOdd"] = 0.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v121["Fennec Fox"] = v126
v120.Items = v121
v119.RarityData = v120
v1["Oasis Egg"] = v119
local v127 = {
    ["Color"] = Color3.fromRGB(255, 205, 119),
    ["HatchTime"] = 30,
    ["Icon"] = "rbxassetid://76221946588708"
}
local v128 = {}
local v129 = {}
local v130 = {
    ["ItemOdd"] = 45,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v129.Meerkat = v130
local v131 = {
    ["ItemOdd"] = 34.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v129["Sand Snake"] = v131
local v132 = {
    ["ItemOdd"] = 15,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v129.Axolotl = v132
local v133 = {
    ["ItemOdd"] = 5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v129["Hyacinth Macaw"] = v133
local v134 = {
    ["ItemOdd"] = 0.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v129["Fennec Fox"] = v134
v128.Items = v129
v127.RarityData = v128
v1["Premium Oasis Egg"] = v127
local v135 = {
    ["Color"] = Color3.fromRGB(85, 170, 127),
    ["HatchTime"] = 15000,
    ["Icon"] = "rbxassetid://71688868083095"
}
local v136 = {}
local v137 = {}
local v138 = {
    ["ItemOdd"] = 35,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v137.Raptor = v138
local v139 = {
    ["ItemOdd"] = 32.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v137.Triceratops = v139
local v140 = {
    ["ItemOdd"] = 28,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v137.Stegosaurus = v140
local v141 = {
    ["ItemOdd"] = 3,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v137.Pterodactyl = v141
local v142 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v137.Brontosaurus = v142
local v143 = {
    ["ItemOdd"] = 0.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v137["T-Rex"] = v143
v136.Items = v137
v135.RarityData = v136
v1["Dinosaur Egg"] = v135
local v144 = {
    ["Color"] = Color3.fromRGB(85, 170, 0),
    ["HatchTime"] = 15000,
    ["Icon"] = "rbxassetid://85559070844097"
}
local v145 = {}
local v146 = {}
local v147 = {
    ["ItemOdd"] = 35,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v146.Parasaurolophus = v147
local v148 = {
    ["ItemOdd"] = 32.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v146.Iguanodon = v148
local v149 = {
    ["ItemOdd"] = 28,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v146.Pachycephalosaurus = v149
local v150 = {
    ["ItemOdd"] = 3,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v146.Dilophosaurus = v150
local v151 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v146.Ankylosaurus = v151
local v152 = {
    ["ItemOdd"] = 0.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v146.Spinosaurus = v152
v145.Items = v146
v144.RarityData = v145
v1["Primal Egg"] = v144
local v153 = {
    ["Color"] = Color3.fromRGB(85, 170, 0),
    ["HatchTime"] = 30,
    ["Icon"] = "rbxassetid://85559070844097"
}
local v154 = {}
local v155 = {}
local v156 = {
    ["ItemOdd"] = 34,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v155.Parasaurolophus = v156
local v157 = {
    ["ItemOdd"] = 32.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v155.Iguanodon = v157
local v158 = {
    ["ItemOdd"] = 28,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v155.Pachycephalosaurus = v158
local v159 = {
    ["ItemOdd"] = 3,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v155.Dilophosaurus = v159
local v160 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v155.Ankylosaurus = v160
local v161 = {
    ["ItemOdd"] = 0.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v155.Spinosaurus = v161
local v162 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 1, 1 },
        ["HugeChance"] = 0
    }
}
v155["Egg/Rainbow Premium Primal Egg"] = v162
v154.Items = v155
v153.RarityData = v154
v1["Premium Primal Egg"] = v153
local v163 = {
    ["Color"] = Color3.fromRGB(255, 255, 255),
    ["HatchTime"] = 15000,
    ["Icon"] = "rbxassetid://109901373212167"
}
local v164 = {}
local v165 = {}
local v166 = {
    ["ItemOdd"] = 40,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v165["Shiba Inu"] = v166
local v167 = {
    ["ItemOdd"] = 31,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v165.Nihonzaru = v167
local v168 = {
    ["ItemOdd"] = 20.82,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v165.Tanuki = v168
local v169 = {
    ["ItemOdd"] = 4.6,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v165.Tanchozuru = v169
local v170 = {
    ["ItemOdd"] = 3.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v165.Kappa = v170
local v171 = {
    ["ItemOdd"] = 0.08,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v165.Kitsune = v171
v164.Items = v165
v163.RarityData = v164
v1["Zen Egg"] = v163
local v172 = {
    ["Color"] = Color3.fromRGB(85, 0, 0),
    ["HatchTime"] = 15000,
    ["Icon"] = "rbxassetid://109901373212167"
}
local v173 = {}
local v174 = {}
local v175 = {
    ["ItemOdd"] = 40,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v174["Maneki-neko"] = v175
local v176 = {
    ["ItemOdd"] = 32.2,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v174.Tsuchinoko = v176
local v177 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v174.Kodama = v177
local v178 = {
    ["ItemOdd"] = 4,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v174.Raiju = v178
local v179 = {
    ["ItemOdd"] = 3.5,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v174.Mizuchi = v179
local v180 = {
    ["ItemOdd"] = 0.3,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v174["Corrupted Kitsune"] = v180
v173.Items = v174
v172.RarityData = v173
v1["Corrupted Zen Egg"] = v172
local v181 = {
    ["Color"] = Color3.fromRGB(85, 170, 0),
    ["HatchTime"] = 30,
    ["Icon"] = "rbxassetid://73988542242362"
}
local v182 = {}
local v183 = {}
local v184 = {
    ["ItemOdd"] = 30,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v183["Rainbow Parasaurolophus"] = v184
local v185 = {
    ["ItemOdd"] = 25,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v183["Rainbow Iguanodon"] = v185
local v186 = {
    ["ItemOdd"] = 20,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v183["Rainbow Pachycephalosaurus"] = v186
local v187 = {
    ["ItemOdd"] = 10,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v183["Rainbow Dilophosaurus"] = v187
local v188 = {
    ["ItemOdd"] = 8,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v183["Rainbow Ankylosaurus"] = v188
local v189 = {
    ["ItemOdd"] = 7,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v183["Rainbow Spinosaurus"] = v189
v182.Items = v183
v181.RarityData = v182
v1["Rainbow Premium Primal Egg"] = v181
local v190 = {
    ["Color"] = Color3.fromRGB(255, 255, 255),
    ["HatchTime"] = 300000000
}
local v191 = {}
local v192 = {}
local v193 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Silver Monkey"] = v193
local v194 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Kiwi = v194
local v195 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Cow = v195
local v196 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Sea Otter"] = v196
local v197 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Turtle = v197
local v198 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Polar Bear"] = v198
local v199 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Orange Tabby"] = v199
local v200 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Moon Cat"] = v200
local v201 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Monkey = v201
local v202 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Spotted Deer"] = v202
local v203 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Rooster = v203
local v204 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Pig = v204
local v205 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Black Bunny"] = v205
local v206 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Cat = v206
local v207 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Deer = v207
local v208 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Chicken = v208
local v209 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Dog = v209
local v210 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Golden Lab"] = v210
local v211 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Bunny = v211
local v212 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Dragonfly = v212
local v213 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Praying Mantis"] = v213
local v214 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Caterpillar = v214
local v215 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Giant Ant"] = v215
local v216 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Snail = v216
local v217 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Raccoon = v217
local v218 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Owl = v218
local v219 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Night Owl"] = v219
local v220 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Echo Frog"] = v220
local v221 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Frog = v221
local v222 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Mole = v222
local v223 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Hedgehog = v223
local v224 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Blood Hedgehog"] = v224
local v225 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Blood Kiwi"] = v225
local v226 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Blood Owl"] = v226
local v227 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Chicken Zombie"] = v227
local v228 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Grey Mouse"] = v228
local v229 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Squirrel = v229
local v230 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Brown Mouse"] = v230
local v231 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Red Giant Ant"] = v231
local v232 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Red Fox"] = v232
local v233 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Queen Bee"] = v233
local v234 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Bear Bee"] = v234
local v235 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Petal Bee"] = v235
local v236 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Bee = v236
local v237 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Disco Bee"] = v237
local v238 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Butterfly = v238
local v239 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Tarantula Hawk"] = v239
local v240 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Moth = v240
local v241 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Wasp = v241
local v242 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Cooked Owl"] = v242
local v243 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Pack Bee"] = v243
local v244 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Starfish = v244
local v245 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Crab = v245
local v246 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Seagull = v246
local v247 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Flamingo = v247
local v248 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Toucan = v248
local v249 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Sea Turtle"] = v249
local v250 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Orangutan = v250
local v251 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Seal = v251
local v252 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Ostrich = v252
local v253 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Peacock = v253
local v254 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Capybara = v254
local v255 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Scarlet Macaw"] = v255
local v256 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Mimic Octopus"] = v256
local v257 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Meerkat = v257
local v258 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Sand Snake"] = v258
local v259 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Axolotl = v259
local v260 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Hyacinth Macaw"] = v260
local v261 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Fennec Fox"] = v261
local v262 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Hamster = v262
local v263 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Raptor = v263
local v264 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Triceratops = v264
local v265 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Stegosaurus = v265
local v266 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Pterodactyl = v266
local v267 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Brontosaurus = v267
local v268 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["T-Rex"] = v268
local v269 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Iguanodon = v269
local v270 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Parasaurolophus "] = v270
local v271 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Pachycephalosaurus = v271
local v272 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Ankylosaurus = v272
local v273 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Dilophosaurus = v273
local v274 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Spinosaurus = v274
local v275 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Parasaurolophus"] = v275
local v276 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Iguanodon"] = v276
local v277 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Pachycephalosaurus"] = v277
local v278 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Dilophosaurus"] = v278
local v279 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Ankylosaurus"] = v279
local v280 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Spinosaurus"] = v280
local v281 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Shiba Inu"] = v281
local v282 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Nihonzaru = v282
local v283 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Tanuki = v283
local v284 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Tanchozuru = v284
local v285 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Kappa = v285
local v286 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Kitsune = v286
local v287 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Koi = v287
local v288 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Football = v288
local v289 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Maneki-neko"] = v289
local v290 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Tsuchinoko = v290
local v291 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Kodama = v291
local v292 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Raiju = v292
local v293 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Mizuchi = v293
local v294 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Corrupted Kitsune"] = v294
local v295 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Maneki-neko"] = v295
local v296 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Kodama"] = v296
local v297 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Rainbow Corrupted Kitsune"] = v297
local v298 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Golden Bee"] = v298
local v299 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192["Red Dragon"] = v299
local v300 = {
    ["ItemOdd"] = 1,
    ["GeneratedPetData"] = {
        ["WeightRange"] = { 0.8, 2 },
        ["HugeChance"] = 0.001
    }
}
v192.Firefly = v300
v191.Items = v192
v190.RarityData = v191
v1["Fake Egg"] = v190
return v1
