if getgenv().EggDisplay_Cleanup then
    getgenv().EggDisplay_Cleanup()
end

local replicatedStorage = game:GetService("ReplicatedStorage")
local collectionService = game:GetService("CollectionService")
local players = game:GetService("Players")
local runService = game:GetService("RunService")
local coreGui = game:GetService("CoreGui")
local tweenService = game:GetService("TweenService")
local userInputService = game:GetService("UserInputService")
local lighting = game:GetService("Lighting")

local localPlayer = players.LocalPlayer
local currentCamera = workspace.CurrentCamera

local hatchFunction = getupvalue(getupvalue(getconnections(replicatedStorage.GameEvents.PetEggService.OnClientEvent)[1].Function, 1), 2)
local eggModels = getupvalue(hatchFunction, 1)
local eggPets = getupvalue(hatchFunction, 2)

local petRegistry = nil
pcall(function()
    petRegistry = require(replicatedStorage.Data.PetRegistry.PetList)
end)

local eggData = nil
pcall(function()
    eggData = require(replicatedStorage.Data.PetRegistry.PetEggs)
end)

local espCache = {}
local activeEggs = {}
local connections = {}
local selectedEgg = nil
local lastReloadTime = 0
local reloadCooldown = 2

local gui = Instance.new("ScreenGui")
gui.Name = "EggDisplay"
gui.Parent = coreGui

local blur = Instance.new("BlurEffect")
blur.Size = 0
blur.Parent = lighting

local mainFrame = Instance.new("Frame")
mainFrame.Size = UDim2.new(0, 450, 0, 320)
mainFrame.Position = UDim2.new(0.5, -225, 0.5, -160)
mainFrame.BackgroundColor3 = Color3.fromRGB(8, 8, 12)
mainFrame.BorderSizePixel = 0
mainFrame.Parent = gui

local shadow = Instance.new("ImageLabel")
shadow.Size = UDim2.new(1, 30, 1, 30)
shadow.Position = UDim2.new(0, -15, 0, -15)
shadow.BackgroundTransparency = 1
shadow.Image = "rbxasset://textures/ui/GuiImagePlaceholder.png"
shadow.ImageColor3 = Color3.fromRGB(0, 0, 0)
shadow.ImageTransparency = 0.7
shadow.ZIndex = mainFrame.ZIndex - 1
shadow.Parent = mainFrame

local corner = Instance.new("UICorner")
corner.CornerRadius = UDim.new(0, 20)
corner.Parent = mainFrame

local shadowCorner = Instance.new("UICorner")
shadowCorner.CornerRadius = UDim.new(0, 25)
shadowCorner.Parent = shadow

local topBar = Instance.new("Frame")
topBar.Size = UDim2.new(1, 0, 0, 60)
topBar.Position = UDim2.new(0, 0, 0, 0)
topBar.BackgroundColor3 = Color3.fromRGB(15, 15, 22)
topBar.BorderSizePixel = 0
topBar.Parent = mainFrame

local topCorner = Instance.new("UICorner")
topCorner.CornerRadius = UDim.new(0, 20)
topCorner.Parent = topBar

local topGradient = Instance.new("UIGradient")
topGradient.Color = ColorSequence.new{
    ColorSequenceKeypoint.new(0, Color3.fromRGB(25, 25, 35)),
    ColorSequenceKeypoint.new(1, Color3.fromRGB(10, 10, 15))
}
topGradient.Rotation = 90
topGradient.Parent = topBar

local titleLabel = Instance.new("TextLabel")
titleLabel.Size = UDim2.new(0.7, 0, 1, 0)
titleLabel.Position = UDim2.new(0, 20, 0, 0)
titleLabel.BackgroundTransparency = 1
titleLabel.Text = "EXTREME EGG REROLLER"
titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
titleLabel.TextSize = 18
titleLabel.Font = Enum.Font.SourceSansBold
titleLabel.TextXAlignment = Enum.TextXAlignment.Left
titleLabel.Parent = topBar

local closeButton = Instance.new("TextButton")
closeButton.Size = UDim2.new(0, 40, 0, 40)
closeButton.Position = UDim2.new(1, -50, 0, 10)
closeButton.BackgroundColor3 = Color3.fromRGB(255, 60, 60)
closeButton.BorderSizePixel = 0
closeButton.Text = "✕"
closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
closeButton.TextSize = 20
closeButton.Font = Enum.Font.SourceSansBold
closeButton.Parent = topBar

local closeCorner = Instance.new("UICorner")
closeCorner.CornerRadius = UDim.new(0, 10)
closeCorner.Parent = closeButton

local rerollButton = Instance.new("TextButton")
rerollButton.Size = UDim2.new(0.9, 0, 0, 70)
rerollButton.Position = UDim2.new(0.05, 0, 0, 80)
rerollButton.BackgroundColor3 = Color3.fromRGB(120, 40, 255)
rerollButton.BorderSizePixel = 0
rerollButton.Text = "REROLL SELECTED EGG"
rerollButton.TextColor3 = Color3.fromRGB(255, 255, 255)
rerollButton.TextSize = 16
rerollButton.Font = Enum.Font.SourceSansBold
rerollButton.Parent = mainFrame

local rerollCorner = Instance.new("UICorner")
rerollCorner.CornerRadius = UDim.new(0, 15)
rerollCorner.Parent = rerollButton

local rerollGradient = Instance.new("UIGradient")
rerollGradient.Color = ColorSequence.new{
    ColorSequenceKeypoint.new(0, Color3.fromRGB(140, 60, 255)),
    ColorSequenceKeypoint.new(1, Color3.fromRGB(100, 20, 200))
}
rerollGradient.Rotation = 45
rerollGradient.Parent = rerollButton

local statusFrame = Instance.new("Frame")
statusFrame.Size = UDim2.new(0.9, 0, 0, 50)
statusFrame.Position = UDim2.new(0.05, 0, 0, 170)
statusFrame.BackgroundColor3 = Color3.fromRGB(20, 20, 30)
statusFrame.BorderSizePixel = 0
statusFrame.Parent = mainFrame

local statusCorner = Instance.new("UICorner")
statusCorner.CornerRadius = UDim.new(0, 12)
statusCorner.Parent = statusFrame

local statusLabel = Instance.new("TextLabel")
statusLabel.Size = UDim2.new(1, 0, 1, 0)
statusLabel.Position = UDim2.new(0, 0, 0, 0)
statusLabel.BackgroundTransparency = 1
statusLabel.Text = "SELECT AN EGG TO BEGIN"
statusLabel.TextColor3 = Color3.fromRGB(180, 180, 180)
statusLabel.TextSize = 14
statusLabel.Font = Enum.Font.SourceSans
statusLabel.Parent = statusFrame

local infoFrame = Instance.new("Frame")
infoFrame.Size = UDim2.new(0.9, 0, 0, 80)
infoFrame.Position = UDim2.new(0.05, 0, 0, 230)
infoFrame.BackgroundColor3 = Color3.fromRGB(15, 25, 15)
infoFrame.BorderSizePixel = 0
infoFrame.Parent = mainFrame

local infoCorner = Instance.new("UICorner")
infoCorner.CornerRadius = UDim.new(0, 12)
infoCorner.Parent = infoFrame

local eggInfoLabel = Instance.new("TextLabel")
eggInfoLabel.Size = UDim2.new(1, 0, 0.5, 0)
eggInfoLabel.Position = UDim2.new(0, 0, 0, 0)
eggInfoLabel.BackgroundTransparency = 1
eggInfoLabel.Text = ""
eggInfoLabel.TextColor3 = Color3.fromRGB(100, 255, 100)
eggInfoLabel.TextSize = 14
eggInfoLabel.Font = Enum.Font.SourceSansBold
eggInfoLabel.Parent = infoFrame

local petInfoLabel = Instance.new("TextLabel")
petInfoLabel.Size = UDim2.new(1, 0, 0.5, 0)
petInfoLabel.Position = UDim2.new(0, 0, 0.5, 0)
petInfoLabel.BackgroundTransparency = 1
petInfoLabel.Text = ""
petInfoLabel.TextColor3 = Color3.fromRGB(255, 200, 100)
petInfoLabel.TextSize = 12
petInfoLabel.Font = Enum.Font.SourceSans
petInfoLabel.Parent = infoFrame

local function getObjectFromId(objectId)
    for eggModel in eggModels do
        if eggModel:GetAttribute("OBJECT_UUID") ~= objectId then continue end
        return eggModel
    end
end


closeButton.MouseButton1Click:Connect(function()
    if getgenv().EggDisplay_Cleanup then
        getgenv().EggDisplay_Cleanup()
    end
end)

local function getRandomPetFromEgg(eggName)
    if not eggData or not eggData[eggName] then return nil end

    local eggInfo = eggData[eggName]
    if not eggInfo.RarityData or not eggInfo.RarityData.Items then return nil end

    local pets = {}
    local totalWeight = 0

    for petName, petData in pairs(eggInfo.RarityData.Items) do
        local weight = petData.ItemOdd or 1
        table.insert(pets, {name = petName, weight = weight})
        totalWeight = totalWeight + weight
    end

    if #pets == 0 then return nil end

    local random = math.random() * totalWeight
    local currentWeight = 0

    for _, pet in ipairs(pets) do
        currentWeight = currentWeight + pet.weight
        if random <= currentWeight then
            return pet.name
        end
    end

    return pets[1].name
end

local function getPetWeight(petName)
    if not petRegistry or not petName then
        return math.random(80, 200) / 100
    end

    local petData = petRegistry[petName]
    if not petData then
        return math.random(80, 200) / 100
    end

    local weight = petData.SellPrice and (petData.SellPrice / 1000000) or math.random(80, 200) / 100
    return weight
end

local function manualReroll(objectId)
    local currentTime = tick()
    if currentTime - lastReloadTime < reloadCooldown then
        statusLabel.Text = "COOLDOWN: " .. math.ceil(reloadCooldown - (currentTime - lastReloadTime)) .. "s"
        return
    end

    if not objectId or not espCache[objectId] then return end

    local object = getObjectFromId(objectId)
    if not object then return end

    local eggName = object:GetAttribute("EggName")
    local randomPet = getRandomPetFromEgg(eggName)

    if randomPet then
        local weight = getPetWeight(randomPet)

        local billboard = espCache[objectId].billboard
        local frame = espCache[objectId].frame
        local nameLabel = espCache[objectId].nameLabel
        local weightLabel = espCache[objectId].weightLabel
        local glowFrame = espCache[objectId].glowFrame

        nameLabel.Text = randomPet
        weightLabel.Text = weight .. " KG"

        petInfoLabel.Text = "Pet: " .. randomPet .. " | Weight: " .. weight .. " KG"

        local color = Color3.fromRGB(255, 255, 255)
        if weight >= 6.0 then
            color = Color3.fromRGB(255, 215, 0)
            glowFrame.BackgroundColor3 = Color3.fromRGB(255, 215, 0)
            glowFrame.BackgroundTransparency = 0.3
        elseif weight >= 4.0 then
            color = Color3.fromRGB(255, 165, 0)
            glowFrame.BackgroundColor3 = Color3.fromRGB(255, 165, 0)
            glowFrame.BackgroundTransparency = 0.5
        elseif weight >= 2.0 then
            color = Color3.fromRGB(100, 255, 100)
            glowFrame.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
            glowFrame.BackgroundTransparency = 0.5
        else
            glowFrame.BackgroundTransparency = 1
        end

        nameLabel.TextColor3 = color
        weightLabel.TextColor3 = color
        frame.BackgroundColor3 = Color3.fromRGB(math.min(255, color.R * 255 * 0.3), math.min(255, color.G * 255 * 0.3), math.min(255, color.B * 255 * 0.3))

        local scaleTween = tweenService:Create(billboard, TweenInfo.new(0.3, Enum.EasingStyle.Back), {Size = UDim2.new(0, 220, 0, 110)})
        scaleTween:Play()
        scaleTween.Completed:Connect(function()
            tweenService:Create(billboard, TweenInfo.new(0.2, Enum.EasingStyle.Back), {Size = UDim2.new(0, 200, 0, 100)}):Play()
        end)

        lastReloadTime = currentTime
        statusLabel.Text = "REROLLED SUCCESSFULLY"
    end
end

rerollButton.MouseButton1Click:Connect(function()
    if selectedEgg then
        manualReroll(selectedEgg)

        local buttonTween = tweenService:Create(rerollButton, TweenInfo.new(0.1, Enum.EasingStyle.Quad), {Size = UDim2.new(0.85, 0, 0, 65)})
        buttonTween:Play()
        buttonTween.Completed:Connect(function()
            tweenService:Create(rerollButton, TweenInfo.new(0.1, Enum.EasingStyle.Quad), {Size = UDim2.new(0.9, 0, 0, 70)}):Play()
        end)
    else
        statusLabel.Text = "NO EGG SELECTED"
        statusLabel.TextColor3 = Color3.fromRGB(255, 100, 100)
        task.wait(1)
        statusLabel.Text = "SELECT AN EGG TO BEGIN"
        statusLabel.TextColor3 = Color3.fromRGB(180, 180, 180)
    end
end)

local function UpdateEsp(objectId, petName)
    local object = getObjectFromId(objectId)
    if not object or not espCache[objectId] then return end

    local weight = getPetWeight(petName)

    local billboard = espCache[objectId].billboard
    local frame = espCache[objectId].frame
    local nameLabel = espCache[objectId].nameLabel
    local weightLabel = espCache[objectId].weightLabel
    local glowFrame = espCache[objectId].glowFrame
    local statusLabel = espCache[objectId].statusLabel

    nameLabel.Text = petName or "?"
    weightLabel.Text = weight .. " KG"
    statusLabel.Text = "READY"
    statusLabel.TextColor3 = Color3.fromRGB(100, 255, 100)

    local color = Color3.fromRGB(255, 255, 255)
    if weight >= 6.0 then
        color = Color3.fromRGB(255, 215, 0)
        glowFrame.BackgroundColor3 = Color3.fromRGB(255, 215, 0)
        glowFrame.BackgroundTransparency = 0.3
    elseif weight >= 4.0 then
        color = Color3.fromRGB(255, 165, 0)
        glowFrame.BackgroundColor3 = Color3.fromRGB(255, 165, 0)
        glowFrame.BackgroundTransparency = 0.5
    elseif weight >= 2.0 then
        color = Color3.fromRGB(100, 255, 100)
        glowFrame.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
        glowFrame.BackgroundTransparency = 0.5
    else
        glowFrame.BackgroundTransparency = 1
    end

    nameLabel.TextColor3 = color
    weightLabel.TextColor3 = color
    frame.BackgroundColor3 = Color3.fromRGB(math.min(255, color.R * 255 * 0.3), math.min(255, color.G * 255 * 0.3), math.min(255, color.B * 255 * 0.3))
end


local function AddEsp(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end

    local eggName = object:GetAttribute("EggName")
    local petName = eggPets[object:GetAttribute("OBJECT_UUID")]

    local objectId = object:GetAttribute("OBJECT_UUID")
    if not objectId then return end

    local label = Drawing.new("Text")
    label.Size = 16
    label.Color = Color3.fromRGB(255, 255, 255)
    label.Outline = true
    label.OutlineColor = Color3.new(0, 0, 0)
    label.Center = true
    label.Visible = false
    label.Font = Drawing.Fonts.Plex

    espCache[objectId] = label
    activeEggs[objectId] = object

    if petName then
        local weight = getPetWeight(petName)
        label.Text = string.format("✅ %s\n🐾 %s | ⚖️ %.1f KG", eggName, petName, weight)

        if weight >= 6.0 then
            label.Color = Color3.fromRGB(255, 215, 0)
        elseif weight >= 4.0 then
            label.Color = Color3.fromRGB(255, 165, 0)
        elseif weight >= 2.0 then
            label.Color = Color3.fromRGB(100, 255, 100)
        else
            label.Color = Color3.fromRGB(255, 255, 255)
        end
    else
        label.Text = string.format("⏳ %s\n� Rolling...", eggName)
        label.Color = Color3.fromRGB(255, 255, 100)
        startRollAnimation(objectId, eggName)
    end
end


local function RemoveEsp(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end


    local objectId = object:GetAttribute("OBJECT_UUID")
    if espCache[objectId] then
        espCache[objectId]:Remove()
        espCache[objectId] = nil
    end
   
    activeEggs[objectId] = nil
end


local function UpdateAllEsp()
    for objectId, object in activeEggs do
        if not object or not object:IsDescendantOf(workspace) then
            activeEggs[objectId] = nil
            if espCache[objectId] then
                espCache[objectId].Visible = false
            end
            continue
        end


        local label = espCache[objectId]
        if label then
            local pos, onScreen = currentCamera:WorldToViewportPoint(object:GetPivot().Position)
            if onScreen then
                label.Position = Vector2.new(pos.X, pos.Y)
                label.Visible = true
            else
                label.Visible = false
            end
        end
    end
end


-- Initialize existing eggs
for _, object in collectionService:GetTagged("PetEggServer") do
    task.spawn(AddEsp, object)
end

-- Connect events and store connections for cleanup
connections[#connections + 1] = collectionService:GetInstanceAddedSignal("PetEggServer"):Connect(AddEsp)
connections[#connections + 1] = collectionService:GetInstanceRemovedSignal("PetEggServer"):Connect(RemoveEsp)

local old; old = hookfunction(getconnections(replicatedStorage.GameEvents.EggReadyToHatch_RE.OnClientEvent)[1].Function, newcclosure(function(objectId, petName)
    if rollAnimations[objectId] then
        rollAnimations[objectId] = nil
    end

    UpdateEsp(objectId, petName)
    return old(objectId, petName)
end))

-- Connect render loop
connections[#connections + 1] = runService.PreRender:Connect(UpdateAllEsp)

getgenv().EggDisplay_Cleanup = function()
    for _, connection in pairs(connections) do
        if connection then
            connection:Disconnect()
        end
    end

    for _, label in pairs(espCache) do
        if label then
            label:Remove()
        end
    end

    if gui then
        gui:Destroy()
    end

    espCache = {}
    activeEggs = {}
    connections = {}
    rollAnimations = {}
end