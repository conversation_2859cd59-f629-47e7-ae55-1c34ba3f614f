-- Cleanup previous instances
if getgenv().EggESP_Cleanup then
    getgenv().EggESP_Cleanup()
end

local replicatedStorage = game:GetService("ReplicatedStorage")
local collectionService = game:GetService("CollectionService")
local players = game:GetService("Players")
local runService = game:GetService("RunService")

local localPlayer = players.LocalPlayer
local currentCamera = workspace.CurrentCamera

-- Get game data
local hatchFunction = getupvalue(getupvalue(getconnections(replicatedStorage.GameEvents.PetEggService.OnClientEvent)[1].Function, 1), 2)
local eggModels = getupvalue(hatchFunction, 1)
local eggPets = getupvalue(hatchFunction, 2)

-- Get real egg and pet data
local eggData = nil
local petRegistry = nil
pcall(function()
    eggData = require(replicatedStorage.Data.PetRegistry.PetEggs)
    petRegistry = require(replicatedStorage.Data.PetRegistry.PetList)
end)

local espCache = {}
local activeEggs = {}
local connections = {}

-- Fake display settings
local FAKE_DISPLAY = {
    enabled = false,  -- Set to true to enable fake display
    fakeWeight = 8.5,
    fakeHugeChance = 95,
    fakeRarity = "Prismatic"
}


local function getObjectFromId(objectId)
    for eggModel in eggModels do
        if eggModel:GetAttribute("OBJECT_UUID") ~= objectId then continue end
        return eggModel
    end
end


-- Get pet info with real weight, huge chance, and odds from egg data
local function getPetInfo(petName, eggName)
    if not petRegistry or not eggData or not petName or not eggName then
        return {
            weight = "Unknown",
            rarity = "Unknown",
            hugeChance = "0.1%",
            odds = "Unknown",
            isHuge = false
        }
    end

    local petData = petRegistry[petName]
    local eggInfo = eggData[eggName]

    if not petData then
        return {
            weight = "Unknown",
            rarity = "Unknown",
            hugeChance = "0.1%",
            odds = "Unknown",
            isHuge = false
        }
    end

    -- Calculate real weight from sell price (KG = SellPrice / 1,000,000)
    local weight = petData.SellPrice and (petData.SellPrice / 1000000) or 0.8
    local rarity = petData.Rarity or "Common"

    -- Get huge chance from egg data (default 0.1%)
    local hugeChance = 0.1
    if eggInfo and eggInfo.RarityData and eggInfo.RarityData.Items and eggInfo.RarityData.Items[petName] then
        local petEggData = eggInfo.RarityData.Items[petName]
        if petEggData.GeneratedPetData and petEggData.GeneratedPetData.HugeChance then
            hugeChance = petEggData.GeneratedPetData.HugeChance * 100
        end
    end

    -- Get odds from egg data
    local odds = "Unknown"
    if eggInfo and eggInfo.RarityData and eggInfo.RarityData.Items and eggInfo.RarityData.Items[petName] then
        local petEggData = eggInfo.RarityData.Items[petName]
        if petEggData.ItemOdd then
            odds = string.format("1/%d", math.floor(100 / petEggData.ItemOdd))
        end
    end

    local isHuge = weight >= 4.0

    return {
        weight = string.format("%.1f KG", weight),
        rarity = rarity,
        hugeChance = string.format("%.1f%%", hugeChance),
        odds = odds,
        isHuge = isHuge
    }
end

local function UpdateEsp(objectId, petName)
    local object = getObjectFromId(objectId)
    if not object or not espCache[objectId] then return end

    local eggName = object:GetAttribute("EggName")
    local petInfo = getPetInfo(petName, eggName)

    -- Use fake display if enabled
    local displayWeight = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeWeight .. " KG" or petInfo.weight
    local displayHuge = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeHugeChance .. "%" or petInfo.hugeChance
    local displayRarity = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeRarity or petInfo.rarity
    local displayOdds = FAKE_DISPLAY.enabled and "1/1" or petInfo.odds

    -- Color coding: Gold = Huge, Green = Rare, White = Normal, Purple = Fake
    local color = Color3.fromRGB(255, 255, 255) -- Default white
    if FAKE_DISPLAY.enabled then
        color = Color3.fromRGB(255, 100, 255) -- Purple for fake
    elseif petInfo.isHuge then
        color = Color3.fromRGB(255, 215, 0) -- Gold for huge
    elseif petInfo.rarity == "Divine" or petInfo.rarity == "Prismatic" then
        color = Color3.fromRGB(255, 100, 255) -- Purple for divine/prismatic
    elseif petInfo.rarity == "Legendary" or petInfo.rarity == "Mythical" then
        color = Color3.fromRGB(255, 165, 0) -- Orange for legendary/mythical
    elseif petInfo.rarity == "Rare" then
        color = Color3.fromRGB(100, 255, 100) -- Green for rare
    end

    espCache[objectId].Text = string.format("%s | %s\n💎 %s | ⚖️ %s | 🎯 %s | 🎲 %s",
        eggName, petName or "?", displayRarity, displayWeight, displayHuge, displayOdds)
    espCache[objectId].Color = color
end


local function AddEsp(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end

    local eggName = object:GetAttribute("EggName")
    local petName = eggPets[object:GetAttribute("OBJECT_UUID")]

    local objectId = object:GetAttribute("OBJECT_UUID")
    if not objectId then return end

    local petInfo = getPetInfo(petName, eggName)

    -- Use fake display if enabled
    local displayWeight = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeWeight .. " KG" or petInfo.weight
    local displayHuge = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeHugeChance .. "%" or petInfo.hugeChance
    local displayRarity = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeRarity or petInfo.rarity
    local displayOdds = FAKE_DISPLAY.enabled and "1/1" or petInfo.odds

    -- Color coding: Gold = Huge, Green = Rare, White = Normal, Purple = Fake
    local color = Color3.fromRGB(255, 255, 255) -- Default white
    if FAKE_DISPLAY.enabled then
        color = Color3.fromRGB(255, 100, 255) -- Purple for fake
    elseif petInfo.isHuge then
        color = Color3.fromRGB(255, 215, 0) -- Gold for huge
    elseif petInfo.rarity == "Divine" or petInfo.rarity == "Prismatic" then
        color = Color3.fromRGB(255, 100, 255) -- Purple for divine/prismatic
    elseif petInfo.rarity == "Legendary" or petInfo.rarity == "Mythical" then
        color = Color3.fromRGB(255, 165, 0) -- Orange for legendary/mythical
    elseif petInfo.rarity == "Rare" then
        color = Color3.fromRGB(100, 255, 100) -- Green for rare
    end

    local label = Drawing.new("Text")
    label.Text = string.format("%s | %s\n💎 %s | ⚖️ %s | 🎯 %s | 🎲 %s",
        eggName, petName or "?", displayRarity, displayWeight, displayHuge, displayOdds)
    label.Size = 14
    label.Color = color
    label.Outline = true
    label.OutlineColor = Color3.new(0, 0, 0)
    label.Center = true
    label.Visible = false

    espCache[objectId] = label
    activeEggs[objectId] = object
end


local function RemoveEsp(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end


    local objectId = object:GetAttribute("OBJECT_UUID")
    if espCache[objectId] then
        espCache[objectId]:Remove()
        espCache[objectId] = nil
    end
   
    activeEggs[objectId] = nil
end


local function UpdateAllEsp()
    for objectId, object in activeEggs do
        if not object or not object:IsDescendantOf(workspace) then
            activeEggs[objectId] = nil
            if espCache[objectId] then
                espCache[objectId].Visible = false
            end
            continue
        end


        local label = espCache[objectId]
        if label then
            local pos, onScreen = currentCamera:WorldToViewportPoint(object:GetPivot().Position)
            if onScreen then
                label.Position = Vector2.new(pos.X, pos.Y)
                label.Visible = true
            else
                label.Visible = false
            end
        end
    end
end


-- Initialize existing eggs
for _, object in collectionService:GetTagged("PetEggServer") do
    task.spawn(AddEsp, object)
end

-- Connect events and store connections for cleanup
connections[#connections + 1] = collectionService:GetInstanceAddedSignal("PetEggServer"):Connect(AddEsp)
connections[#connections + 1] = collectionService:GetInstanceRemovedSignal("PetEggServer"):Connect(RemoveEsp)

-- Hook egg ready event
local old; old = hookfunction(getconnections(replicatedStorage.GameEvents.EggReadyToHatch_RE.OnClientEvent)[1].Function, newcclosure(function(objectId, petName)
    UpdateEsp(objectId, petName)
    return old(objectId, petName)
end))

-- Connect render loop
connections[#connections + 1] = runService.PreRender:Connect(UpdateAllEsp)

-- Cleanup function for when script reruns
getgenv().EggESP_Cleanup = function()
    -- Disconnect all connections
    for _, connection in pairs(connections) do
        if connection then
            connection:Disconnect()
        end
    end

    -- Remove all ESP labels
    for _, label in pairs(espCache) do
        if label then
            label:Remove()
        end
    end

    -- Clear tables
    espCache = {}
    activeEggs = {}
    connections = {}

    print("🧹 Egg ESP cleaned up!")
end

print("🥚 Enhanced Egg ESP loaded!")
print("📊 Features: Weight, Huge Chance, Rarity, Fake Display")
print("🎨 Colors: Gold = Huge, White = Normal, Purple = Fake")
print("🔧 Fake Display: " .. (FAKE_DISPLAY.enabled and "ENABLED" or "DISABLED"))