-- Cleanup previous instances
if getgenv().EggESP_Cleanup then
    getgenv().EggESP_Cleanup()
end

local replicatedStorage = game:GetService("ReplicatedStorage")
local collectionService = game:GetService("CollectionService")
local players = game:GetService("Players")
local runService = game:GetService("RunService")

local localPlayer = players.LocalPlayer
local currentCamera = workspace.CurrentCamera

-- Get game data
local hatchFunction = getupvalue(getupvalue(getconnections(replicatedStorage.GameEvents.PetEggService.OnClientEvent)[1].Function, 1), 2)
local eggModels = getupvalue(hatchFunction, 1)
local eggPets = getupvalue(hatchFunction, 2)

-- Try to get pet registry for weight/huge data
local petRegistry = nil
pcall(function()
    petRegistry = require(replicatedStorage.Data.PetRegistry.PetList)
end)

local espCache = {}
local activeEggs = {}
local connections = {}

-- Fake display settings
local FAKE_DISPLAY = {
    enabled = true,
    fakeWeight = 8.5,
    fakeHugeChance = 95,
    fakeRarity = "Divine"
}


local function getObjectFromId(objectId)
    for eggModel in eggModels do
        if eggModel:GetAttribute("OBJECT_UUID") ~= objectId then continue end
        return eggModel
    end
end


-- Get pet info with weight and huge chance
local function getPetInfo(petName)
    if not petRegistry or not petName then
        return {
            weight = "Unknown",
            rarity = "Unknown",
            hugeChance = "0%",
            isHuge = false
        }
    end

    local petData = petRegistry[petName]
    if not petData then
        return {
            weight = "Unknown",
            rarity = "Unknown",
            hugeChance = "0%",
            isHuge = false
        }
    end

    local weight = petData.SellPrice and (petData.SellPrice / 1000000) or 0
    local rarity = petData.Rarity or "Common"
    local isHuge = weight >= 4.0
    local hugeChance = isHuge and "100%" or "0%"

    return {
        weight = string.format("%.1f KG", weight),
        rarity = rarity,
        hugeChance = hugeChance,
        isHuge = isHuge
    }
end

local function UpdateEsp(objectId, petName)
    local object = getObjectFromId(objectId)
    if not object or not espCache[objectId] then return end

    local eggName = object:GetAttribute("EggName")
    local petInfo = getPetInfo(petName)

    -- Use fake display if enabled
    local displayWeight = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeWeight .. " KG" or petInfo.weight
    local displayHuge = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeHugeChance .. "%" or petInfo.hugeChance
    local displayRarity = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeRarity or petInfo.rarity

    local color = petInfo.isHuge and Color3.fromRGB(255, 215, 0) or Color3.fromRGB(255, 255, 255)
    if FAKE_DISPLAY.enabled then
        color = Color3.fromRGB(255, 100, 255) -- Purple for fake
    end

    espCache[objectId].Text = string.format("%s | %s\n💎 %s | ⚖️ %s | 🎯 %s",
        eggName, petName or "?", displayRarity, displayWeight, displayHuge)
    espCache[objectId].Color = color
end


local function AddEsp(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end

    local eggName = object:GetAttribute("EggName")
    local petName = eggPets[object:GetAttribute("OBJECT_UUID")]

    local objectId = object:GetAttribute("OBJECT_UUID")
    if not objectId then return end

    local petInfo = getPetInfo(petName)

    -- Use fake display if enabled
    local displayWeight = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeWeight .. " KG" or petInfo.weight
    local displayHuge = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeHugeChance .. "%" or petInfo.hugeChance
    local displayRarity = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeRarity or petInfo.rarity

    local color = petInfo.isHuge and Color3.fromRGB(255, 215, 0) or Color3.fromRGB(255, 255, 255)
    if FAKE_DISPLAY.enabled then
        color = Color3.fromRGB(255, 100, 255) -- Purple for fake
    end

    local label = Drawing.new("Text")
    label.Text = string.format("%s | %s\n💎 %s | ⚖️ %s | 🎯 %s",
        eggName, petName or "?", displayRarity, displayWeight, displayHuge)
    label.Size = 16
    label.Color = color
    label.Outline = true
    label.OutlineColor = Color3.new(0, 0, 0)
    label.Center = true
    label.Visible = false

    espCache[objectId] = label
    activeEggs[objectId] = object
end


local function RemoveEsp(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end


    local objectId = object:GetAttribute("OBJECT_UUID")
    if espCache[objectId] then
        espCache[objectId]:Remove()
        espCache[objectId] = nil
    end
   
    activeEggs[objectId] = nil
end


local function UpdateAllEsp()
    for objectId, object in activeEggs do
        if not object or not object:IsDescendantOf(workspace) then
            activeEggs[objectId] = nil
            if espCache[objectId] then
                espCache[objectId].Visible = false
            end
            continue
        end


        local label = espCache[objectId]
        if label then
            local pos, onScreen = currentCamera:WorldToViewportPoint(object:GetPivot().Position)
            if onScreen then
                label.Position = Vector2.new(pos.X, pos.Y)
                label.Visible = true
            else
                label.Visible = false
            end
        end
    end
end


-- Initialize existing eggs
for _, object in collectionService:GetTagged("PetEggServer") do
    task.spawn(AddEsp, object)
end

-- Connect events and store connections for cleanup
connections[#connections + 1] = collectionService:GetInstanceAddedSignal("PetEggServer"):Connect(AddEsp)
connections[#connections + 1] = collectionService:GetInstanceRemovedSignal("PetEggServer"):Connect(RemoveEsp)

-- Hook egg ready event
local old; old = hookfunction(getconnections(replicatedStorage.GameEvents.EggReadyToHatch_RE.OnClientEvent)[1].Function, newcclosure(function(objectId, petName)
    UpdateEsp(objectId, petName)
    return old(objectId, petName)
end))

-- Connect render loop
connections[#connections + 1] = runService.PreRender:Connect(UpdateAllEsp)

-- Cleanup function for when script reruns
getgenv().EggESP_Cleanup = function()
    -- Disconnect all connections
    for _, connection in pairs(connections) do
        if connection then
            connection:Disconnect()
        end
    end

    -- Remove all ESP labels
    for _, label in pairs(espCache) do
        if label then
            label:Remove()
        end
    end

    -- Clear tables
    espCache = {}
    activeEggs = {}
    connections = {}

    print("🧹 Egg ESP cleaned up!")
end

print("🥚 Enhanced Egg ESP loaded!")
print("📊 Features: Weight, Huge Chance, Rarity, Fake Display")
print("🎨 Colors: Gold = Huge, White = Normal, Purple = Fake")
print("🔧 Fake Display: " .. (FAKE_DISPLAY.enabled and "ENABLED" or "DISABLED"))