-- Cleanup previous instances
if getgenv().EggESP_Cleanup then
    getgenv().EggESP_Cleanup()
end

local replicatedStorage = game:GetService("ReplicatedStorage")
local collectionService = game:GetService("CollectionService")
local players = game:GetService("Players")
local runService = game:GetService("RunService")

local localPlayer = players.LocalPlayer
local currentCamera = workspace.CurrentCamera

local hatchFunction = getupvalue(getupvalue(getconnections(replicatedStorage.GameEvents.PetEggService.OnClientEvent)[1].Function, 1), 2)
local eggModels = getupvalue(hatchFunction, 1)
local eggPets = getupvalue(hatchFunction, 2)

-- Get pet registry for weight calculation
local petRegistry = nil
pcall(function()
    petRegistry = require(replicatedStorage.Data.PetRegistry.PetList)
end)

-- Get egg data for random pet selection
local eggData = nil
pcall(function()
    eggData = require(replicatedStorage.Data.PetRegistry.PetEggs)
end)

local espCache = {}
local activeEggs = {}
local connections = {}
local rerollAnimations = {}

local function getObjectFromId(objectId)
    for eggModel in eggModels do
        if eggModel:GetAttribute("OBJECT_UUID") ~= objectId then continue end
        return eggModel
    end
end


local function getObjectFromId(objectId)
    for eggModel in eggModels do
        if eggModel:GetAttribute("OBJECT_UUID") ~= objectId then continue end
        return eggModel
    end
end


-- Get random pet from egg data
local function getRandomPetFromEgg(eggName)
    if not eggData or not eggData[eggName] then return nil end

    local eggInfo = eggData[eggName]
    if not eggInfo.RarityData or not eggInfo.RarityData.Items then return nil end

    local pets = {}
    local totalWeight = 0

    for petName, petData in pairs(eggInfo.RarityData.Items) do
        local weight = petData.ItemOdd or 1
        table.insert(pets, {name = petName, weight = weight})
        totalWeight = totalWeight + weight
    end

    if #pets == 0 then return nil end

    local random = math.random() * totalWeight
    local currentWeight = 0

    for _, pet in ipairs(pets) do
        currentWeight = currentWeight + pet.weight
        if random <= currentWeight then
            return pet.name
        end
    end

    return pets[1].name
end

-- Get pet weight only (simplified)
local function getPetWeight(petName)
    if not petRegistry or not petName then
        return math.random(80, 200) / 100 -- Random weight between 0.8-2.0 KG
    end

    local petData = petRegistry[petName]
    if not petData then
        return math.random(80, 200) / 100
    end

    -- Calculate real weight from sell price (KG = SellPrice / 1,000,000)
    local weight = petData.SellPrice and (petData.SellPrice / 1000000) or math.random(80, 200) / 100
    return weight
end

-- Fake reroller animation
local function startFakeReroll(objectId, eggName)
    if rerollAnimations[objectId] then return end

    rerollAnimations[objectId] = true

    task.spawn(function()
        local rerollCount = 0
        local maxRerolls = math.random(20, 40)

        while rerollAnimations[objectId] and rerollCount < maxRerolls do
            local randomPet = getRandomPetFromEgg(eggName)
            if randomPet and espCache[objectId] then
                local weight = getPetWeight(randomPet)
                local object = getObjectFromId(objectId)
                if object then
                    local eggName = object:GetAttribute("EggName")
                    espCache[objectId].Text = string.format("🔄 %s\n🎲 %s | ⚖️ %.1f KG", eggName, randomPet, weight)

                    -- Color based on weight
                    if weight >= 6.0 then
                        espCache[objectId].Color = Color3.fromRGB(255, 215, 0) -- Gold for 6+ KG
                    elseif weight >= 4.0 then
                        espCache[objectId].Color = Color3.fromRGB(255, 165, 0) -- Orange for 4+ KG
                    elseif weight >= 2.0 then
                        espCache[objectId].Color = Color3.fromRGB(100, 255, 100) -- Green for 2+ KG
                    else
                        espCache[objectId].Color = Color3.fromRGB(255, 255, 255) -- White for normal
                    end
                end
            end

            rerollCount = rerollCount + 1
            task.wait(0.08) -- Faster reroll animation
        end

        rerollAnimations[objectId] = nil
    end)
end

local function UpdateEsp(objectId, petName)
    local object = getObjectFromId(objectId)
    if not object or not espCache[objectId] then return end

    local eggName = object:GetAttribute("EggName")

    -- Stop reroll animation when egg is ready
    if rerollAnimations[objectId] then
        rerollAnimations[objectId] = nil
    end

    local weight = getPetWeight(petName)

    -- Simple display: Egg name, pet name, and weight only
    espCache[objectId].Text = string.format("✅ %s\n🐾 %s | ⚖️ %.1f KG", eggName, petName or "?", weight)

    -- Color based on weight
    if weight >= 6.0 then
        espCache[objectId].Color = Color3.fromRGB(255, 215, 0) -- Gold for 6+ KG
    elseif weight >= 4.0 then
        espCache[objectId].Color = Color3.fromRGB(255, 165, 0) -- Orange for 4+ KG
    elseif weight >= 2.0 then
        espCache[objectId].Color = Color3.fromRGB(100, 255, 100) -- Green for 2+ KG
    else
        espCache[objectId].Color = Color3.fromRGB(255, 255, 255) -- White for normal
    end
end


local function AddEsp(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end

    local eggName = object:GetAttribute("EggName")
    local petName = eggPets[object:GetAttribute("OBJECT_UUID")]

    local objectId = object:GetAttribute("OBJECT_UUID")
    if not objectId then return end

    local label = Drawing.new("Text")
    label.Size = 16
    label.Color = Color3.fromRGB(255, 255, 255)
    label.Outline = true
    label.OutlineColor = Color3.new(0, 0, 0)
    label.Center = true
    label.Visible = false
    label.Font = Drawing.Fonts.Plex -- Better font

    espCache[objectId] = label
    activeEggs[objectId] = object

    -- If egg has pet name, show it immediately
    if petName then
        local weight = getPetWeight(petName)
        label.Text = string.format("✅ %s\n🐾 %s | ⚖️ %.1f KG", eggName, petName, weight)

        -- Color based on weight
        if weight >= 6.0 then
            label.Color = Color3.fromRGB(255, 215, 0) -- Gold for 6+ KG
        elseif weight >= 4.0 then
            label.Color = Color3.fromRGB(255, 165, 0) -- Orange for 4+ KG
        elseif weight >= 2.0 then
            label.Color = Color3.fromRGB(100, 255, 100) -- Green for 2+ KG
        else
            label.Color = Color3.fromRGB(255, 255, 255) -- White for normal
        end
    else
        -- Start fake reroll animation if no pet name (egg not ready)
        label.Text = string.format("⏳ %s\n� Rolling...", eggName)
        label.Color = Color3.fromRGB(255, 255, 100) -- Yellow for rolling
        startFakeReroll(objectId, eggName)
    end
end


local function RemoveEsp(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end


    local objectId = object:GetAttribute("OBJECT_UUID")
    if espCache[objectId] then
        espCache[objectId]:Remove()
        espCache[objectId] = nil
    end
   
    activeEggs[objectId] = nil
end


local function UpdateAllEsp()
    for objectId, object in activeEggs do
        if not object or not object:IsDescendantOf(workspace) then
            activeEggs[objectId] = nil
            if espCache[objectId] then
                espCache[objectId].Visible = false
            end
            continue
        end


        local label = espCache[objectId]
        if label then
            local pos, onScreen = currentCamera:WorldToViewportPoint(object:GetPivot().Position)
            if onScreen then
                label.Position = Vector2.new(pos.X, pos.Y)
                label.Visible = true
            else
                label.Visible = false
            end
        end
    end
end


-- Initialize existing eggs
for _, object in collectionService:GetTagged("PetEggServer") do
    task.spawn(AddEsp, object)
end

-- Connect events and store connections for cleanup
connections[#connections + 1] = collectionService:GetInstanceAddedSignal("PetEggServer"):Connect(AddEsp)
connections[#connections + 1] = collectionService:GetInstanceRemovedSignal("PetEggServer"):Connect(RemoveEsp)

-- Hook egg ready event
local old; old = hookfunction(getconnections(replicatedStorage.GameEvents.EggReadyToHatch_RE.OnClientEvent)[1].Function, newcclosure(function(objectId, petName)
    -- Stop fake reroll animation
    if rerollAnimations[objectId] then
        rerollAnimations[objectId] = nil
    end

    -- Update with real pet
    UpdateEsp(objectId, petName)
    return old(objectId, petName)
end))

-- Connect render loop
connections[#connections + 1] = runService.PreRender:Connect(UpdateAllEsp)

-- Cleanup function for when script reruns
getgenv().EggESP_Cleanup = function()
    -- Disconnect all connections
    for _, connection in pairs(connections) do
        if connection then
            connection:Disconnect()
        end
    end

    -- Remove all ESP labels
    for _, label in pairs(espCache) do
        if label then
            label:Remove()
        end
    end

    -- Clear tables
    espCache = {}
    activeEggs = {}
    connections = {}
    rerollAnimations = {}

    print("🧹 Egg ESP cleaned up!")
end

print("🥚 ENHANCED EGG ESP + FAKE REROLLER LOADED!")
print("📊 Features: Pet Weight Display + Fake Reroll Animation")
print("🎨 Colors: Gold = 6+ KG, Orange = 4+ KG, Green = 2+ KG, White = Normal, Yellow = Rolling")
print("🎲 Fake Reroller: Shows random pets until egg is ready to hatch")
print("📈 Data Source: Real game pet registry for accurate weights")
print("🎯 Format: EggName | PetName | Weight")
print("🔄 Auto-reroll animation stops when egg is ready!")
print("✅ Clean UI with only essential information")