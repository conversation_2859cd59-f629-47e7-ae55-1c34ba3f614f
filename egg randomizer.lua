-- Cleanup previous instances
if getgenv().EggESP_Cleanup then
    getgenv().EggESP_Cleanup()
end

local replicatedStorage = game:GetService("ReplicatedStorage")
local collectionService = game:GetService("CollectionService")
local players = game:GetService("Players")
local runService = game:GetService("RunService")

local localPlayer = players.LocalPlayer
local currentCamera = workspace.CurrentCamera

local hatchFunction = getupvalue(getupvalue(getconnections(replicatedStorage.GameEvents.PetEggService.OnClientEvent)[1].Function, 1), 2)
local eggModels = getupvalue(hatchFunction, 1)
local eggPets = getupvalue(hatchFunction, 2)

-- Load egg data from the file (not from game)
local eggData = loadstring(game:HttpGet("https://raw.githubusercontent.com/YourRepo/eggdata.lua"))() -- Replace with your eggdata.lua content
-- For now, I'll use the data structure from your eggdata.lua file
local eggData = {
    ["Legendary Egg"] = {
        ["RarityData"] = {
            ["Items"] = {
                ["Silver Monkey"] = {["ItemOdd"] = 20, ["GeneratedPetData"] = {["WeightRange"] = {0.8, 2}, ["HugeChance"] = 0.001}},
                ["Cow"] = {["ItemOdd"] = 20, ["GeneratedPetData"] = {["WeightRange"] = {0.8, 2}, ["HugeChance"] = 0.001}},
                ["Sea Otter"] = {["ItemOdd"] = 5, ["GeneratedPetData"] = {["WeightRange"] = {0.8, 2}, ["HugeChance"] = 0.001}},
                ["Turtle"] = {["ItemOdd"] = 1, ["GeneratedPetData"] = {["WeightRange"] = {0.8, 2}, ["HugeChance"] = 0.001}},
                ["Polar Bear"] = {["ItemOdd"] = 1, ["GeneratedPetData"] = {["WeightRange"] = {0.8, 2}, ["HugeChance"] = 0.001}}
            }
        }
    },
    ["Zen Egg"] = {
        ["RarityData"] = {
            ["Items"] = {
                ["Shiba Inu"] = {["ItemOdd"] = 40, ["GeneratedPetData"] = {["WeightRange"] = {0.8, 2}, ["HugeChance"] = 0.001}},
                ["Nihonzaru"] = {["ItemOdd"] = 31, ["GeneratedPetData"] = {["WeightRange"] = {0.8, 2}, ["HugeChance"] = 0.001}},
                ["Tanuki"] = {["ItemOdd"] = 20.82, ["GeneratedPetData"] = {["WeightRange"] = {0.8, 2}, ["HugeChance"] = 0.001}},
                ["Tanchozuru"] = {["ItemOdd"] = 4.6, ["GeneratedPetData"] = {["WeightRange"] = {0.8, 2}, ["HugeChance"] = 0.001}},
                ["Kappa"] = {["ItemOdd"] = 3.5, ["GeneratedPetData"] = {["WeightRange"] = {0.8, 2}, ["HugeChance"] = 0.001}},
                ["Kitsune"] = {["ItemOdd"] = 0.08, ["GeneratedPetData"] = {["WeightRange"] = {0.8, 2}, ["HugeChance"] = 0.001}}
            }
        }
    }
}

local espCache = {}
local activeEggs = {}
local connections = {}
local rerollAnimations = {}


local function getObjectFromId(objectId)
    for eggModel in eggModels do
        if eggModel:GetAttribute("OBJECT_UUID") ~= objectId then continue end
        return eggModel
    end
end


-- Get random pet from egg data
local function getRandomPetFromEgg(eggName)
    if not eggData or not eggData[eggName] then return nil end

    local eggInfo = eggData[eggName]
    if not eggInfo.RarityData or not eggInfo.RarityData.Items then return nil end

    local pets = {}
    local totalWeight = 0

    for petName, petData in pairs(eggInfo.RarityData.Items) do
        local weight = petData.ItemOdd or 1
        table.insert(pets, {name = petName, weight = weight})
        totalWeight = totalWeight + weight
    end

    if #pets == 0 then return nil end

    local random = math.random() * totalWeight
    local currentWeight = 0

    for _, pet in ipairs(pets) do
        currentWeight = currentWeight + pet.weight
        if random <= currentWeight then
            return pet.name
        end
    end

    return pets[1].name
end

-- Get pet info with real weight, huge chance, and odds from egg data
local function getPetInfo(petName, eggName)
    -- If no pet name, get random one from egg
    if not petName and eggName then
        petName = getRandomPetFromEgg(eggName)
    end

    if not petRegistry or not eggData or not petName or not eggName then
        return {
            weight = "0.8 KG",
            rarity = "Common",
            hugeChance = "0.1%",
            odds = "1/100",
            isHuge = false,
            petName = petName or "Unknown"
        }
    end

    local petData = petRegistry[petName]
    local eggInfo = eggData[eggName]

    if not petData then
        -- Return default values if pet not found
        return {
            weight = "0.8 KG",
            rarity = "Common",
            hugeChance = "0.1%",
            odds = "1/100",
            isHuge = false,
            petName = petName
        }
    end

    -- Calculate real weight from sell price (KG = SellPrice / 1,000,000)
    local weight = petData.SellPrice and (petData.SellPrice / 1000000) or 0.8
    local rarity = petData.Rarity or "Common"

    -- Get huge chance from egg data (default 0.1%)
    local hugeChance = 0.1
    if eggInfo and eggInfo.RarityData and eggInfo.RarityData.Items and eggInfo.RarityData.Items[petName] then
        local petEggData = eggInfo.RarityData.Items[petName]
        if petEggData.GeneratedPetData and petEggData.GeneratedPetData.HugeChance then
            hugeChance = petEggData.GeneratedPetData.HugeChance * 100
        end
    end

    -- Get odds from egg data
    local odds = "1/100"
    if eggInfo and eggInfo.RarityData and eggInfo.RarityData.Items and eggInfo.RarityData.Items[petName] then
        local petEggData = eggInfo.RarityData.Items[petName]
        if petEggData.ItemOdd then
            odds = string.format("1/%d", math.floor(100 / petEggData.ItemOdd))
        end
    end

    local isHuge = weight >= 4.0

    return {
        weight = string.format("%.1f KG", weight),
        rarity = rarity,
        hugeChance = string.format("%.1f%%", hugeChance),
        odds = odds,
        isHuge = isHuge,
        petName = petName
    }
end

-- Fake reroller animation
local rerollAnimations = {}
local function startFakeReroll(objectId, eggName)
    if rerollAnimations[objectId] then return end

    rerollAnimations[objectId] = true

    task.spawn(function()
        local rerollCount = 0
        local maxRerolls = math.random(15, 30)

        while rerollAnimations[objectId] and rerollCount < maxRerolls do
            local randomPet = getRandomPetFromEgg(eggName)
            if randomPet then
                UpdateEsp(objectId, randomPet)
            end

            rerollCount = rerollCount + 1
            task.wait(0.1)
        end

        -- Final result
        local finalPet = getRandomPetFromEgg(eggName)
        if finalPet then
            UpdateEsp(objectId, finalPet)
        end

        rerollAnimations[objectId] = nil
    end)
end

local function UpdateEsp(objectId, petName)
    local object = getObjectFromId(objectId)
    if not object or not espCache[objectId] then return end

    local eggName = object:GetAttribute("EggName")
    local petInfo = getPetInfo(petName, eggName)

    -- Use fake display if enabled
    local displayWeight = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeWeight .. " KG" or petInfo.weight
    local displayHuge = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeHugeChance .. "%" or petInfo.hugeChance
    local displayRarity = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeRarity or petInfo.rarity
    local displayOdds = FAKE_DISPLAY.enabled and "1/1" or petInfo.odds
    local displayPetName = petInfo.petName or petName or "?"

    -- Color coding: Gold = Huge, Green = Rare, White = Normal, Purple = Fake
    local color = Color3.fromRGB(255, 255, 255) -- Default white
    if FAKE_DISPLAY.enabled then
        color = Color3.fromRGB(255, 100, 255) -- Purple for fake
    elseif petInfo.isHuge then
        color = Color3.fromRGB(255, 215, 0) -- Gold for huge
    elseif petInfo.rarity == "Divine" or petInfo.rarity == "Prismatic" then
        color = Color3.fromRGB(255, 100, 255) -- Purple for divine/prismatic
    elseif petInfo.rarity == "Legendary" or petInfo.rarity == "Mythical" then
        color = Color3.fromRGB(255, 165, 0) -- Orange for legendary/mythical
    elseif petInfo.rarity == "Rare" then
        color = Color3.fromRGB(100, 255, 100) -- Green for rare
    end

    espCache[objectId].Text = string.format("%s | %s\n💎 %s | ⚖️ %s | 🎯 %s | 🎲 %s",
        eggName, displayPetName, displayRarity, displayWeight, displayHuge, displayOdds)
    espCache[objectId].Color = color
end


local function AddEsp(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end

    local eggName = object:GetAttribute("EggName")
    local petName = eggPets[object:GetAttribute("OBJECT_UUID")]

    local objectId = object:GetAttribute("OBJECT_UUID")
    if not objectId then return end

    -- Get initial pet info (will show random pet if no pet name)
    local petInfo = getPetInfo(petName, eggName)

    -- Use fake display if enabled
    local displayWeight = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeWeight .. " KG" or petInfo.weight
    local displayHuge = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeHugeChance .. "%" or petInfo.hugeChance
    local displayRarity = FAKE_DISPLAY.enabled and FAKE_DISPLAY.fakeRarity or petInfo.rarity
    local displayOdds = FAKE_DISPLAY.enabled and "1/1" or petInfo.odds
    local displayPetName = petInfo.petName or petName or "?"

    -- Color coding: Gold = Huge, Green = Rare, White = Normal, Purple = Fake
    local color = Color3.fromRGB(255, 255, 255) -- Default white
    if FAKE_DISPLAY.enabled then
        color = Color3.fromRGB(255, 100, 255) -- Purple for fake
    elseif petInfo.isHuge then
        color = Color3.fromRGB(255, 215, 0) -- Gold for huge
    elseif petInfo.rarity == "Divine" or petInfo.rarity == "Prismatic" then
        color = Color3.fromRGB(255, 100, 255) -- Purple for divine/prismatic
    elseif petInfo.rarity == "Legendary" or petInfo.rarity == "Mythical" then
        color = Color3.fromRGB(255, 165, 0) -- Orange for legendary/mythical
    elseif petInfo.rarity == "Rare" then
        color = Color3.fromRGB(100, 255, 100) -- Green for rare
    end

    local label = Drawing.new("Text")
    label.Text = string.format("%s | %s\n💎 %s | ⚖️ %s | 🎯 %s | 🎲 %s",
        eggName, displayPetName, displayRarity, displayWeight, displayHuge, displayOdds)
    label.Size = 14
    label.Color = color
    label.Outline = true
    label.OutlineColor = Color3.new(0, 0, 0)
    label.Center = true
    label.Visible = false

    espCache[objectId] = label
    activeEggs[objectId] = object

    -- Start fake reroll animation if no pet name (egg not ready)
    if not petName then
        startFakeReroll(objectId, eggName)
    end
end


local function RemoveEsp(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end


    local objectId = object:GetAttribute("OBJECT_UUID")
    if espCache[objectId] then
        espCache[objectId]:Remove()
        espCache[objectId] = nil
    end
   
    activeEggs[objectId] = nil
end


local function UpdateAllEsp()
    for objectId, object in activeEggs do
        if not object or not object:IsDescendantOf(workspace) then
            activeEggs[objectId] = nil
            if espCache[objectId] then
                espCache[objectId].Visible = false
            end
            continue
        end


        local label = espCache[objectId]
        if label then
            local pos, onScreen = currentCamera:WorldToViewportPoint(object:GetPivot().Position)
            if onScreen then
                label.Position = Vector2.new(pos.X, pos.Y)
                label.Visible = true
            else
                label.Visible = false
            end
        end
    end
end


-- Initialize existing eggs
for _, object in collectionService:GetTagged("PetEggServer") do
    task.spawn(AddEsp, object)
end

-- Connect events and store connections for cleanup
connections[#connections + 1] = collectionService:GetInstanceAddedSignal("PetEggServer"):Connect(AddEsp)
connections[#connections + 1] = collectionService:GetInstanceRemovedSignal("PetEggServer"):Connect(RemoveEsp)

-- Hook egg ready event
local old; old = hookfunction(getconnections(replicatedStorage.GameEvents.EggReadyToHatch_RE.OnClientEvent)[1].Function, newcclosure(function(objectId, petName)
    -- Stop fake reroll animation
    if rerollAnimations[objectId] then
        rerollAnimations[objectId] = nil
    end

    -- Update with real pet
    UpdateEsp(objectId, petName)
    return old(objectId, petName)
end))

-- Connect render loop
connections[#connections + 1] = runService.PreRender:Connect(UpdateAllEsp)

-- Cleanup function for when script reruns
getgenv().EggESP_Cleanup = function()
    -- Disconnect all connections
    for _, connection in pairs(connections) do
        if connection then
            connection:Disconnect()
        end
    end

    -- Remove all ESP labels
    for _, label in pairs(espCache) do
        if label then
            label:Remove()
        end
    end

    -- Clear tables
    espCache = {}
    activeEggs = {}
    connections = {}
    rerollAnimations = {}

    print("🧹 Egg ESP cleaned up!")
end

print("🥚 ADVANCED EGG ESP + FAKE REROLLER LOADED!")
print("📊 Features: Real Weight, Huge Chance, Rarity, Odds, Fake Reroller")
print("🎨 Colors: Gold = Huge, Purple = Divine/Prismatic, Orange = Legendary/Mythical, Green = Rare, White = Common")
print("🔧 Fake Display: " .. (FAKE_DISPLAY.enabled and "ENABLED" or "DISABLED"))
print("🎲 Fake Reroller: Shows random pets until egg is ready to hatch")
print("📈 Data Source: Real game egg and pet registry")
print("🎯 Format: EggName | PetName")
print("💎 Rarity | ⚖️ Weight | 🎯 HugeChance | 🎲 Odds")
print("🔄 Auto-reroll animation stops when egg is ready!")