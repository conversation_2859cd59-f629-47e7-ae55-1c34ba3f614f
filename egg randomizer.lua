if getgenv().EggDisplay_Cleanup then
    getgenv().EggDisplay_Cleanup()
end

local replicatedStorage = game:GetService("ReplicatedStorage")
local collectionService = game:GetService("CollectionService")
local players = game:GetService("Players")
local runService = game:GetService("RunService")
local coreGui = game:GetService("CoreGui")
local tweenService = game:GetService("TweenService")
local userInputService = game:GetService("UserInputService")

local localPlayer = players.LocalPlayer
local currentCamera = workspace.CurrentCamera

local hatchFunction = getupvalue(getupvalue(getconnections(replicatedStorage.GameEvents.PetEggService.OnClientEvent)[1].Function, 1), 2)
local eggModels = getupvalue(hatchFunction, 1)
local eggPets = getupvalue(hatchFunction, 2)

local petRegistry = nil
pcall(function()
    petRegistry = require(replicatedStorage.Data.PetRegistry.PetList)
end)

local eggData = nil
pcall(function()
    eggData = require(replicatedStorage.Data.PetRegistry.PetEggs)
end)

local espCache = {}
local activeEggs = {}
local connections = {}
local selectedEgg = nil

local gui = Instance.new("ScreenGui")
gui.Name = "EggDisplay"
gui.Parent = coreGui

local mainFrame = Instance.new("Frame")
mainFrame.Name = "MainFrame"
mainFrame.Size = UDim2.new(0, 300, 0, 200)
mainFrame.Position = UDim2.new(0, 50, 0, 50)
mainFrame.BackgroundColor3 = Color3.fromRGB(25, 25, 35)
mainFrame.BorderSizePixel = 0
mainFrame.Parent = gui

local corner = Instance.new("UICorner")
corner.CornerRadius = UDim.new(0, 12)
corner.Parent = mainFrame

local gradient = Instance.new("UIGradient")
gradient.Color = ColorSequence.new{
    ColorSequenceKeypoint.new(0, Color3.fromRGB(35, 35, 50)),
    ColorSequenceKeypoint.new(1, Color3.fromRGB(15, 15, 25))
}
gradient.Rotation = 45
gradient.Parent = mainFrame

local titleLabel = Instance.new("TextLabel")
titleLabel.Name = "Title"
titleLabel.Size = UDim2.new(1, 0, 0, 40)
titleLabel.Position = UDim2.new(0, 0, 0, 0)
titleLabel.BackgroundTransparency = 1
titleLabel.Text = "🥚 Egg Reroller"
titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
titleLabel.TextScaled = true
titleLabel.Font = Enum.Font.GothamBold
titleLabel.Parent = mainFrame

local rerollButton = Instance.new("TextButton")
rerollButton.Name = "RerollButton"
rerollButton.Size = UDim2.new(0.8, 0, 0, 50)
rerollButton.Position = UDim2.new(0.1, 0, 0.3, 0)
rerollButton.BackgroundColor3 = Color3.fromRGB(255, 100, 100)
rerollButton.BorderSizePixel = 0
rerollButton.Text = "🎲 REROLL SELECTED EGG"
rerollButton.TextColor3 = Color3.fromRGB(255, 255, 255)
rerollButton.TextScaled = true
rerollButton.Font = Enum.Font.GothamBold
rerollButton.Parent = mainFrame

local rerollCorner = Instance.new("UICorner")
rerollCorner.CornerRadius = UDim.new(0, 8)
rerollCorner.Parent = rerollButton

local statusLabel = Instance.new("TextLabel")
statusLabel.Name = "Status"
statusLabel.Size = UDim2.new(0.9, 0, 0, 30)
statusLabel.Position = UDim2.new(0.05, 0, 0.6, 0)
statusLabel.BackgroundTransparency = 1
statusLabel.Text = "Click an egg to select it"
statusLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
statusLabel.TextScaled = true
statusLabel.Font = Enum.Font.Gotham
statusLabel.Parent = mainFrame

local eggInfoLabel = Instance.new("TextLabel")
eggInfoLabel.Name = "EggInfo"
eggInfoLabel.Size = UDim2.new(0.9, 0, 0, 40)
eggInfoLabel.Position = UDim2.new(0.05, 0, 0.75, 0)
eggInfoLabel.BackgroundTransparency = 1
eggInfoLabel.Text = ""
eggInfoLabel.TextColor3 = Color3.fromRGB(255, 215, 0)
eggInfoLabel.TextScaled = true
eggInfoLabel.Font = Enum.Font.GothamBold
eggInfoLabel.Parent = mainFrame

local function getObjectFromId(objectId)
    for eggModel in eggModels do
        if eggModel:GetAttribute("OBJECT_UUID") ~= objectId then continue end
        return eggModel
    end
end


local function getRandomPetFromEgg(eggName)
    if not eggData or not eggData[eggName] then return nil end

    local eggInfo = eggData[eggName]
    if not eggInfo.RarityData or not eggInfo.RarityData.Items then return nil end

    local pets = {}
    local totalWeight = 0

    for petName, petData in pairs(eggInfo.RarityData.Items) do
        local weight = petData.ItemOdd or 1
        table.insert(pets, {name = petName, weight = weight})
        totalWeight = totalWeight + weight
    end

    if #pets == 0 then return nil end

    local random = math.random() * totalWeight
    local currentWeight = 0

    for _, pet in ipairs(pets) do
        currentWeight = currentWeight + pet.weight
        if random <= currentWeight then
            return pet.name
        end
    end

    return pets[1].name
end

local function getPetWeight(petName)
    if not petRegistry or not petName then
        return math.random(80, 200) / 100
    end

    local petData = petRegistry[petName]
    if not petData then
        return math.random(80, 200) / 100
    end

    local weight = petData.SellPrice and (petData.SellPrice / 1000000) or math.random(80, 200) / 100
    return weight
end

local function manualReroll(objectId)
    if not objectId or not espCache[objectId] then return end

    local object = getObjectFromId(objectId)
    if not object then return end

    local eggName = object:GetAttribute("EggName")
    local randomPet = getRandomPetFromEgg(eggName)

    if randomPet then
        local weight = getPetWeight(randomPet)

        local frame = espCache[objectId].frame
        local nameLabel = espCache[objectId].nameLabel
        local weightLabel = espCache[objectId].weightLabel
        local glowFrame = espCache[objectId].glowFrame

        nameLabel.Text = randomPet
        weightLabel.Text = string.format("⚖️ %.1f KG", weight)

        local color = Color3.fromRGB(255, 255, 255)
        if weight >= 6.0 then
            color = Color3.fromRGB(255, 215, 0)
            glowFrame.BackgroundColor3 = Color3.fromRGB(255, 215, 0)
            glowFrame.BackgroundTransparency = 0.7
        elseif weight >= 4.0 then
            color = Color3.fromRGB(255, 165, 0)
            glowFrame.BackgroundColor3 = Color3.fromRGB(255, 165, 0)
            glowFrame.BackgroundTransparency = 0.8
        elseif weight >= 2.0 then
            color = Color3.fromRGB(100, 255, 100)
            glowFrame.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
            glowFrame.BackgroundTransparency = 0.8
        else
            glowFrame.BackgroundTransparency = 1
        end

        nameLabel.TextColor3 = color
        weightLabel.TextColor3 = color

        local tween = tweenService:Create(frame, TweenInfo.new(0.2, Enum.EasingStyle.Back), {Size = UDim2.new(0, 160, 0, 80)})
        tween:Play()
        tween.Completed:Connect(function()
            tweenService:Create(frame, TweenInfo.new(0.2, Enum.EasingStyle.Back), {Size = UDim2.new(0, 150, 0, 70)}):Play()
        end)
    end
end

local function UpdateEsp(objectId, petName)
    local object = getObjectFromId(objectId)
    if not object or not espCache[objectId] then return end

    local weight = getPetWeight(petName)

    local frame = espCache[objectId].frame
    local nameLabel = espCache[objectId].nameLabel
    local weightLabel = espCache[objectId].weightLabel
    local glowFrame = espCache[objectId].glowFrame
    local statusLabel = espCache[objectId].statusLabel

    nameLabel.Text = petName or "?"
    weightLabel.Text = string.format("⚖️ %.1f KG", weight)
    statusLabel.Text = "✅ READY"
    statusLabel.TextColor3 = Color3.fromRGB(100, 255, 100)

    local color = Color3.fromRGB(255, 255, 255)
    if weight >= 6.0 then
        color = Color3.fromRGB(255, 215, 0)
        glowFrame.BackgroundColor3 = Color3.fromRGB(255, 215, 0)
        glowFrame.BackgroundTransparency = 0.7
    elseif weight >= 4.0 then
        color = Color3.fromRGB(255, 165, 0)
        glowFrame.BackgroundColor3 = Color3.fromRGB(255, 165, 0)
        glowFrame.BackgroundTransparency = 0.8
    elseif weight >= 2.0 then
        color = Color3.fromRGB(100, 255, 100)
        glowFrame.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
        glowFrame.BackgroundTransparency = 0.8
    else
        glowFrame.BackgroundTransparency = 1
    end

    nameLabel.TextColor3 = color
    weightLabel.TextColor3 = color
end


local function AddEsp(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end

    local eggName = object:GetAttribute("EggName")
    local petName = eggPets[object:GetAttribute("OBJECT_UUID")]

    local objectId = object:GetAttribute("OBJECT_UUID")
    if not objectId then return end

    local label = Drawing.new("Text")
    label.Size = 16
    label.Color = Color3.fromRGB(255, 255, 255)
    label.Outline = true
    label.OutlineColor = Color3.new(0, 0, 0)
    label.Center = true
    label.Visible = false
    label.Font = Drawing.Fonts.Plex

    espCache[objectId] = label
    activeEggs[objectId] = object

    if petName then
        local weight = getPetWeight(petName)
        label.Text = string.format("✅ %s\n🐾 %s | ⚖️ %.1f KG", eggName, petName, weight)

        if weight >= 6.0 then
            label.Color = Color3.fromRGB(255, 215, 0)
        elseif weight >= 4.0 then
            label.Color = Color3.fromRGB(255, 165, 0)
        elseif weight >= 2.0 then
            label.Color = Color3.fromRGB(100, 255, 100)
        else
            label.Color = Color3.fromRGB(255, 255, 255)
        end
    else
        label.Text = string.format("⏳ %s\n� Rolling...", eggName)
        label.Color = Color3.fromRGB(255, 255, 100)
        startRollAnimation(objectId, eggName)
    end
end


local function RemoveEsp(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end


    local objectId = object:GetAttribute("OBJECT_UUID")
    if espCache[objectId] then
        espCache[objectId]:Remove()
        espCache[objectId] = nil
    end
   
    activeEggs[objectId] = nil
end


local function UpdateAllEsp()
    for objectId, object in activeEggs do
        if not object or not object:IsDescendantOf(workspace) then
            activeEggs[objectId] = nil
            if espCache[objectId] then
                espCache[objectId].Visible = false
            end
            continue
        end


        local label = espCache[objectId]
        if label then
            local pos, onScreen = currentCamera:WorldToViewportPoint(object:GetPivot().Position)
            if onScreen then
                label.Position = Vector2.new(pos.X, pos.Y)
                label.Visible = true
            else
                label.Visible = false
            end
        end
    end
end


-- Initialize existing eggs
for _, object in collectionService:GetTagged("PetEggServer") do
    task.spawn(AddEsp, object)
end

-- Connect events and store connections for cleanup
connections[#connections + 1] = collectionService:GetInstanceAddedSignal("PetEggServer"):Connect(AddEsp)
connections[#connections + 1] = collectionService:GetInstanceRemovedSignal("PetEggServer"):Connect(RemoveEsp)

local old; old = hookfunction(getconnections(replicatedStorage.GameEvents.EggReadyToHatch_RE.OnClientEvent)[1].Function, newcclosure(function(objectId, petName)
    if rollAnimations[objectId] then
        rollAnimations[objectId] = nil
    end

    UpdateEsp(objectId, petName)
    return old(objectId, petName)
end))

-- Connect render loop
connections[#connections + 1] = runService.PreRender:Connect(UpdateAllEsp)

getgenv().EggDisplay_Cleanup = function()
    for _, connection in pairs(connections) do
        if connection then
            connection:Disconnect()
        end
    end

    for _, label in pairs(espCache) do
        if label then
            label:Remove()
        end
    end

    if gui then
        gui:Destroy()
    end

    espCache = {}
    activeEggs = {}
    connections = {}
    rollAnimations = {}
end