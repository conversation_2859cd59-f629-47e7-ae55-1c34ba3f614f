if getgenv().EggReroller_Cleanup then
    getgenv().EggReroller_Cleanup()
end

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local CollectionService = game:GetService("CollectionService")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local CoreGui = game:GetService("CoreGui")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

local LocalPlayer = Players.LocalPlayer

local HatchFunction = getupvalue(getupvalue(getconnections(ReplicatedStorage.GameEvents.PetEggService.OnClientEvent)[1].Function, 1), 2)
local EggModels = getupvalue(HatchFunction, 1)
local EggPets = getupvalue(HatchFunction, 2)

local PetRegistry = nil
pcall(function()
    PetRegistry = require(ReplicatedStorage.Data.PetRegistry.PetList)
end)

local EggData = nil
pcall(function()
    EggData = require(ReplicatedStorage.Data.PetRegistry.PetEggs)
end)

local EspCache = {}
local ActiveEggs = {}
local ReadyEggs = {}
local Connections = {}
local RerollCooldown = 1.5
local LastRerollTime = {}
local RollAnimations = {}
local IsMinimized = false

local Stats = {
    TotalEggs = 0,
    RollingEggs = 0,
    ReadyEggs = 0,
    RerollsPerformed = 0
}

local MainGui = Instance.new("ScreenGui")
MainGui.Name = "EggReroller"
MainGui.Parent = CoreGui
MainGui.ResetOnSpawn = false

local MainFrame = Instance.new("Frame")
MainFrame.Size = UDim2.new(0, 233, 0, 280)
MainFrame.Position = UDim2.new(0.5, -116, 0.5, -140)
MainFrame.BackgroundColor3 = Color3.fromRGB(8, 8, 12)
MainFrame.BorderSizePixel = 0
MainFrame.Parent = MainGui

local MinimizedFrame = Instance.new("Frame")
MinimizedFrame.Size = UDim2.new(0, 33, 0, 33)
MinimizedFrame.Position = UDim2.new(1, -40, 0.5, -16)
MinimizedFrame.BackgroundColor3 = Color3.fromRGB(8, 8, 12)
MinimizedFrame.BorderSizePixel = 0
MinimizedFrame.Visible = false
MinimizedFrame.Parent = MainGui

local MainCorner = Instance.new("UICorner")
MainCorner.CornerRadius = UDim.new(0, 12)
MainCorner.Parent = MainFrame

local MainStroke = Instance.new("UIStroke")
MainStroke.Color = Color3.fromRGB(60, 60, 70)
MainStroke.Thickness = 2
MainStroke.Parent = MainFrame

local MinimizedCorner = Instance.new("UICorner")
MinimizedCorner.CornerRadius = UDim.new(0, 25)
MinimizedCorner.Parent = MinimizedFrame

local MinimizedIcon = Instance.new("TextLabel")
MinimizedIcon.Size = UDim2.new(1, 0, 1, 0)
MinimizedIcon.BackgroundTransparency = 1
MinimizedIcon.Text = "E"
MinimizedIcon.TextColor3 = Color3.fromRGB(200, 200, 200)
MinimizedIcon.TextSize = 24
MinimizedIcon.Font = Enum.Font.SourceSansBold
MinimizedIcon.Parent = MinimizedFrame

local HeaderFrame = Instance.new("Frame")
HeaderFrame.Size = UDim2.new(1, 0, 0, 40)
HeaderFrame.Position = UDim2.new(0, 0, 0, 0)
HeaderFrame.BackgroundColor3 = Color3.fromRGB(0, 255, 255)
HeaderFrame.BorderSizePixel = 0
HeaderFrame.Parent = MainFrame

local HeaderCorner = Instance.new("UICorner")
HeaderCorner.CornerRadius = UDim.new(0, 8)
HeaderCorner.Parent = HeaderFrame

local HeaderGlow = Instance.new("UIStroke")
HeaderGlow.Color = Color3.fromRGB(0, 255, 255)
HeaderGlow.Thickness = 2
HeaderGlow.Transparency = 0.3
HeaderGlow.Parent = HeaderFrame

local TitleLabel = Instance.new("TextLabel")
TitleLabel.Size = UDim2.new(0.7, 0, 1, 0)
TitleLabel.Position = UDim2.new(0, 10, 0, 0)
TitleLabel.BackgroundTransparency = 1
TitleLabel.Text = "⚡ STYLISH ESP"
TitleLabel.TextColor3 = Color3.fromRGB(0, 0, 0)
TitleLabel.TextSize = 16
TitleLabel.Font = Enum.Font.GothamBold
TitleLabel.TextXAlignment = Enum.TextXAlignment.Left
TitleLabel.Parent = HeaderFrame

local SubtitleLabel = Instance.new("TextLabel")
SubtitleLabel.Size = UDim2.new(0.7, 0, 0.4, 0)
SubtitleLabel.Position = UDim2.new(0, 10, 0.6, 0)
SubtitleLabel.BackgroundTransparency = 1
SubtitleLabel.Text = "Advanced Reroller"
SubtitleLabel.TextColor3 = Color3.fromRGB(0, 0, 0)
SubtitleLabel.TextSize = 9
SubtitleLabel.Font = Enum.Font.Gotham
SubtitleLabel.TextXAlignment = Enum.TextXAlignment.Left
SubtitleLabel.Parent = HeaderFrame

local MinimizeButton = Instance.new("TextButton")
MinimizeButton.Size = UDim2.new(0, 20, 0, 20)
MinimizeButton.Position = UDim2.new(1, -50, 0, 10)
MinimizeButton.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
MinimizeButton.BorderSizePixel = 0
MinimizeButton.Text = "_"
MinimizeButton.TextColor3 = Color3.fromRGB(0, 255, 255)
MinimizeButton.TextSize = 12
MinimizeButton.Font = Enum.Font.GothamBold
MinimizeButton.Parent = HeaderFrame

local CloseButton = Instance.new("TextButton")
CloseButton.Size = UDim2.new(0, 20, 0, 20)
CloseButton.Position = UDim2.new(1, -27, 0, 10)
CloseButton.BackgroundColor3 = Color3.fromRGB(255, 0, 0)
CloseButton.BorderSizePixel = 0
CloseButton.Text = "X"
CloseButton.TextColor3 = Color3.fromRGB(0, 0, 0)
CloseButton.TextSize = 11
CloseButton.Font = Enum.Font.GothamBold
CloseButton.Parent = HeaderFrame

local CloseCorner = Instance.new("UICorner")
CloseCorner.CornerRadius = UDim.new(0, 6)
CloseCorner.Parent = CloseButton

local MinimizeCorner = Instance.new("UICorner")
MinimizeCorner.CornerRadius = UDim.new(0, 6)
MinimizeCorner.Parent = MinimizeButton

CloseButton.MouseEnter:Connect(function()
    TweenService:Create(CloseButton, TweenInfo.new(0.2), {
        BackgroundColor3 = Color3.fromRGB(220, 70, 70)
    }):Play()
end)

CloseButton.MouseLeave:Connect(function()
    TweenService:Create(CloseButton, TweenInfo.new(0.2), {
        BackgroundColor3 = Color3.fromRGB(180, 50, 50)
    }):Play()
end)

MinimizeButton.MouseEnter:Connect(function()
    TweenService:Create(MinimizeButton, TweenInfo.new(0.2), {
        BackgroundColor3 = Color3.fromRGB(70, 70, 80)
    }):Play()
end)

MinimizeButton.MouseLeave:Connect(function()
    TweenService:Create(MinimizeButton, TweenInfo.new(0.2), {
        BackgroundColor3 = Color3.fromRGB(50, 50, 60)
    }):Play()
end)

local function ToggleMinimize()
    IsMinimized = not IsMinimized
    if IsMinimized then
        TweenService:Create(MainFrame, TweenInfo.new(0.3, Enum.EasingStyle.Quart), {
            Position = UDim2.new(1, -40, 0.5, -16),
            Size = UDim2.new(0, 0, 0, 0)
        }):Play()
        MinimizedFrame.Visible = true
        TweenService:Create(MinimizedFrame, TweenInfo.new(0.3, Enum.EasingStyle.Quart), {
            Size = UDim2.new(0, 33, 0, 33)
        }):Play()
    else
        TweenService:Create(MinimizedFrame, TweenInfo.new(0.3, Enum.EasingStyle.Quart), {
            Size = UDim2.new(0, 0, 0, 0)
        }):Play()
        task.wait(0.3)
        MinimizedFrame.Visible = false
        TweenService:Create(MainFrame, TweenInfo.new(0.3, Enum.EasingStyle.Quart), {
            Position = UDim2.new(0.5, -116, 0.5, -140),
            Size = UDim2.new(0, 233, 0, 280)
        }):Play()
    end
end

MinimizeButton.MouseButton1Click:Connect(ToggleMinimize)
MinimizedFrame.InputBegan:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseButton1 then
        ToggleMinimize()
    end
end)

local ControlPanel = Instance.new("Frame")
ControlPanel.Size = UDim2.new(0.9, 0, 0, 40)
ControlPanel.Position = UDim2.new(0.05, 0, 0, 47)
ControlPanel.BackgroundColor3 = Color3.fromRGB(5, 5, 8)
ControlPanel.BorderSizePixel = 0
ControlPanel.Parent = MainFrame

local ControlCorner = Instance.new("UICorner")
ControlCorner.CornerRadius = UDim.new(0, 6)
ControlCorner.Parent = ControlPanel

local ControlStroke = Instance.new("UIStroke")
ControlStroke.Color = Color3.fromRGB(0, 255, 255)
ControlStroke.Thickness = 2
ControlStroke.Transparency = 0.4
ControlStroke.Parent = ControlPanel

local RerollButton = Instance.new("TextButton")
RerollButton.Size = UDim2.new(0.9, 0, 0, 27)
RerollButton.Position = UDim2.new(0.05, 0, 0, 7)
RerollButton.BackgroundColor3 = Color3.fromRGB(0, 255, 255)
RerollButton.BorderSizePixel = 0
RerollButton.Text = "⚡ REROLL EGG"
RerollButton.TextColor3 = Color3.fromRGB(0, 0, 0)
RerollButton.TextSize = 12
RerollButton.Font = Enum.Font.GothamBold
RerollButton.Parent = ControlPanel

local RerollCorner = Instance.new("UICorner")
RerollCorner.CornerRadius = UDim.new(0, 4)
RerollCorner.Parent = RerollButton

local RerollGlow = Instance.new("UIStroke")
RerollGlow.Color = Color3.fromRGB(0, 255, 255)
RerollGlow.Thickness = 2
RerollGlow.Transparency = 0.3
RerollGlow.Parent = RerollButton

local StatusPanel = Instance.new("Frame")
StatusPanel.Size = UDim2.new(0.9, 0, 0, 33)
StatusPanel.Position = UDim2.new(0.05, 0, 0, 93)
StatusPanel.BackgroundColor3 = Color3.fromRGB(5, 5, 8)
StatusPanel.BorderSizePixel = 0
StatusPanel.Parent = MainFrame

local StatusCorner = Instance.new("UICorner")
StatusCorner.CornerRadius = UDim.new(0, 6)
StatusCorner.Parent = StatusPanel

local StatusStroke = Instance.new("UIStroke")
StatusStroke.Color = Color3.fromRGB(0, 255, 255)
StatusStroke.Thickness = 2
StatusStroke.Transparency = 0.4
StatusStroke.Parent = StatusPanel

local StatusLabel = Instance.new("TextLabel")
StatusLabel.Size = UDim2.new(1, 0, 0.6, 0)
StatusLabel.Position = UDim2.new(0, 0, 0, 0)
StatusLabel.BackgroundTransparency = 1
StatusLabel.Text = "Scanning for eggs..."
StatusLabel.TextColor3 = Color3.fromRGB(0, 255, 255)
StatusLabel.TextSize = 11
StatusLabel.Font = Enum.Font.GothamBold
StatusLabel.Parent = StatusPanel

local DetailsLabel = Instance.new("TextLabel")
DetailsLabel.Size = UDim2.new(1, 0, 0.4, 0)
DetailsLabel.Position = UDim2.new(0, 0, 0.6, 0)
DetailsLabel.BackgroundTransparency = 1
DetailsLabel.Text = "Ready to reroll manually"
DetailsLabel.TextColor3 = Color3.fromRGB(180, 180, 180)
DetailsLabel.TextSize = 8
DetailsLabel.Font = Enum.Font.Gotham
DetailsLabel.Parent = StatusPanel

local StatsPanel = Instance.new("Frame")
StatsPanel.Size = UDim2.new(0.9, 0, 0, 120)
StatsPanel.Position = UDim2.new(0.05, 0, 0, 133)
StatsPanel.BackgroundColor3 = Color3.fromRGB(5, 5, 8)
StatsPanel.BorderSizePixel = 0
StatsPanel.Parent = MainFrame

local StatsCorner = Instance.new("UICorner")
StatsCorner.CornerRadius = UDim.new(0, 6)
StatsCorner.Parent = StatsPanel

local StatsStroke = Instance.new("UIStroke")
StatsStroke.Color = Color3.fromRGB(0, 255, 255)
StatsStroke.Thickness = 2
StatsStroke.Transparency = 0.4
StatsStroke.Parent = StatsPanel

local StatsTitle = Instance.new("TextLabel")
StatsTitle.Size = UDim2.new(1, 0, 0, 20)
StatsTitle.Position = UDim2.new(0, 0, 0, 3)
StatsTitle.BackgroundTransparency = 1
StatsTitle.Text = "📊 STATISTICS"
StatsTitle.TextColor3 = Color3.fromRGB(0, 255, 255)
StatsTitle.TextSize = 12
StatsTitle.Font = Enum.Font.GothamBold
StatsTitle.Parent = StatsPanel

local TotalEggsLabel = Instance.new("TextLabel")
TotalEggsLabel.Size = UDim2.new(0.5, 0, 0, 17)
TotalEggsLabel.Position = UDim2.new(0, 10, 0, 27)
TotalEggsLabel.BackgroundTransparency = 1
TotalEggsLabel.Text = "Total: 0"
TotalEggsLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
TotalEggsLabel.TextSize = 9
TotalEggsLabel.Font = Enum.Font.Gotham
TotalEggsLabel.TextXAlignment = Enum.TextXAlignment.Left
TotalEggsLabel.Parent = StatsPanel

local RollingEggsLabel = Instance.new("TextLabel")
RollingEggsLabel.Size = UDim2.new(0.5, 0, 0, 17)
RollingEggsLabel.Position = UDim2.new(0.5, 0, 0, 27)
RollingEggsLabel.BackgroundTransparency = 1
RollingEggsLabel.Text = "Rolling: 0"
RollingEggsLabel.TextColor3 = Color3.fromRGB(255, 200, 100)
RollingEggsLabel.TextSize = 9
RollingEggsLabel.Font = Enum.Font.Gotham
RollingEggsLabel.TextXAlignment = Enum.TextXAlignment.Left
RollingEggsLabel.Parent = StatsPanel

local ReadyEggsLabel = Instance.new("TextLabel")
ReadyEggsLabel.Size = UDim2.new(0.5, 0, 0, 17)
ReadyEggsLabel.Position = UDim2.new(0, 10, 0, 47)
ReadyEggsLabel.BackgroundTransparency = 1
ReadyEggsLabel.Text = "Ready: 0"
ReadyEggsLabel.TextColor3 = Color3.fromRGB(0, 255, 0)
ReadyEggsLabel.TextSize = 9
ReadyEggsLabel.Font = Enum.Font.Gotham
ReadyEggsLabel.TextXAlignment = Enum.TextXAlignment.Left
ReadyEggsLabel.Parent = StatsPanel

local RerollCountLabel = Instance.new("TextLabel")
RerollCountLabel.Size = UDim2.new(0.5, 0, 0, 17)
RerollCountLabel.Position = UDim2.new(0.5, 0, 0, 47)
RerollCountLabel.BackgroundTransparency = 1
RerollCountLabel.Text = "Rerolls: 0"
RerollCountLabel.TextColor3 = Color3.fromRGB(255, 0, 255)
RerollCountLabel.TextSize = 9
RerollCountLabel.Font = Enum.Font.Gotham
RerollCountLabel.TextXAlignment = Enum.TextXAlignment.Left
RerollCountLabel.Parent = StatsPanel

local PerformanceLabel = Instance.new("TextLabel")
PerformanceLabel.Size = UDim2.new(1, 0, 0, 17)
PerformanceLabel.Position = UDim2.new(0, 10, 0, 67)
PerformanceLabel.BackgroundTransparency = 1
PerformanceLabel.Text = "Performance: Optimal"
PerformanceLabel.TextColor3 = Color3.fromRGB(0, 255, 255)
PerformanceLabel.TextSize = 9
PerformanceLabel.Font = Enum.Font.Gotham
PerformanceLabel.TextXAlignment = Enum.TextXAlignment.Left
PerformanceLabel.Parent = StatsPanel

local CooldownLabel = Instance.new("TextLabel")
CooldownLabel.Size = UDim2.new(1, 0, 0, 17)
CooldownLabel.Position = UDim2.new(0, 10, 0, 87)
CooldownLabel.BackgroundTransparency = 1
CooldownLabel.Text = "Cooldown: 1.5s"
CooldownLabel.TextColor3 = Color3.fromRGB(180, 180, 180)
CooldownLabel.TextSize = 9
CooldownLabel.Font = Enum.Font.Gotham
CooldownLabel.TextXAlignment = Enum.TextXAlignment.Left
CooldownLabel.Parent = StatsPanel

local function GetObjectFromId(objectId)
    for eggModel in EggModels do
        if eggModel:GetAttribute("OBJECT_UUID") == objectId then
            return eggModel
        end
    end
    return nil
end

local function UpdateStatsDisplay()
    Stats.TotalEggs = 0
    Stats.RollingEggs = 0
    Stats.ReadyEggs = 0

    for objectId, _ in pairs(ActiveEggs) do
        Stats.TotalEggs = Stats.TotalEggs + 1
        if ReadyEggs[objectId] then
            Stats.ReadyEggs = Stats.ReadyEggs + 1
        else
            Stats.RollingEggs = Stats.RollingEggs + 1
        end
    end

    TotalEggsLabel.Text = "Total Eggs: " .. Stats.TotalEggs
    RollingEggsLabel.Text = "Rolling: " .. Stats.RollingEggs
    ReadyEggsLabel.Text = "Ready: " .. Stats.ReadyEggs
    RerollCountLabel.Text = "Rerolls: " .. Stats.RerollsPerformed

    StatusLabel.Text = "Manual Rerolling Active"
    DetailsLabel.Text = string.format("Found %d eggs - Click to reroll", Stats.TotalEggs)
end

local function StartRollAnimation(objectId, eggName)
    if RollAnimations[objectId] then return end

    RollAnimations[objectId] = true
    local dots = 0

    task.spawn(function()
        while RollAnimations[objectId] and EspCache[objectId] do
            dots = (dots % 3) + 1
            local dotString = string.rep(".", dots)
            if EspCache[objectId] and EspCache[objectId].petLabel then
                EspCache[objectId].petLabel.Text = "Rolling" .. dotString
                EspCache[objectId].statusIcon.Text = "●"

                local colors = {
                    Color3.fromRGB(255, 100, 100),
                    Color3.fromRGB(100, 255, 100),
                    Color3.fromRGB(100, 100, 255)
                }
                EspCache[objectId].statusIcon.TextColor3 = colors[dots]
            end
            task.wait(0.3)
        end
    end)
end

local function GetRandomPetFromEgg(eggName)
    if not EggData or not EggData[eggName] then return nil end

    local eggInfo = EggData[eggName]
    if not eggInfo.RarityData or not eggInfo.RarityData.Items then return nil end

    local pets = {}
    local totalWeight = 0

    for petName, petData in pairs(eggInfo.RarityData.Items) do
        local weight = petData.ItemOdd or 1
        table.insert(pets, {name = petName, weight = weight})
        totalWeight = totalWeight + weight
    end

    if #pets == 0 then return nil end

    local random = math.random() * totalWeight
    local currentWeight = 0

    for _, pet in ipairs(pets) do
        currentWeight = currentWeight + pet.weight
        if random <= currentWeight then
            return pet.name
        end
    end

    return pets[1].name
end

local function GetPetWeight(petName)
    if not PetRegistry or not petName then
        return math.random(80, 200) / 100
    end

    local petData = PetRegistry[petName]
    if not petData then
        return math.random(80, 200) / 100
    end

    local weight = petData.SellPrice and (petData.SellPrice / 1000000) or math.random(80, 200) / 100
    return weight
end

local function PerformReroll(objectId)
    local currentTime = tick()
    if LastRerollTime[objectId] and currentTime - LastRerollTime[objectId] < RerollCooldown then
        return false
    end

    local object = GetObjectFromId(objectId)
    if not object then return false end

    local eggName = object:GetAttribute("EggName")
    if not eggName then return false end

    if RollAnimations[objectId] then
        RollAnimations[objectId] = nil
    end

    ReadyEggs[objectId] = nil

    local randomPet = GetRandomPetFromEgg(eggName)
    if not randomPet then
        randomPet = "Unknown Pet"
    end

    local weight = GetPetWeight(randomPet)
    local espData = EspCache[objectId]

    if espData then
        espData.statusIcon.Text = "✓"
        espData.statusIcon.TextColor3 = Color3.fromRGB(0, 255, 0)
        espData.petLabel.Text = randomPet
        espData.weightLabel.Text = string.format("%.1f", weight)

        local color = Color3.fromRGB(255, 255, 255)
        local glowColor = Color3.fromRGB(70, 130, 180)
        local glowTransparency = 0.7

        if weight >= 10.0 then
            color = Color3.fromRGB(255, 0, 255)
            glowColor = Color3.fromRGB(255, 0, 255)
            glowTransparency = 0.2

            task.spawn(function()
                local rainbowColors = {
                    Color3.fromRGB(255, 0, 0),
                    Color3.fromRGB(255, 127, 0),
                    Color3.fromRGB(255, 255, 0),
                    Color3.fromRGB(0, 255, 0),
                    Color3.fromRGB(0, 0, 255),
                    Color3.fromRGB(75, 0, 130),
                    Color3.fromRGB(148, 0, 211)
                }
                while espData.glow do
                    for _, rainbowColor in ipairs(rainbowColors) do
                        if espData.glow then
                            espData.glow.BackgroundColor3 = rainbowColor
                            task.wait(0.2)
                        end
                    end
                end
            end)
        elseif weight >= 8.0 then
            color = Color3.fromRGB(255, 0, 255)
            glowColor = Color3.fromRGB(255, 0, 255)
            glowTransparency = 0.3
        elseif weight >= 6.0 then
            color = Color3.fromRGB(255, 215, 0)
            glowColor = Color3.fromRGB(255, 215, 0)
            glowTransparency = 0.4
        elseif weight >= 4.0 then
            color = Color3.fromRGB(255, 165, 0)
            glowColor = Color3.fromRGB(255, 165, 0)
            glowTransparency = 0.5
        elseif weight >= 2.0 then
            color = Color3.fromRGB(0, 255, 0)
            glowColor = Color3.fromRGB(0, 255, 0)
            glowTransparency = 0.6
        end

        espData.petLabel.TextColor3 = color
        espData.weightLabel.TextColor3 = color
        espData.glow.BackgroundColor3 = glowColor
        espData.glow.BackgroundTransparency = glowTransparency

        task.spawn(function()
            for i = 1, 10 do
                if espData.glow then
                    espData.glow.BackgroundTransparency = glowTransparency + (i * 0.05)
                    task.wait(0.1)
                end
            end
            if espData.glow then
                espData.glow.BackgroundTransparency = glowTransparency
            end
        end)
    end

    LastRerollTime[objectId] = currentTime
    Stats.RerollsPerformed = Stats.RerollsPerformed + 1
    ReadyEggs[objectId] = true

    task.spawn(function()
        task.wait(0.5)
        if RollAnimations[objectId] then
            RollAnimations[objectId] = nil
        end
    end)

    return true
end

local function AddEggESP(object)
    if object:GetAttribute("OWNER") ~= LocalPlayer.Name then return end

    local objectId = object:GetAttribute("OBJECT_UUID")
    if not objectId then return end

    local eggName = object:GetAttribute("EggName") or "Unknown Egg"
    local petName = EggPets[objectId]

    local billboard = Instance.new("BillboardGui")
    billboard.Size = UDim2.new(0, 160, 0, 80)
    billboard.StudsOffset = Vector3.new(0, 2, 0)
    billboard.Parent = object
    billboard.Adornee = object
    billboard.AlwaysOnTop = true

    local frame = Instance.new("Frame")
    frame.Size = UDim2.new(1, 0, 1, 0)
    frame.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    frame.BackgroundTransparency = 0
    frame.BorderSizePixel = 0
    frame.Parent = billboard

    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = frame

    local stroke = Instance.new("UIStroke")
    stroke.Color = Color3.fromRGB(0, 255, 255)
    stroke.Thickness = 2
    stroke.Transparency = 0
    stroke.Parent = frame

    local innerStroke = Instance.new("UIStroke")
    innerStroke.Color = Color3.fromRGB(255, 255, 255)
    innerStroke.Thickness = 1
    innerStroke.Transparency = 0.7
    innerStroke.Parent = frame

    local glow = Instance.new("Frame")
    glow.Size = UDim2.new(1, 6, 1, 6)
    glow.Position = UDim2.new(0, -3, 0, -3)
    glow.BackgroundColor3 = Color3.fromRGB(0, 255, 255)
    glow.BackgroundTransparency = 0.5
    glow.BorderSizePixel = 0
    glow.ZIndex = frame.ZIndex - 1
    glow.Parent = billboard

    local glowCorner = Instance.new("UICorner")
    glowCorner.CornerRadius = UDim.new(0, 11)
    glowCorner.Parent = glow

    local outerGlow = Instance.new("Frame")
    outerGlow.Size = UDim2.new(1, 12, 1, 12)
    outerGlow.Position = UDim2.new(0, -6, 0, -6)
    outerGlow.BackgroundColor3 = Color3.fromRGB(0, 255, 255)
    outerGlow.BackgroundTransparency = 0.7
    outerGlow.BorderSizePixel = 0
    outerGlow.ZIndex = frame.ZIndex - 2
    outerGlow.Parent = billboard

    local outerGlowCorner = Instance.new("UICorner")
    outerGlowCorner.CornerRadius = UDim.new(0, 14)
    outerGlowCorner.Parent = outerGlow

    local ultraGlow = Instance.new("Frame")
    ultraGlow.Size = UDim2.new(1, 18, 1, 18)
    ultraGlow.Position = UDim2.new(0, -9, 0, -9)
    ultraGlow.BackgroundColor3 = Color3.fromRGB(0, 255, 255)
    ultraGlow.BackgroundTransparency = 0.85
    ultraGlow.BorderSizePixel = 0
    ultraGlow.ZIndex = frame.ZIndex - 3
    ultraGlow.Parent = billboard

    local ultraGlowCorner = Instance.new("UICorner")
    ultraGlowCorner.CornerRadius = UDim.new(0, 17)
    ultraGlowCorner.Parent = ultraGlow

    local statusIcon = Instance.new("TextLabel")
    statusIcon.Size = UDim2.new(0, 18, 0, 18)
    statusIcon.Position = UDim2.new(0, 6, 0, 6)
    statusIcon.BackgroundTransparency = 1
    statusIcon.Text = "⚡"
    statusIcon.TextColor3 = Color3.fromRGB(0, 255, 255)
    statusIcon.TextSize = 14
    statusIcon.Font = Enum.Font.GothamBold
    statusIcon.Parent = frame

    local nameLabel = Instance.new("TextLabel")
    nameLabel.Size = UDim2.new(0, 130, 0, 18)
    nameLabel.Position = UDim2.new(0, 26, 0, 5)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = "🥚 " .. eggName
    nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    nameLabel.TextSize = 11
    nameLabel.Font = Enum.Font.GothamBold
    nameLabel.TextXAlignment = Enum.TextXAlignment.Left
    nameLabel.TextStrokeTransparency = 0
    nameLabel.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
    nameLabel.Parent = frame

    local petLabel = Instance.new("TextLabel")
    petLabel.Size = UDim2.new(0, 130, 0, 16)
    petLabel.Position = UDim2.new(0, 26, 0, 24)
    petLabel.BackgroundTransparency = 1
    petLabel.Text = "✨ Ready to Roll"
    petLabel.TextColor3 = Color3.fromRGB(0, 255, 255)
    petLabel.TextSize = 10
    petLabel.Font = Enum.Font.GothamSemibold
    petLabel.TextXAlignment = Enum.TextXAlignment.Left
    petLabel.TextStrokeTransparency = 0
    petLabel.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
    petLabel.Parent = frame

    local weightLabel = Instance.new("TextLabel")
    weightLabel.Size = UDim2.new(0, 130, 0, 14)
    weightLabel.Position = UDim2.new(0, 26, 0, 41)
    weightLabel.BackgroundTransparency = 1
    weightLabel.Text = ""
    weightLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    weightLabel.TextSize = 9
    weightLabel.Font = Enum.Font.GothamBold
    weightLabel.TextXAlignment = Enum.TextXAlignment.Left
    weightLabel.TextStrokeTransparency = 0
    weightLabel.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
    weightLabel.Parent = frame

    local cooldownLabel = Instance.new("TextLabel")
    cooldownLabel.Size = UDim2.new(0, 130, 0, 13)
    cooldownLabel.Position = UDim2.new(0, 26, 0, 56)
    cooldownLabel.BackgroundTransparency = 1
    cooldownLabel.Text = ""
    cooldownLabel.TextColor3 = Color3.fromRGB(255, 100, 100)
    cooldownLabel.TextSize = 8
    cooldownLabel.Font = Enum.Font.GothamBold
    cooldownLabel.TextXAlignment = Enum.TextXAlignment.Left
    cooldownLabel.TextStrokeTransparency = 0
    cooldownLabel.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
    cooldownLabel.Parent = frame

    task.spawn(function()
        while EspCache[objectId] and glow and outerGlow and ultraGlow do
            for i = 0.5, 0.8, 0.01 do
                if glow then
                    glow.BackgroundTransparency = i
                    stroke.Transparency = i * 0.3
                    innerStroke.Transparency = 0.7 + (i * 0.2)
                end
                if outerGlow then
                    outerGlow.BackgroundTransparency = i + 0.2
                end
                if ultraGlow then
                    ultraGlow.BackgroundTransparency = i + 0.35
                end
                task.wait(0.03)
            end
            for i = 0.8, 0.5, -0.01 do
                if glow then
                    glow.BackgroundTransparency = i
                    stroke.Transparency = i * 0.3
                    innerStroke.Transparency = 0.7 + (i * 0.2)
                end
                if outerGlow then
                    outerGlow.BackgroundTransparency = i + 0.2
                end
                if ultraGlow then
                    ultraGlow.BackgroundTransparency = i + 0.35
                end
                task.wait(0.03)
            end
        end
    end)

    task.spawn(function()
        while EspCache[objectId] and statusIcon do
            local icons = {
                {text = "⚡", color = Color3.fromRGB(0, 255, 255)},
                {text = "✨", color = Color3.fromRGB(255, 255, 0)},
                {text = "💎", color = Color3.fromRGB(255, 0, 255)},
                {text = "🔥", color = Color3.fromRGB(255, 100, 0)},
                {text = "⭐", color = Color3.fromRGB(255, 255, 255)},
                {text = "💫", color = Color3.fromRGB(0, 255, 0)}
            }
            for _, iconData in ipairs(icons) do
                if statusIcon then
                    statusIcon.Text = iconData.text
                    statusIcon.TextColor3 = iconData.color
                    task.wait(0.4)
                end
            end
        end
    end)

    EspCache[objectId] = {
        billboard = billboard,
        frame = frame,
        glow = glow,
        outerGlow = outerGlow,
        ultraGlow = ultraGlow,
        statusIcon = statusIcon,
        nameLabel = nameLabel,
        petLabel = petLabel,
        weightLabel = weightLabel,
        cooldownLabel = cooldownLabel
    }

    ActiveEggs[objectId] = object

    if petName then
        ReadyEggs[objectId] = true
        local weight = GetPetWeight(petName)

        statusIcon.Text = "✓"
        statusIcon.TextColor3 = Color3.fromRGB(100, 200, 100)
        petLabel.Text = petName
        weightLabel.Text = string.format("%.1f", weight)

        local color = Color3.fromRGB(255, 255, 255)
        if weight >= 6.0 then
            color = Color3.fromRGB(255, 215, 0)
            glow.BackgroundColor3 = Color3.fromRGB(255, 215, 0)
            glow.BackgroundTransparency = 0.5
        elseif weight >= 4.0 then
            color = Color3.fromRGB(255, 165, 0)
            glow.BackgroundColor3 = Color3.fromRGB(255, 165, 0)
            glow.BackgroundTransparency = 0.6
        elseif weight >= 2.0 then
            color = Color3.fromRGB(100, 255, 100)
            glow.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
            glow.BackgroundTransparency = 0.6
        else
            glow.BackgroundTransparency = 0.9
        end

        petLabel.TextColor3 = color
        weightLabel.TextColor3 = color
    else
        ReadyEggs[objectId] = nil
        StartRollAnimation(objectId, eggName)
    end

    UpdateStatsDisplay()
end

local function RemoveEggESP(object)
    if object:GetAttribute("OWNER") ~= LocalPlayer.Name then return end

    local objectId = object:GetAttribute("OBJECT_UUID")
    if not objectId then return end

    if EspCache[objectId] then
        if EspCache[objectId].billboard then
            EspCache[objectId].billboard:Destroy()
        end
        EspCache[objectId] = nil
    end

    ActiveEggs[objectId] = nil
    ReadyEggs[objectId] = nil
    LastRerollTime[objectId] = nil

    if RollAnimations[objectId] then
        RollAnimations[objectId] = nil
    end

    UpdateStatsDisplay()
end

local function UpdateEggESP(objectId, petName)
    local object = GetObjectFromId(objectId)
    if not object or not EspCache[objectId] then return end

    local weight = GetPetWeight(petName)
    local espData = EspCache[objectId]

    if RollAnimations[objectId] then
        RollAnimations[objectId] = nil
    end

    ReadyEggs[objectId] = true

    espData.statusIcon.Text = "✓"
    espData.statusIcon.TextColor3 = Color3.fromRGB(100, 200, 100)
    espData.petLabel.Text = petName
    espData.weightLabel.Text = string.format("%.1f", weight)

    local color = Color3.fromRGB(255, 255, 255)
    if weight >= 6.0 then
        color = Color3.fromRGB(255, 215, 0)
        espData.glow.BackgroundColor3 = Color3.fromRGB(255, 215, 0)
        espData.glow.BackgroundTransparency = 0.5
    elseif weight >= 4.0 then
        color = Color3.fromRGB(255, 165, 0)
        espData.glow.BackgroundColor3 = Color3.fromRGB(255, 165, 0)
        espData.glow.BackgroundTransparency = 0.6
    elseif weight >= 2.0 then
        color = Color3.fromRGB(100, 255, 100)
        espData.glow.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
        espData.glow.BackgroundTransparency = 0.6
    else
        espData.glow.BackgroundTransparency = 0.9
    end

    espData.petLabel.TextColor3 = color
    espData.weightLabel.TextColor3 = color

    UpdateStatsDisplay()
end

CloseButton.MouseButton1Click:Connect(function()
    if getgenv().EggReroller_Cleanup then
        getgenv().EggReroller_Cleanup()
    end
end)

RerollButton.MouseEnter:Connect(function()
    TweenService:Create(RerollButton, TweenInfo.new(0.2), {
        BackgroundColor3 = Color3.fromRGB(90, 150, 200)
    }):Play()
end)

RerollButton.MouseLeave:Connect(function()
    TweenService:Create(RerollButton, TweenInfo.new(0.2), {
        BackgroundColor3 = Color3.fromRGB(70, 130, 180)
    }):Play()
end)

RerollButton.MouseButton1Click:Connect(function()
    local foundEgg = false
    local rerolledCount = 0

    for objectId, object in pairs(ActiveEggs) do
        if object and object.Parent then
            local currentTime = tick()
            if not LastRerollTime[objectId] or currentTime - LastRerollTime[objectId] >= RerollCooldown then
                if RollAnimations[objectId] then
                    RollAnimations[objectId] = nil
                end

                task.spawn(function()
                    local success = PerformReroll(objectId)
                    if success then
                        rerolledCount = rerolledCount + 1
                    end
                end)

                foundEgg = true

                if rerolledCount >= 5 then
                    break
                end
            else
                local timeLeft = RerollCooldown - (currentTime - LastRerollTime[objectId])
                if EspCache[objectId] and EspCache[objectId].cooldownLabel then
                    EspCache[objectId].cooldownLabel.Text = string.format("Cooldown: %.1fs", timeLeft)
                end
            end
        end
    end

    if foundEgg then
        TweenService:Create(RerollButton, TweenInfo.new(0.1, Enum.EasingStyle.Quad), {
            Size = UDim2.new(0.85, 0, 0, 35)
        }):Play()
        task.wait(0.1)
        TweenService:Create(RerollButton, TweenInfo.new(0.1, Enum.EasingStyle.Quad), {
            Size = UDim2.new(0.9, 0, 0, 40)
        }):Play()

        StatusLabel.Text = "Rerolling eggs..."
        StatusLabel.TextColor3 = Color3.fromRGB(0, 255, 255)

        task.spawn(function()
            task.wait(1)
            StatusLabel.Text = "Manual Rerolling Active"
            StatusLabel.TextColor3 = Color3.fromRGB(100, 200, 100)
        end)
    else
        StatusLabel.Text = "No eggs to reroll"
        StatusLabel.TextColor3 = Color3.fromRGB(255, 150, 150)
        task.spawn(function()
            task.wait(2)
            StatusLabel.Text = "Manual Rerolling Active"
            StatusLabel.TextColor3 = Color3.fromRGB(100, 200, 100)
        end)
    end
end)

for _, object in pairs(CollectionService:GetTagged("PetEggServer")) do
    task.spawn(AddEggESP, object)
end

Connections[#Connections + 1] = CollectionService:GetInstanceAddedSignal("PetEggServer"):Connect(AddEggESP)
Connections[#Connections + 1] = CollectionService:GetInstanceRemovedSignal("PetEggServer"):Connect(RemoveEggESP)

local old; old = hookfunction(getconnections(ReplicatedStorage.GameEvents.EggReadyToHatch_RE.OnClientEvent)[1].Function, newcclosure(function(objectId, petName)
    UpdateEggESP(objectId, petName)
    return old(objectId, petName)
end))

Connections[#Connections + 1] = RunService.Heartbeat:Connect(function()
    UpdateStatsDisplay()
end)

local dragging = false
local dragStart = nil
local startPos = nil

HeaderFrame.InputBegan:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseButton1 then
        dragging = true
        dragStart = input.Position
        startPos = MainFrame.Position
    end
end)

HeaderFrame.InputChanged:Connect(function(input)
    if dragging and input.UserInputType == Enum.UserInputType.MouseMovement then
        local delta = input.Position - dragStart
        MainFrame.Position = UDim2.new(startPos.X.Scale, startPos.X.Offset + delta.X, startPos.Y.Scale, startPos.Y.Offset + delta.Y)
    end
end)

HeaderFrame.InputEnded:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseButton1 then
        dragging = false
    end
end)

getgenv().EggReroller_Cleanup = function()
    for _, connection in pairs(Connections) do
        if connection then
            connection:Disconnect()
        end
    end

    for _, espData in pairs(EspCache) do
        if espData and espData.billboard then
            espData.billboard:Destroy()
        end
    end

    if MainGui then
        MainGui:Destroy()
    end

    EspCache = {}
    ActiveEggs = {}
    ReadyEggs = {}
    Connections = {}
    RollAnimations = {}
    LastRerollTime = {}
    IsMinimized = false
    Stats = {
        TotalEggs = 0,
        RollingEggs = 0,
        ReadyEggs = 0,
        RerollsPerformed = 0
    }
end

UpdateStatsDisplay()
