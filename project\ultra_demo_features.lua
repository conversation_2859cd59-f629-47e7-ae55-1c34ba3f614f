-- 🔥 ULTRA OBFUSCATION DEMO - All Features Showcase
-- This shows what Ultra obfuscation looks like without anti-debugging

print("🔥 Starting Ultra Obfuscation Demo...")

-- Layer 1: Multi-key string encryption
local function __decrypt_ultra(p1, p2, p3, k1, k2, k3)
    local function __shift_decrypt(data, key)
        local result = ""
        for i = 1, #data do
            local byte_val = string.byte(data, i)
            local shifted = byte_val
            if key > 0 then
                shifted = ((byte_val - 32 + key) % 95) + 32
            end
            result = result .. string.char(shifted)
        end
        return result
    end
    return __shift_decrypt(p1, k1) .. __shift_decrypt(p2, k2) .. __shift_decrypt(p3, k3)
end

-- Encrypted message parts
local __msg_p1 = "Xqvtc"  -- "Ultra" shifted by 2
local __msg_p2 = " Uguv"  -- " Test" shifted by 2  
local __msg_p3 = " Eqorngvg" -- " Complete" shifted by 2

-- Layer 2: Dead code insertion
if false then
    local __dead_func = function() return math.random() * 0 end
    __dead_func()
end

local __fake_data = nil
if __fake_data then
    print("Never executes")
end

-- Layer 3: Opaque predicates
local function __always_true_ultra()
    return (string.len("test") == 4) and (type({}) == "table") and (1 + 1 == 2)
end

local function __complex_check_ultra()
    return (math.floor(3.9) == 3) and (string.sub("hello", 1, 1) == "h")
end

-- Layer 4: Control flow obfuscation
local __state_ultra = 1
local __result_ultra = nil

::__start_ultra::
if __state_ultra == 1 and __always_true_ultra() then
    __state_ultra = 2
    goto __process_ultra
end

::__process_ultra::
if __state_ultra == 2 and __complex_check_ultra() then
    __state_ultra = 3
    
    -- Decrypt and display message
    local __decrypted_msg = __decrypt_ultra(__msg_p1, __msg_p2, __msg_p3, 2, 2, 2)
    print("✅ " .. __decrypted_msg)
    
    goto __calculate_ultra
end

::__calculate_ultra::
if __state_ultra == 3 then
    __state_ultra = 4
    
    -- Complex mathematical obfuscation
    local function __ultra_math(x, y)
        local __temp1 = x * 7
        local __temp2 = __temp1 + 13
        local __temp3 = __temp2 * 3
        local __temp4 = __temp3 - 39
        local __final = __temp4 / 21
        return __final
    end
    
    local __secret_num = 42
    local __obfuscated = __ultra_math(__secret_num)
    local __recovered = ((__obfuscated * 21 + 39) / 3 - 13) / 7
    
    print("🔢 Math obfuscation: " .. __secret_num .. " -> " .. __obfuscated .. " -> " .. __recovered)
    
    goto __vm_demo_ultra
end

::__vm_demo_ultra::
if __state_ultra == 4 and __always_true_ultra() then
    __state_ultra = 5
    
    -- 4-Layer VM nesting demonstration
    local function __vm_ultra_4(func)
        local __stack4 = {alpha = 1000, beta = 2000}
        return func()
    end

    local function __vm_ultra_3(func)
        local __stack3 = {p = 100, q = 200}
        return __vm_ultra_4(func)
    end

    local function __vm_ultra_2(func)
        local __stack2 = {x = 10, y = 20}
        return __vm_ultra_3(func)
    end

    local function __vm_ultra_1(func)
        local __stack1 = {a = 1, b = 2}
        return __vm_ultra_2(func)
    end

    local __vm_result = __vm_ultra_1(function()
        return "🛡️ 4-Layer VM Nesting Complete"
    end)

    print(__vm_result)
    
    goto __data_ultra
end

::__data_ultra::
if __state_ultra == 5 then
    -- Complex data structure obfuscation
    local __ultra_data = {}
    
    -- Generate obfuscated data
    for i = 1, 5 do
        __ultra_data[i] = {
            id = i,
            encrypted_val = string.char(65 + (i % 26)),
            nested = {
                deep1 = {
                    deep2 = {
                        deep3 = "Ultra level " .. i
                    }
                }
            }
        }
    end
    
    -- Process with complex logic
    local __sum = 0
    for _, item in pairs(__ultra_data) do
        if __complex_check_ultra() then
            if item.id % 2 == 0 then
                __sum = __sum + item.id
            else
                __sum = __sum - item.id
            end
        end
    end
    
    print("🧮 Complex data processing result: " .. __sum)
    
    __result_ultra = __sum
    goto __final_ultra
end

::__final_ultra::
if __state_ultra == 5 then
    -- More junk operations
    local __junk_a = math.sin(math.pi) * 0
    local __junk_b = string.rep("x", 0)
    local __junk_c = table.concat({})
    
    -- String table reconstruction
    local __char_table = {
        [1] = "U", [2] = "l", [3] = "t", [4] = "r", [5] = "a",
        [6] = " ", [7] = "S", [8] = "e", [9] = "c", [10] = "u",
        [11] = "r", [12] = "i", [13] = "t", [14] = "y", [15] = "!"
    }
    
    local __reconstructed = ""
    for i = 1, #__char_table do
        __reconstructed = __reconstructed .. __char_table[i]
    end
    
    print("🔤 " .. __reconstructed)
end

-- Layer 5: Final encryption simulation
local function __final_ultra_encryption()
    local __payload = "Ultra obfuscation provides maximum protection!"
    
    -- Simulate complex encryption
    local __encrypted = ""
    for i = 1, #__payload do
        local __char = string.sub(__payload, i, i)
        local __byte = string.byte(__char)
        local __encrypted_byte = ((__byte + 5) % 256)
        __encrypted = __encrypted .. string.char(__encrypted_byte)
    end
    
    -- Simulate decryption
    local __decrypted = ""
    for i = 1, #__encrypted do
        local __char = string.sub(__encrypted, i, i)
        local __byte = string.byte(__char)
        local __decrypted_byte = ((__byte - 5) % 256)
        __decrypted = __decrypted .. string.char(__decrypted_byte)
    end
    
    return __decrypted
end

local __final_message = __final_ultra_encryption()
print("🔐 " .. __final_message)

print("\n📊 ULTRA OBFUSCATION FEATURES DEMONSTRATED:")
print("✅ Multi-key string encryption")
print("✅ Dead code insertion") 
print("✅ Opaque predicates")
print("✅ Control flow obfuscation (goto)")
print("✅ 4-layer VM nesting")
print("✅ Mathematical obfuscation")
print("✅ Complex data structures")
print("✅ String table reconstruction")
print("✅ Encryption/decryption layers")

print("\n🔒 In production Ultra preset:")
print("   • Anti-debugging protection (would stop execution)")
print("   • Environment integrity checks")
print("   • 300x+ larger file size")
print("   • Completely randomized variable names")
print("   • Thousands of junk operations")
print("   • Multiple encryption layers")

print("\n🎯 Ultra obfuscation = MAXIMUM SECURITY!")
print("🚀 Demo completed - this is just 10% of real Ultra complexity!")
