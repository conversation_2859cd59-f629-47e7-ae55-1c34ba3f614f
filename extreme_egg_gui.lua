if getgenv().EggReroller_Cleanup then
    getgenv().EggReroller_Cleanup()
end

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local CollectionService = game:GetService("CollectionService")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local CoreGui = game:GetService("CoreGui")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

local LocalPlayer = Players.LocalPlayer
local CurrentCamera = workspace.CurrentCamera

local HatchFunction = getupvalue(getupvalue(getconnections(ReplicatedStorage.GameEvents.PetEggService.OnClientEvent)[1].Function, 1), 2)
local EggModels = getupvalue(HatchFunction, 1)
local EggPets = getupvalue(HatchFunction, 2)

local PetRegistry = nil
pcall(function()
    PetRegistry = require(ReplicatedStorage.Data.PetRegistry.PetList)
end)

local EggData = nil
pcall(function()
    EggData = require(ReplicatedStorage.Data.PetRegistry.PetEggs)
end)

local EspCache = {}
local ActiveEggs = {}
local ReadyEggs = {}
local Connections = {}
local RerollCooldown = 1.5
local LastRerollTime = {}
local RollAnimations = {}
local SelectedEgg = nil
local IsMinimized = false

local Stats = {
    TotalEggs = 0,
    RollingEggs = 0,
    ReadyEggs = 0,
    RerollsPerformed = 0
}

local EspCache = {}
local ActiveEggs = {}
local ReadyEggs = {}
local Connections = {}
local RerollCooldown = 1.5
local LastRerollTime = {}
local RollAnimations = {}
local SelectedEgg = nil

local Stats = {
    TotalEggs = 0,
    RollingEggs = 0,
    ReadyEggs = 0,
    RerollsPerformed = 0
}

local MainGui = Instance.new("ScreenGui")
MainGui.Name = "KawaiiEggReroller"
MainGui.Parent = CoreGui
MainGui.ResetOnSpawn = false

local MainFrame = Instance.new("Frame")
MainFrame.Size = UDim2.new(0, 320, 0, 380)
MainFrame.Position = UDim2.new(0.5, -160, 0.5, -190)
MainFrame.BackgroundColor3 = Color3.fromRGB(255, 250, 255)
MainFrame.BorderSizePixel = 0
MainFrame.Parent = MainGui

local MinimizedFrame = Instance.new("Frame")
MinimizedFrame.Size = UDim2.new(0, 60, 0, 60)
MinimizedFrame.Position = UDim2.new(1, -70, 0.5, -30)
MinimizedFrame.BackgroundColor3 = Color3.fromRGB(255, 182, 193)
MinimizedFrame.BorderSizePixel = 0
MinimizedFrame.Visible = false
MinimizedFrame.Parent = MainGui

local MainCorner = Instance.new("UICorner")
MainCorner.CornerRadius = UDim.new(0, 25)
MainCorner.Parent = MainFrame

local MainShadow = Instance.new("Frame")
MainShadow.Size = UDim2.new(1, 10, 1, 10)
MainShadow.Position = UDim2.new(0, -5, 0, -5)
MainShadow.BackgroundColor3 = Color3.fromRGB(255, 182, 193)
MainShadow.BackgroundTransparency = 0.7
MainShadow.ZIndex = MainFrame.ZIndex - 1
MainShadow.Parent = MainFrame

local ShadowCorner = Instance.new("UICorner")
ShadowCorner.CornerRadius = UDim.new(0, 30)
ShadowCorner.Parent = MainShadow

local MinimizedCorner = Instance.new("UICorner")
MinimizedCorner.CornerRadius = UDim.new(0, 30)
MinimizedCorner.Parent = MinimizedFrame

local MinimizedIcon = Instance.new("TextLabel")
MinimizedIcon.Size = UDim2.new(1, 0, 1, 0)
MinimizedIcon.BackgroundTransparency = 1
MinimizedIcon.Text = "🥚"
MinimizedIcon.TextColor3 = Color3.fromRGB(255, 255, 255)
MinimizedIcon.TextSize = 30
MinimizedIcon.Font = Enum.Font.SourceSansBold
MinimizedIcon.Parent = MinimizedFrame

local HeaderFrame = Instance.new("Frame")
HeaderFrame.Size = UDim2.new(1, 0, 0, 70)
HeaderFrame.Position = UDim2.new(0, 0, 0, 0)
HeaderFrame.BackgroundColor3 = Color3.fromRGB(255, 182, 193)
HeaderFrame.BorderSizePixel = 0
HeaderFrame.Parent = MainFrame

local HeaderCorner = Instance.new("UICorner")
HeaderCorner.CornerRadius = UDim.new(0, 25)
HeaderCorner.Parent = HeaderFrame

local HeaderGradient = Instance.new("UIGradient")
HeaderGradient.Color = ColorSequence.new{
    ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 182, 193)),
    ColorSequenceKeypoint.new(1, Color3.fromRGB(255, 192, 203))
}
HeaderGradient.Rotation = 90
HeaderGradient.Parent = HeaderFrame

local TitleLabel = Instance.new("TextLabel")
TitleLabel.Size = UDim2.new(0.7, 0, 0.6, 0)
TitleLabel.Position = UDim2.new(0, 15, 0, 5)
TitleLabel.BackgroundTransparency = 1
TitleLabel.Text = "✨ Egg Reroller ✨"
TitleLabel.TextColor3 = Color3.fromRGB(80, 45, 60)
TitleLabel.TextSize = 28
TitleLabel.Font = Enum.Font.GothamBold
TitleLabel.TextXAlignment = Enum.TextXAlignment.Left
TitleLabel.Parent = HeaderFrame

local SubtitleLabel = Instance.new("TextLabel")
SubtitleLabel.Size = UDim2.new(0.7, 0, 0.4, 0)
SubtitleLabel.Position = UDim2.new(0, 15, 0.6, 0)
SubtitleLabel.BackgroundTransparency = 1
SubtitleLabel.Text = "� Kawaii Rerolling System"
SubtitleLabel.TextColor3 = Color3.fromRGB(120, 80, 100)
SubtitleLabel.TextSize = 18
SubtitleLabel.Font = Enum.Font.Gotham
SubtitleLabel.TextXAlignment = Enum.TextXAlignment.Left
SubtitleLabel.Parent = HeaderFrame

local MinimizeButton = Instance.new("TextButton")
MinimizeButton.Size = UDim2.new(0, 40, 0, 40)
MinimizeButton.Position = UDim2.new(1, -100, 0, 15)
MinimizeButton.BackgroundColor3 = Color3.fromRGB(255, 215, 215)
MinimizeButton.BorderSizePixel = 0
MinimizeButton.Text = "—"
MinimizeButton.TextColor3 = Color3.fromRGB(80, 45, 60)
MinimizeButton.TextSize = 24
MinimizeButton.Font = Enum.Font.GothamBold
MinimizeButton.Parent = HeaderFrame

local CloseButton = Instance.new("TextButton")
CloseButton.Size = UDim2.new(0, 40, 0, 40)
CloseButton.Position = UDim2.new(1, -50, 0, 15)
CloseButton.BackgroundColor3 = Color3.fromRGB(255, 200, 200)
CloseButton.BorderSizePixel = 0
CloseButton.Text = "✕"
CloseButton.TextColor3 = Color3.fromRGB(80, 45, 60)
CloseButton.TextSize = 24
CloseButton.Font = Enum.Font.GothamBold
CloseButton.Parent = HeaderFrame

local CloseCorner = Instance.new("UICorner")
CloseCorner.CornerRadius = UDim.new(0, 20)
CloseCorner.Parent = CloseButton

local MinimizeCorner = Instance.new("UICorner")
MinimizeCorner.CornerRadius = UDim.new(0, 20)
MinimizeCorner.Parent = MinimizeButton

CloseButton.MouseEnter:Connect(function()
    TweenService:Create(CloseButton, TweenInfo.new(0.2), {
        BackgroundColor3 = Color3.fromRGB(255, 150, 150),
        Size = UDim2.new(0, 45, 0, 45)
    }):Play()
end)

CloseButton.MouseLeave:Connect(function()
    TweenService:Create(CloseButton, TweenInfo.new(0.2), {
        BackgroundColor3 = Color3.fromRGB(255, 200, 200),
        Size = UDim2.new(0, 40, 0, 40)
    }):Play()
end)

MinimizeButton.MouseEnter:Connect(function()
    TweenService:Create(MinimizeButton, TweenInfo.new(0.2), {
        BackgroundColor3 = Color3.fromRGB(255, 230, 230),
        Size = UDim2.new(0, 45, 0, 45)
    }):Play()
end)

MinimizeButton.MouseLeave:Connect(function()
    TweenService:Create(MinimizeButton, TweenInfo.new(0.2), {
        BackgroundColor3 = Color3.fromRGB(255, 215, 215),
        Size = UDim2.new(0, 40, 0, 40)
    }):Play()
end)

local function ToggleMinimize()
    IsMinimized = not IsMinimized
    if IsMinimized then
        TweenService:Create(MainFrame, TweenInfo.new(0.3, Enum.EasingStyle.Back), {
            Position = UDim2.new(1, -70, 0.5, -30),
            Size = UDim2.new(0, 0, 0, 0)
        }):Play()
        MinimizedFrame.Visible = true
        TweenService:Create(MinimizedFrame, TweenInfo.new(0.3, Enum.EasingStyle.Back), {
            Size = UDim2.new(0, 60, 0, 60)
        }):Play()
    else
        TweenService:Create(MinimizedFrame, TweenInfo.new(0.3, Enum.EasingStyle.Back), {
            Size = UDim2.new(0, 0, 0, 0)
        }):Play()
        task.wait(0.3)
        MinimizedFrame.Visible = false
        TweenService:Create(MainFrame, TweenInfo.new(0.3, Enum.EasingStyle.Back), {
            Position = UDim2.new(0.5, -160, 0.5, -190),
            Size = UDim2.new(0, 320, 0, 380)
        }):Play()
    end
end

MinimizeButton.MouseButton1Click:Connect(ToggleMinimize)
MinimizedFrame.InputBegan:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseButton1 then
        ToggleMinimize()
    end
end)

local ControlPanel = Instance.new("Frame")
ControlPanel.Size = UDim2.new(0.9, 0, 0, 70)
ControlPanel.Position = UDim2.new(0.05, 0, 0, 80)
ControlPanel.BackgroundColor3 = Color3.fromRGB(255, 240, 245)
ControlPanel.BorderSizePixel = 0
ControlPanel.Parent = MainFrame

local ControlCorner = Instance.new("UICorner")
ControlCorner.CornerRadius = UDim.new(0, 20)
ControlCorner.Parent = ControlPanel

local ControlGradient = Instance.new("UIGradient")
ControlGradient.Color = ColorSequence.new{
    ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 240, 245)),
    ColorSequenceKeypoint.new(1, Color3.fromRGB(255, 230, 240))
}
ControlGradient.Rotation = 90
ControlGradient.Parent = ControlPanel

local RerollButton = Instance.new("TextButton")
RerollButton.Size = UDim2.new(0.9, 0, 0, 50)
RerollButton.Position = UDim2.new(0.05, 0, 0, 10)
RerollButton.BackgroundColor3 = Color3.fromRGB(255, 182, 193)
RerollButton.BorderSizePixel = 0
RerollButton.Text = "� Reroll Egg"
RerollButton.TextColor3 = Color3.fromRGB(80, 45, 60)
RerollButton.TextSize = 24
RerollButton.Font = Enum.Font.GothamBold
RerollButton.Parent = ControlPanel

local RerollCorner = Instance.new("UICorner")
RerollCorner.CornerRadius = UDim.new(0, 20)
RerollCorner.Parent = RerollButton

local RerollGradient = Instance.new("UIGradient")
RerollGradient.Color = ColorSequence.new{
    ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 182, 193)),
    ColorSequenceKeypoint.new(1, Color3.fromRGB(255, 192, 203))
}
RerollGradient.Rotation = 45
RerollGradient.Parent = RerollButton

local StatusPanel = Instance.new("Frame")
StatusPanel.Size = UDim2.new(0.9, 0, 0, 60)
StatusPanel.Position = UDim2.new(0.05, 0, 0, 160)
StatusPanel.BackgroundColor3 = Color3.fromRGB(240, 255, 240)
StatusPanel.BorderSizePixel = 0
StatusPanel.Parent = MainFrame

local StatusCorner = Instance.new("UICorner")
StatusCorner.CornerRadius = UDim.new(0, 20)
StatusCorner.Parent = StatusPanel

local StatusGradient = Instance.new("UIGradient")
StatusGradient.Color = ColorSequence.new{
    ColorSequenceKeypoint.new(0, Color3.fromRGB(240, 255, 240)),
    ColorSequenceKeypoint.new(1, Color3.fromRGB(230, 250, 230))
}
StatusGradient.Rotation = 90
StatusGradient.Parent = StatusPanel

local StatusLabel = Instance.new("TextLabel")
StatusLabel.Size = UDim2.new(1, 0, 0.6, 0)
StatusLabel.Position = UDim2.new(0, 0, 0, 0)
StatusLabel.BackgroundTransparency = 1
StatusLabel.Text = "🌸 Scanning for eggs..."
StatusLabel.TextColor3 = Color3.fromRGB(80, 120, 80)
StatusLabel.TextSize = 20
StatusLabel.Font = Enum.Font.GothamBold
StatusLabel.Parent = StatusPanel

local DetailsLabel = Instance.new("TextLabel")
DetailsLabel.Size = UDim2.new(1, 0, 0.4, 0)
DetailsLabel.Position = UDim2.new(0, 0, 0.6, 0)
DetailsLabel.BackgroundTransparency = 1
DetailsLabel.Text = "Ready to reroll manually ♡"
DetailsLabel.TextColor3 = Color3.fromRGB(120, 150, 120)
DetailsLabel.TextSize = 16
DetailsLabel.Font = Enum.Font.Gotham
DetailsLabel.Parent = StatusPanel

local StatsPanel = Instance.new("Frame")
StatsPanel.Size = UDim2.new(0.9, 0, 0, 110)
StatsPanel.Position = UDim2.new(0.05, 0, 0, 230)
StatsPanel.BackgroundColor3 = Color3.fromRGB(245, 240, 255)
StatsPanel.BorderSizePixel = 0
StatsPanel.Parent = MainFrame

local StatsCorner = Instance.new("UICorner")
StatsCorner.CornerRadius = UDim.new(0, 20)
StatsCorner.Parent = StatsPanel

local StatsGradient = Instance.new("UIGradient")
StatsGradient.Color = ColorSequence.new{
    ColorSequenceKeypoint.new(0, Color3.fromRGB(245, 240, 255)),
    ColorSequenceKeypoint.new(1, Color3.fromRGB(235, 230, 250))
}
StatsGradient.Rotation = 90
StatsGradient.Parent = StatsPanel

local StatsTitle = Instance.new("TextLabel")
StatsTitle.Size = UDim2.new(1, 0, 0, 25)
StatsTitle.Position = UDim2.new(0, 0, 0, 5)
StatsTitle.BackgroundTransparency = 1
StatsTitle.Text = "� Statistics"
StatsTitle.TextColor3 = Color3.fromRGB(120, 80, 140)
StatsTitle.TextSize = 20
StatsTitle.Font = Enum.Font.GothamBold
StatsTitle.Parent = StatsPanel

local TotalEggsLabel = Instance.new("TextLabel")
TotalEggsLabel.Size = UDim2.new(0.5, 0, 0, 20)
TotalEggsLabel.Position = UDim2.new(0, 10, 0, 30)
TotalEggsLabel.BackgroundTransparency = 1
TotalEggsLabel.Text = "🥚 Total: 0"
TotalEggsLabel.TextColor3 = Color3.fromRGB(100, 80, 120)
TotalEggsLabel.TextSize = 16
TotalEggsLabel.Font = Enum.Font.Gotham
TotalEggsLabel.TextXAlignment = Enum.TextXAlignment.Left
TotalEggsLabel.Parent = StatsPanel

local RollingEggsLabel = Instance.new("TextLabel")
RollingEggsLabel.Size = UDim2.new(0.5, 0, 0, 20)
RollingEggsLabel.Position = UDim2.new(0.5, 0, 0, 30)
RollingEggsLabel.BackgroundTransparency = 1
RollingEggsLabel.Text = "🎲 Rolling: 0"
RollingEggsLabel.TextColor3 = Color3.fromRGB(140, 100, 80)
RollingEggsLabel.TextSize = 16
RollingEggsLabel.Font = Enum.Font.Gotham
RollingEggsLabel.TextXAlignment = Enum.TextXAlignment.Left
RollingEggsLabel.Parent = StatsPanel

local ReadyEggsLabel = Instance.new("TextLabel")
ReadyEggsLabel.Size = UDim2.new(0.5, 0, 0, 20)
ReadyEggsLabel.Position = UDim2.new(0, 10, 0, 50)
ReadyEggsLabel.BackgroundTransparency = 1
ReadyEggsLabel.Text = "✨ Ready: 0"
ReadyEggsLabel.TextColor3 = Color3.fromRGB(80, 140, 100)
ReadyEggsLabel.TextSize = 16
ReadyEggsLabel.Font = Enum.Font.Gotham
ReadyEggsLabel.TextXAlignment = Enum.TextXAlignment.Left
ReadyEggsLabel.Parent = StatsPanel

local RerollCountLabel = Instance.new("TextLabel")
RerollCountLabel.Size = UDim2.new(0.5, 0, 0, 20)
RerollCountLabel.Position = UDim2.new(0.5, 0, 0, 50)
RerollCountLabel.BackgroundTransparency = 1
RerollCountLabel.Text = "🎀 Rerolls: 0"
RerollCountLabel.TextColor3 = Color3.fromRGB(160, 100, 140)
RerollCountLabel.TextSize = 16
RerollCountLabel.Font = Enum.Font.Gotham
RerollCountLabel.TextXAlignment = Enum.TextXAlignment.Left
RerollCountLabel.Parent = StatsPanel

local PerformanceLabel = Instance.new("TextLabel")
PerformanceLabel.Size = UDim2.new(1, 0, 0, 20)
PerformanceLabel.Position = UDim2.new(0, 10, 0, 70)
PerformanceLabel.BackgroundTransparency = 1
PerformanceLabel.Text = "💫 Performance: Kawaii"
PerformanceLabel.TextColor3 = Color3.fromRGB(120, 120, 160)
PerformanceLabel.TextSize = 16
PerformanceLabel.Font = Enum.Font.Gotham
PerformanceLabel.TextXAlignment = Enum.TextXAlignment.Left
PerformanceLabel.Parent = StatsPanel

local function GetObjectFromId(objectId)
    for eggModel in EggModels do
        if eggModel:GetAttribute("OBJECT_UUID") == objectId then
            return eggModel
        end
    end
    return nil
end

local function UpdateStatsDisplay()
    Stats.TotalEggs = 0
    Stats.RollingEggs = 0
    Stats.ReadyEggs = 0

    for objectId, _ in pairs(ActiveEggs) do
        Stats.TotalEggs = Stats.TotalEggs + 1
        if ReadyEggs[objectId] then
            Stats.ReadyEggs = Stats.ReadyEggs + 1
        else
            Stats.RollingEggs = Stats.RollingEggs + 1
        end
    end

    TotalEggsLabel.Text = "🥚 Total: " .. Stats.TotalEggs
    RollingEggsLabel.Text = "🎲 Rolling: " .. Stats.RollingEggs
    ReadyEggsLabel.Text = "✨ Ready: " .. Stats.ReadyEggs
    RerollCountLabel.Text = "🎀 Rerolls: " .. Stats.RerollsPerformed

    StatusLabel.Text = "� Manual Rerolling"
    DetailsLabel.Text = string.format("Found %d eggs - Click to reroll ♡", Stats.TotalEggs)
end

local function StartRollAnimation(objectId, eggName)
    if RollAnimations[objectId] then return end

    RollAnimations[objectId] = true
    local dots = 0

    task.spawn(function()
        while RollAnimations[objectId] and EspCache[objectId] do
            dots = (dots % 3) + 1
            local dotString = string.rep(".", dots)
            if EspCache[objectId] and EspCache[objectId].petLabel then
                EspCache[objectId].petLabel.Text = "🎲 Rolling" .. dotString
            end
            task.wait(0.5)
        end
    end)
end


local function getRandomPetFromEgg(eggName)
    if not eggData or not eggData[eggName] then return nil end

    local eggInfo = eggData[eggName]
    if not eggInfo.RarityData or not eggInfo.RarityData.Items then return nil end

    local pets = {}
    local totalWeight = 0

    for petName, petData in pairs(eggInfo.RarityData.Items) do
        local weight = petData.ItemOdd or 1
        table.insert(pets, {name = petName, weight = weight})
        totalWeight = totalWeight + weight
    end

    if #pets == 0 then return nil end

    local random = math.random() * totalWeight
    local currentWeight = 0

    for _, pet in ipairs(pets) do
        currentWeight = currentWeight + pet.weight
        if random <= currentWeight then
            return pet.name
        end
    end

    return pets[1].name
end

local function getPetWeight(petName)
    if not petRegistry or not petName then
        return math.random(80, 200) / 100
    end

    local petData = petRegistry[petName]
    if not petData then
        return math.random(80, 200) / 100
    end

    local weight = petData.SellPrice and (petData.SellPrice / 1000000) or math.random(80, 200) / 100
    return weight
end

local function performReroll(objectId)
    local currentTime = tick()
    if lastRerollTime[objectId] and currentTime - lastRerollTime[objectId] < rerollCooldown then
        return false
    end

    local object = getObjectFromId(objectId)
    if not object then return false end

    local eggName = object:GetAttribute("EggName")
    local randomPet = getRandomPetFromEgg(eggName)

    if randomPet then
        local weight = getPetWeight(randomPet)
        local label = espCache[objectId]

        if label then
            label.Text = string.format("✅ %s\n🐾 %s | ⚖️ %.1f KG", eggName, randomPet, weight)

            -- Color based on weight
            if weight >= 6.0 then
                label.Color = Color3.fromRGB(255, 215, 0) -- Gold
            elseif weight >= 4.0 then
                label.Color = Color3.fromRGB(255, 165, 0) -- Orange
            elseif weight >= 2.0 then
                label.Color = Color3.fromRGB(100, 255, 100) -- Green
            else
                label.Color = Color3.fromRGB(255, 255, 255) -- White
            end
        end

        lastRerollTime[objectId] = currentTime
        stats.rerollsPerformed = stats.rerollsPerformed + 1
        return true
    end

    return false
end

-- ═══════════════════════════════════════════════════════════════
--                      EVENT HANDLERS
-- ═══════════════════════════════════════════════════════════════

closeButton.MouseButton1Click:Connect(function()
    if getgenv().EggReroller_Cleanup then
        getgenv().EggReroller_Cleanup()
    end
end)

-- Manual reroll functionality
local selectedEgg = nil

rerollButton.MouseButton1Click:Connect(function()
    -- Find any rolling egg to reroll
    local foundEgg = false
    for objectId, object in pairs(activeEggs) do
        if not readyEggs[objectId] then -- Only reroll eggs that are still rolling
            local currentTime = tick()
            if not lastRerollTime[objectId] or currentTime - lastRerollTime[objectId] >= rerollCooldown then
                performReroll(objectId)
                foundEgg = true

                -- Button animation
                local buttonTween = tweenService:Create(rerollButton, TweenInfo.new(0.1, Enum.EasingStyle.Quad), {
                    Size = UDim2.new(0.85, 0, 0, 35)
                })
                buttonTween:Play()
                buttonTween.Completed:Connect(function()
                    tweenService:Create(rerollButton, TweenInfo.new(0.1, Enum.EasingStyle.Quad), {
                        Size = UDim2.new(0.9, 0, 0, 40)
                    }):Play()
                end)

                statusLabel.Text = "🎲 REROLLED!"
                statusLabel.TextColor3 = Color3.fromRGB(100, 255, 100)
                break -- Only reroll one egg per click
            end
        end
    end

    if not foundEgg then
        statusLabel.Text = "❌ NO EGGS TO REROLL"
        statusLabel.TextColor3 = Color3.fromRGB(255, 100, 100)
        task.spawn(function()
            task.wait(2)
            statusLabel.Text = "🔍 SCANNING..."
            statusLabel.TextColor3 = Color3.fromRGB(100, 255, 100)
        end)
    end
end)

local function getRandomPetFromEgg(eggName)
    if not eggData or not eggData[eggName] then return nil end

    local eggInfo = eggData[eggName]
    if not eggInfo.RarityData or not eggInfo.RarityData.Items then return nil end

    local pets = {}
    local totalWeight = 0

    for petName, petData in pairs(eggInfo.RarityData.Items) do
        local weight = petData.ItemOdd or 1
        table.insert(pets, {name = petName, weight = weight})
        totalWeight = totalWeight + weight
    end

    if #pets == 0 then return nil end

    local random = math.random() * totalWeight
    local currentWeight = 0

    for _, pet in ipairs(pets) do
        currentWeight = currentWeight + pet.weight
        if random <= currentWeight then
            return pet.name
        end
    end

    return pets[1].name
end

local function getPetWeight(petName)
    if not petRegistry or not petName then
        return math.random(80, 200) / 100
    end

    local petData = petRegistry[petName]
    if not petData then
        return math.random(80, 200) / 100
    end

    local weight = petData.SellPrice and (petData.SellPrice / 1000000) or math.random(80, 200) / 100
    return weight
end

local function manualReroll(objectId)
    local currentTime = tick()
    if currentTime - lastReloadTime < reloadCooldown then
        statusLabel.Text = "COOLDOWN: " .. math.ceil(reloadCooldown - (currentTime - lastReloadTime)) .. "s"
        return
    end

    if not objectId or not espCache[objectId] then return end

    local object = getObjectFromId(objectId)
    if not object then return end

    local eggName = object:GetAttribute("EggName")
    local randomPet = getRandomPetFromEgg(eggName)

    if randomPet then
        local weight = getPetWeight(randomPet)

        local billboard = espCache[objectId].billboard
        local frame = espCache[objectId].frame
        local nameLabel = espCache[objectId].nameLabel
        local weightLabel = espCache[objectId].weightLabel
        local glowFrame = espCache[objectId].glowFrame

        nameLabel.Text = randomPet
        weightLabel.Text = weight .. " KG"

        petInfoLabel.Text = "Pet: " .. randomPet .. " | Weight: " .. weight .. " KG"

        local color = Color3.fromRGB(255, 255, 255)
        if weight >= 6.0 then
            color = Color3.fromRGB(255, 215, 0)
            glowFrame.BackgroundColor3 = Color3.fromRGB(255, 215, 0)
            glowFrame.BackgroundTransparency = 0.3
        elseif weight >= 4.0 then
            color = Color3.fromRGB(255, 165, 0)
            glowFrame.BackgroundColor3 = Color3.fromRGB(255, 165, 0)
            glowFrame.BackgroundTransparency = 0.5
        elseif weight >= 2.0 then
            color = Color3.fromRGB(100, 255, 100)
            glowFrame.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
            glowFrame.BackgroundTransparency = 0.5
        else
            glowFrame.BackgroundTransparency = 1
        end

        nameLabel.TextColor3 = color
        weightLabel.TextColor3 = color
        frame.BackgroundColor3 = Color3.fromRGB(math.min(255, color.R * 255 * 0.3), math.min(255, color.G * 255 * 0.3), math.min(255, color.B * 255 * 0.3))

        local scaleTween = tweenService:Create(billboard, TweenInfo.new(0.3, Enum.EasingStyle.Back), {Size = UDim2.new(0, 220, 0, 110)})
        scaleTween:Play()
        scaleTween.Completed:Connect(function()
            tweenService:Create(billboard, TweenInfo.new(0.2, Enum.EasingStyle.Back), {Size = UDim2.new(0, 200, 0, 100)}):Play()
        end)

        lastReloadTime = currentTime
        statusLabel.Text = "REROLLED SUCCESSFULLY"
    end
end

local function AddEggESP(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end

    local objectId = object:GetAttribute("OBJECT_UUID")
    if not objectId then return end

    local eggName = object:GetAttribute("EggName") or "Unknown Egg"
    local petName = eggPets[objectId]

    -- Create medium-sized BillboardGui ESP
    local billboard = Instance.new("BillboardGui")
    billboard.Size = UDim2.new(0, 200, 0, 80)
    billboard.StudsOffset = Vector3.new(0, 2, 0)
    billboard.Parent = object
    billboard.Adornee = object
    billboard.AlwaysOnTop = true

    -- Main frame with background
    local frame = Instance.new("Frame")
    frame.Size = UDim2.new(1, 0, 1, 0)
    frame.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    frame.BackgroundTransparency = 0.4
    frame.BorderSizePixel = 0
    frame.Parent = billboard

    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = frame

    -- Glow effect
    local glow = Instance.new("Frame")
    glow.Size = UDim2.new(1, 4, 1, 4)
    glow.Position = UDim2.new(0, -2, 0, -2)
    glow.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    glow.BackgroundTransparency = 0.9
    glow.BorderSizePixel = 0
    glow.ZIndex = frame.ZIndex - 1
    glow.Parent = billboard

    local glowCorner = Instance.new("UICorner")
    glowCorner.CornerRadius = UDim.new(0, 10)
    glowCorner.Parent = glow

    -- Status icon
    local statusIcon = Instance.new("TextLabel")
    statusIcon.Size = UDim2.new(0, 25, 0, 25)
    statusIcon.Position = UDim2.new(0, 5, 0, 5)
    statusIcon.BackgroundTransparency = 1
    statusIcon.Text = "⏳"
    statusIcon.TextColor3 = Color3.fromRGB(255, 255, 100)
    statusIcon.TextSize = 16
    statusIcon.Font = Enum.Font.SourceSansBold
    statusIcon.Parent = frame

    -- Egg name label (2x larger text)
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Size = UDim2.new(0, 165, 0, 25)
    nameLabel.Position = UDim2.new(0, 32, 0, 2)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = eggName
    nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    nameLabel.TextSize = 24
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.TextXAlignment = Enum.TextXAlignment.Left
    nameLabel.TextStrokeTransparency = 0
    nameLabel.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
    nameLabel.Parent = frame

    -- Pet info label (2x larger text)
    local petLabel = Instance.new("TextLabel")
    petLabel.Size = UDim2.new(0, 165, 0, 20)
    petLabel.Position = UDim2.new(0, 32, 0, 25)
    petLabel.BackgroundTransparency = 1
    petLabel.Text = "🎲 Rolling..."
    petLabel.TextColor3 = Color3.fromRGB(255, 255, 100)
    petLabel.TextSize = 20
    petLabel.Font = Enum.Font.SourceSans
    petLabel.TextXAlignment = Enum.TextXAlignment.Left
    petLabel.TextStrokeTransparency = 0
    petLabel.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
    petLabel.Parent = frame

    -- Weight label (2x larger text, no KG)
    local weightLabel = Instance.new("TextLabel")
    weightLabel.Size = UDim2.new(0, 165, 0, 20)
    weightLabel.Position = UDim2.new(0, 32, 0, 45)
    weightLabel.BackgroundTransparency = 1
    weightLabel.Text = ""
    weightLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    weightLabel.TextSize = 18
    weightLabel.Font = Enum.Font.SourceSans
    weightLabel.TextXAlignment = Enum.TextXAlignment.Left
    weightLabel.TextStrokeTransparency = 0
    weightLabel.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
    weightLabel.Parent = frame

    -- Store ESP components
    espCache[objectId] = {
        billboard = billboard,
        frame = frame,
        glow = glow,
        statusIcon = statusIcon,
        nameLabel = nameLabel,
        petLabel = petLabel,
        weightLabel = weightLabel
    }

    activeEggs[objectId] = object

    if petName then
        -- Egg is ready to hatch
        readyEggs[objectId] = true
        local weight = getPetWeight(petName)

        statusIcon.Text = "✅"
        statusIcon.TextColor3 = Color3.fromRGB(100, 255, 100)
        petLabel.Text = "🐾 " .. petName
        weightLabel.Text = "⚖️ " .. string.format("%.1f", weight)

        -- Color based on weight
        local color = Color3.fromRGB(255, 255, 255)
        if weight >= 6.0 then
            color = Color3.fromRGB(255, 215, 0) -- Gold
            glow.BackgroundColor3 = Color3.fromRGB(255, 215, 0)
            glow.BackgroundTransparency = 0.5
        elseif weight >= 4.0 then
            color = Color3.fromRGB(255, 165, 0) -- Orange
            glow.BackgroundColor3 = Color3.fromRGB(255, 165, 0)
            glow.BackgroundTransparency = 0.6
        elseif weight >= 2.0 then
            color = Color3.fromRGB(100, 255, 100) -- Green
            glow.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
            glow.BackgroundTransparency = 0.6
        else
            glow.BackgroundTransparency = 0.9
        end

        petLabel.TextColor3 = color
        weightLabel.TextColor3 = color
    else
        -- Egg is still rolling
        readyEggs[objectId] = nil
        startRollAnimation(objectId, eggName)
    end

    updateStatsDisplay()
end

local function RemoveEggESP(object)
    if object:GetAttribute("OWNER") ~= localPlayer.Name then return end

    local objectId = object:GetAttribute("OBJECT_UUID")
    if not objectId then return end

    -- Clean up ESP
    if espCache[objectId] then
        if espCache[objectId].billboard then
            espCache[objectId].billboard:Destroy()
        end
        espCache[objectId] = nil
    end

    -- Clean up data
    activeEggs[objectId] = nil
    readyEggs[objectId] = nil
    lastRerollTime[objectId] = nil

    if rollAnimations[objectId] then
        rollAnimations[objectId] = nil
    end

    updateStatsDisplay()
end

local function UpdateEggESP(objectId, petName)
    local object = getObjectFromId(objectId)
    if not object or not espCache[objectId] then return end

    local weight = getPetWeight(petName)
    local label = espCache[objectId]
    local eggName = object:GetAttribute("EggName") or "Unknown Egg"

    -- Stop rolling animation
    if rollAnimations[objectId] then
        rollAnimations[objectId] = nil
    end

    -- Mark as ready
    readyEggs[objectId] = true

    label.Text = string.format("✅ %s\n🐾 %s | ⚖️ %.1f KG", eggName, petName, weight)

    -- Color based on weight
    if weight >= 6.0 then
        label.Color = Color3.fromRGB(255, 215, 0) -- Gold
    elseif weight >= 4.0 then
        label.Color = Color3.fromRGB(255, 165, 0) -- Orange
    elseif weight >= 2.0 then
        label.Color = Color3.fromRGB(100, 255, 100) -- Green
    else
        label.Color = Color3.fromRGB(255, 255, 255) -- White
    end

    updateStatsDisplay()
end

local function UpdateEsp(objectId, petName)
    local object = getObjectFromId(objectId)
    if not object or not espCache[objectId] then return end

    local weight = getPetWeight(petName)
    local label = espCache[objectId]

    if rollAnimations[objectId] then
        rollAnimations[objectId] = nil
    end

    label.Text = string.format("✅ %s\n🐾 %s | ⚖️ %.1f KG", object:GetAttribute("EggName") or "Unknown", petName, weight)

    local color = Color3.fromRGB(255, 255, 255)
    if weight >= 6.0 then
        color = Color3.fromRGB(255, 215, 0) -- Gold
    elseif weight >= 4.0 then
        color = Color3.fromRGB(255, 165, 0) -- Orange
    elseif weight >= 2.0 then
        color = Color3.fromRGB(100, 255, 100) -- Green
    else
        color = Color3.fromRGB(255, 255, 255) -- White
    end

    label.Color = color
end


local function UpdateAllESP()
    for objectId, object in pairs(activeEggs) do
        if not object or not object:IsDescendantOf(workspace) then
            activeEggs[objectId] = nil
            readyEggs[objectId] = nil
            if espCache[objectId] then
                espCache[objectId].Visible = false
            end
            continue
        end

        local label = espCache[objectId]
        if label then
            local pos, onScreen = currentCamera:WorldToViewportPoint(object:GetPivot().Position)
            if onScreen then
                label.Position = Vector2.new(pos.X, pos.Y)
                label.Visible = true
            else
                label.Visible = false
            end
        end
    end
end

-- Auto-reroll system
local function AutoRerollSystem()
    if not autoRerollEnabled then return end

    for objectId, object in pairs(activeEggs) do
        -- Only reroll eggs that are NOT ready to hatch
        if not readyEggs[objectId] then
            local currentTime = tick()
            if not lastRerollTime[objectId] or currentTime - lastRerollTime[objectId] >= rerollCooldown then
                performReroll(objectId)
                task.wait(0.1) -- Small delay between rerolls
            end
        end
    end
end





-- ═══════════════════════════════════════════════════════════════
--                    INITIALIZATION & CONNECTIONS
-- ═══════════════════════════════════════════════════════════════

-- Initialize existing eggs
for _, object in pairs(collectionService:GetTagged("PetEggServer")) do
    task.spawn(AddEggESP, object)
end

-- Connect events
connections[#connections + 1] = collectionService:GetInstanceAddedSignal("PetEggServer"):Connect(AddEggESP)
connections[#connections + 1] = collectionService:GetInstanceRemovedSignal("PetEggServer"):Connect(RemoveEggESP)

-- Hook the egg ready event
local old; old = hookfunction(getconnections(replicatedStorage.GameEvents.EggReadyToHatch_RE.OnClientEvent)[1].Function, newcclosure(function(objectId, petName)
    UpdateEggESP(objectId, petName)
    return old(objectId, petName)
end))

-- Main render loop
connections[#connections + 1] = runService.PreRender:Connect(UpdateAllESP)

-- Stats update loop
connections[#connections + 1] = runService.Heartbeat:Connect(function()
    updateStatsDisplay()
end)

-- Make GUI draggable
local dragging = false
local dragStart = nil
local startPos = nil

headerFrame.InputBegan:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseButton1 then
        dragging = true
        dragStart = input.Position
        startPos = mainFrame.Position
    end
end)

headerFrame.InputChanged:Connect(function(input)
    if dragging and input.UserInputType == Enum.UserInputType.MouseMovement then
        local delta = input.Position - dragStart
        mainFrame.Position = UDim2.new(startPos.X.Scale, startPos.X.Offset + delta.X, startPos.Y.Scale, startPos.Y.Offset + delta.Y)
    end
end)

headerFrame.InputEnded:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseButton1 then
        dragging = false
    end
end)

-- ═══════════════════════════════════════════════════════════════
--                         CLEANUP FUNCTION
-- ═══════════════════════════════════════════════════════════════

getgenv().EggReroller_Cleanup = function()
    -- Disconnect all connections
    for _, connection in pairs(connections) do
        if connection then
            connection:Disconnect()
        end
    end

    -- Clean up ESP labels
    for _, label in pairs(espCache) do
        if label and label.billboard then
            label.billboard:Destroy()
        end
    end

    -- Clean up GUI
    if gui then
        gui:Destroy()
    end

    -- Reset all variables
    espCache = {}
    activeEggs = {}
    readyEggs = {}
    connections = {}
    rollAnimations = {}
    lastRerollTime = {}
    selectedEgg = nil
    stats = {
        totalEggs = 0,
        rollingEggs = 0,
        readyEggs = 0,
        rerollsPerformed = 0
    }

    print("🚀 Extreme Egg Reroller V2.0 cleaned up successfully!")
end

-- ═══════════════════════════════════════════════════════════════
--                         STARTUP MESSAGE
-- ═══════════════════════════════════════════════════════════════

print("🚀 Extreme Egg Reroller V2.0 Loaded!")
print("🔍 Auto-detection system active")
print("🎲 Manual rerolling enabled")
print("🎯 Click the REROLL EGG button to reroll!")

-- Initial stats update
updateStatsDisplay()