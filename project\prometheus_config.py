-- Saved by UniversalSynSaveInstance (Join to Copy Games) https://discord.gg/wx4ThpAsmw

-- [[ Script name: <PERSON><PERSON><PERSON>, time of decompilation: Sun Jun  8 18:02:11 2025 ]]
local l_Assets_0 = game:GetService("ReplicatedStorage"):WaitForChild("Assets");
local l_PetAssets_0 = l_Assets_0.Models.PetAssets
local l_PetAnimations_0 = l_Assets_0:WaitFor<PERSON>hild("Animations"):Wait<PERSON><PERSON><PERSON>hild("PetAnimations");
local v3 = {
    Dog = {
        Description = "Digging Buddy: Occasionally digs up a random seed", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Dog"), 
        DefaultHunger = 1000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://135018170520317", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 10, 
        SellPrice = 55000, 
        Rarity = "Common", 
        Passives = {
            "Digging Buddy"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.Dog.Idle, 
            Walk = l_PetAnimations_0.Dog.Walk, 
            Dig = l_PetAnimations_0.Dog.Dig
        }, 
        States = {}
    }, 
    ["Golden Lab"] = {
        Description = "Digging Friend: Occasionally digs up a random seed at a higher chance", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Dog"), 
        Variant = "Golden Lab", 
        DefaultHunger = 1200, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://99376934607716", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 10, 
        SellPrice = 55000, 
        Rarity = "Common", 
        Passives = {
            "Digging Friend"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.Dog.Idle, 
            Walk = l_PetAnimations_0.Dog.Walk, 
            Dig = l_PetAnimations_0.Dog.Dig
        }, 
        States = {}
    }, 
    Bunny = {
        Description = "Carrot Chomper: Runs to carrots, eats them, and grants bonus sheckles (more than normal value)", 
		Actions = {}, 
        DefaultHunger = 1100, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://85830855120751", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 9, 
        SellPrice = 55000, 
        Rarity = "Common", 
        Passives = {
			"Let Him Cook",
            "King of the Grill",
            "Movement Variation"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.Bunny.Idle, 
            Walk = l_PetAnimations_0.Bunny.Walk, 
            Chomp = l_PetAnimations_0.Bunny.Chomp
        }, 
        States = {}
        },
		["Cooked Owl"] = {
			Description = "Let Him Cook: Occasionaly burns or cook a random nearby fruit & King of the Grill: Grants bonus experience per second gain to all active pets. Also very tasty!", 
			Actions = {}, 
			Model = l_PetAssets_0:FindFirstChild("Cooked Owl"), 
		    DefaultHunger = 30000, 
		    WeldOffset = CFrame.Angles(0, 1.5707963267948966, 1.5707963267948966),
			ToolWeldOffset = true, 
			ModelScalePerLevel = 0.005, 
		    Icon = "rbxassetid://78954652883059", 
			HungerFruitMultipliers = {}, 
			MovementType = "Grounded", 
			MovementSpeed = 8, 
		    SellPrice = 3000000 , 
		    Rarity = "Mythical", 
			Passives = {
			"Let Him Cook",
            "King of the Grill",
            "Movement Variation"
			}, 
			YHeightScaler = 0.1, 
			Animations = {
			    Idle = l_PetAnimations_0.CookedOwl.Idle, 
			    Walk = l_PetAnimations_0.CookedOwl.Walk,
                Fly  = l_PetAnimations_0.CookedOwl.Fly,
                FlyUp = l_PetAnimations_0.CookedOwl.FlyUp,
                FlyDown = l_PetAnimations_0.CookedOwl.FlyDown
			
	    },
		States = {}
  }, 
       ["Black Bunny"] = {
        Description = "Carrot Devourer: Runs to carrots, eats them, and grants bonus sheckles (more than normal value)", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Bunny"), 
        Variant = "Black Bunny", 
        DefaultHunger = 1300, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://86614624778104", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 9, 
        SellPrice = 165000, 
        Rarity = "Uncommon", 
        Passives = {
            "Carrot Chomper"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.Bunny.Idle, 
            Walk = l_PetAnimations_0.Bunny.Walk, 
            Chomp = l_PetAnimations_0.Bunny.Chomp
        }, 
        States = {}
    }, 
    Cat = {
        Description = "Cat Nap: Cat naps in a random spot in your farm, emitting an aura that boosts nearby fruit size", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Cat"), 
        DefaultHunger = 1400, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://136444015144013", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 12, 
        SellPrice = 165000, 
        Rarity = "Uncommon", 
        Passives = {
            "Cat Nap"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.Cat.Idle, 
            Walk = l_PetAnimations_0.Cat.Walk, 
            Nap = l_PetAnimations_0.Cat.Nap
        }, 
        States = {}
	}, 
	["Raptor"] = {
		["Description"] = "Clever Claws: Small chance fruit gets Amber mutation after collecting! & Raptor Dance: Player has increased movement speed",
		["Actions"] = {},
		["Model"] = l_PetAssets_0:FindFirstChild("Raptor"),
		["DefaultHunger"] = 40000,
		["WeldOffset"] = CFrame.Angles(0, 0, 0),
		["ToolWeldOffset"] = true,
		["TwoHanded"] = true,
		["ModelScalePerLevel"] = 0.005,
		["Icon"] = "rbxassetid://133649762905181",
		["HungerFruitMultipliers"] = {},
		["MovementType"] = "Grounded",
		["MovementSpeed"] = 12,
		["SellPrice"] = 5000000,
		["Rarity"] = "Legendary",
		["Passives"] = { "Clever Claws", "Raptor Dance" },
		["YHeightScaler"] = 0,
		["Animations"] = {
			["Idle"] = l_PetAnimations_0.Raptor.Idle,
			["Walk"] = l_PetAnimations_0.Raptor.Walk
		}
	},
	["Stegosaurus"] = {
		["Description"] = "Prehistoric Doubling: Small chance to duplicate harvested fruit & Prehistoric Harvester: Increased chance to duplicate harvested prehistoric type plant",
		["Actions"] = {},
		["Model"] = l_PetAssets_0:FindFirstChild("Stegosaurus"),
		["DefaultHunger"] = 40000,
		["WeldOffset"] = CFrame.Angles(0, 0, 0),
		["ToolWeldOffset"] = true,
		["TwoHanded"] = true,
		["ModelScalePerLevel"] = 0.005,
		["Icon"] = "rbxassetid://115750504063562",
		["HungerFruitMultipliers"] = {},
		["MovementType"] = "Grounded",
		["MovementSpeed"] = 8,
		["SellPrice"] = 5000000,
		["Rarity"] = "Legendary",
		["Passives"] = { "Prehistoric Doubling", "Prehistoric Harvester" },
		["YHeightScaler"] = 0,
		["Animations"] = {
			["Idle"] = l_PetAnimations_0.Stegosaurus.Idle,
			["Walk"] = l_PetAnimations_0.Stegosaurus.Walk
		}
	},
	["Triceratops"] = {
		["Description"] = "Tri-Horn: Rams into random plants and advances their growth",
		["Actions"] = {},
		["Model"] = l_PetAssets_0:FindFirstChild("Triceratops"),
		["DefaultHunger"] = 40000,
		["WeldOffset"] = CFrame.Angles(0, 0, 0),
		["ToolWeldOffset"] = true,
		["TwoHanded"] = true,
		["ModelScalePerLevel"] = 0.005,
		["Icon"] = "rbxassetid://133031079193526",
		["HungerFruitMultipliers"] = {},
		["MovementType"] = "Grounded",
		["MovementSpeed"] = 10,
		["SellPrice"] = 5000000,
		["Rarity"] = "Legendary",
		["Passives"] = { "Tri-Horn" },
		["YHeightScaler"] = 0,
		["Animations"] = {
			["Idle"] = l_PetAnimations_0.Triceratops.Idle,
			["Walk"] = l_PetAnimations_0.Triceratops.Walk,
			["Run"] = l_PetAnimations_0.Triceratops.Run,
			["Ram"] = l_PetAnimations_0.Triceratops.Ram
		}
	},
	["Pterodactyl"] = {
		["Description"] = "Sky Reptile: Occasionally applies Windstruck mutation to multiple nearby fruits! & Air Time: Player has increased jump height",
		["Actions"] = {},
		["Model"] = l_PetAssets_0:FindFirstChild("Pterodactyl"),
		["DefaultHunger"] = 40000,
		["WeldOffset"] = CFrame.Angles(0, 0, 0),
		["ToolWeldOffset"] = true,
		["TwoHanded"] = true,
		["ModelScalePerLevel"] = 0.005,
		["Icon"] = "rbxassetid://132325249273328",
		["HungerFruitMultipliers"] = {},
		["MovementType"] = "Grounded",
		["MovementSpeed"] = 10,
		["SellPrice"] = 5000000,
		["Rarity"] = "Legendary",
		["Passives"] = { "Sky Reptile", "Air Time", "Movement Variation" },
		["YHeightScaler"] = 1,
		["Animations"] = {
			["Idle"] = l_PetAnimations_0.Pterodactyl.Idle,
			["Walk"] = l_PetAnimations_0.Pterodactyl.Walk,
			["Flap"] = l_PetAnimations_0.Pterodactyl.Flap,
			["Fly"] = l_PetAnimations_0.Pterodactyl.Fly,
			["FlyUp"] = l_PetAnimations_0.Pterodactyl.FlyUp,
			["FlyDown"] = l_PetAnimations_0.Pterodactyl.FlyDown
		}
	},
	["Brontosaurus"] = {
		["Description"] = "Giant Incubator: Pets hatched from eggs have higher base weight",
		["Actions"] = {},
		["Model"] = l_PetAssets_0:FindFirstChild("Brontosaurus"),
		["DefaultHunger"] = 80000,
		["WeldOffset"] = CFrame.Angles(0, 0, 0),
		["ToolWeldOffset"] = true,
		["TwoHanded"] = true,
		["ModelScalePerLevel"] = 0.005,
		["Icon"] = "rbxassetid://138431192706334",
		["HungerFruitMultipliers"] = {},
		["MovementType"] = "Grounded",
		["MovementSpeed"] = 5,
		["SellPrice"] = 20000000,
		["Rarity"] = "Mythical",
		["Passives"] = { "Giant Incubator" },
		["YHeightScaler"] = 0,
		["Animations"] = {
			["Idle"] = l_PetAnimations_0.Brontosaurus.Idle,
			["Walk"] = l_PetAnimations_0.Brontosaurus.Walk
		}
	},
	["T-Rex"] = {
		["Description"] = "Apex Predator: Occasionally eats a random mutation from a fruit in your garden then roars and applies that mutation to other fruits in your garden.",
		["Actions"] = {},
		["Model"] = l_PetAssets_0:FindFirstChild("T-Rex"),
		["DefaultHunger"] = 60000,
		["WeldOffset"] = CFrame.Angles(0, 0, 0),
		["ToolWeldOffset"] = true,
		["TwoHanded"] = true,
		["ModelScalePerLevel"] = 0.005,
		["Icon"] = "rbxassetid://72024850228702",
		["HungerFruitMultipliers"] = {},
		["MovementType"] = "Grounded",
		["MovementSpeed"] = 8,
		["SellPrice"] = 40000000,
		["Rarity"] = "Divine",
		["Passives"] = { "Apex Predator" },
		["YHeightScaler"] = 0.05,
		["Animations"] = {
			["Idle"] = l_PetAnimations_0.Trex.Idle,
			["Walk"] = l_PetAnimations_0.Trex.Walk,
			["Eat"] = l_PetAnimations_0.Trex.Eat,
			["Roar"] = l_PetAnimations_0.Trex.Roar
		}
	},
	["Parasaurolophus"] = {
		["Description"] = "Crowbar Head: Occasionally, goes to the cosmetic crate with the highest time and reduces time to open!",
		["Actions"] = {},
		["Model"] = l_PetAssets_0:FindFirstChild("Parasaurolophus"),
		["DefaultHunger"] = 40000,
		["WeldOffset"] = CFrame.Angles(0, 0, 0),
		["ToolWeldOffset"] = true,
		["TwoHanded"] = true,
		["ModelScalePerLevel"] = 0.005,
		["Icon"] = "rbxassetid://77060347493123",
		["HungerFruitMultipliers"] = {},
		["MovementType"] = "Grounded",
		["MovementSpeed"] = 9,
		["SellPrice"] = 5000000,
		["Rarity"] = "Legendary",
		["Passives"] = { "Apex Predator" },
		["YHeightScaler"] = 0.05,
		["Animations"] = {
			["Idle"] = l_PetAnimations_0.Parasaurolophus.Idle,
			["Walk"] = l_PetAnimations_0.Parasaurolophus.Walk,
			["Crowbar"] = l_PetAnimations_0.Parasaurolophus.Crowbar
		}
	},
	["Iguanodon"] = {
		["Description"] = "Dino Herd: Grants bonus experience per second gain to all Dinosaur type active pets",
		["Actions"] = {},
		["Model"] = l_PetAssets_0:FindFirstChild("Iguanodon"),
		["DefaultHunger"] = 40000,
		["WeldOffset"] = CFrame.Angles(0, 0, 0),
		["ToolWeldOffset"] = true,
		["TwoHanded"] = true,
		["ModelScalePerLevel"] = 0.005,
		["Icon"] = "rbxassetid://132997806707299",
		["HungerFruitMultipliers"] = {},
		["MovementType"] = "Grounded",
		["MovementSpeed"] = 8,
		["SellPrice"] = 5000000,
		["Rarity"] = "Legendary",
		["Passives"] = { "Apex Predator" },
		["YHeightScaler"] = 0.05,
		["Animations"] = {
			["Idle"] = l_PetAnimations_0.Iguanodon.Idle,
			["Walk"] = l_PetAnimations_0.Iguanodon.Walk
		}
	},
	["Pachycephalosaurus"] = {
		["Description"] = "Crafty Dome: Grants a small chance to duplicate the crafted item.",
		["Actions"] = {},
		["Model"] = l_PetAssets_0:FindFirstChild("Pachycephalosaurus"),
		["DefaultHunger"] = 40000,
		["WeldOffset"] = CFrame.Angles(0, 0, 0),
		["ToolWeldOffset"] = true,
		["TwoHanded"] = true,
		["ModelScalePerLevel"] = 0.005,
		["Icon"] = "rbxassetid://98967783170808",
		["HungerFruitMultipliers"] = {},
		["MovementType"] = "Grounded",
		["MovementSpeed"] = 9,
		["SellPrice"] = 5000000,
		["Rarity"] = "Legendary",
		["Passives"] = { "Apex Predator" },
		["YHeightScaler"] = 0.05,
		["Animations"] = {
			["Idle"] = l_PetAnimations_0.Pachycephalosaurus.Idle,
			["Walk"] = l_PetAnimations_0.Pachycephalosaurus.Walk
		}
	},
	["Dilophosaurus"] = {
		["Description"] = "Frilled Reptile: Occasionally opens its frills and spits out venom! The venom spreads to other random pets, advancing cooldown OR granting bonus xp",
		["Actions"] = {},
		["Model"] = l_PetAssets_0:FindFirstChild("Dilophosaurus"),
		["DefaultHunger"] = 30000,
		["WeldOffset"] = CFrame.Angles(0, 0, 0),
		["ToolWeldOffset"] = true,
		["TwoHanded"] = true,
		["ModelScalePerLevel"] = 0.005,
		["Icon"] = "rbxassetid://88442192911950",
		["HungerFruitMultipliers"] = {},
		["MovementType"] = "Grounded",
		["MovementSpeed"] = 10,
		["SellPrice"] = 10000000,
		["Rarity"] = "Mythical",
		["Passives"] = { "Apex Predator" },
		["YHeightScaler"] = 0.05,
		["Animations"] = {
			["Idle"] = l_PetAnimations_0.Dilophosaurus.Idle,
			["Walk"] = l_PetAnimations_0.Dilophosaurus.Walk,
			["Frill"] = l_PetAnimations_0.Dilophosaurus.Frill
		}
	},
	--[[
	["Ankylosaurus"] = {
		["Description"] = "Armored Defender: When a player steals a fruit from you, grants a chance you get the stolen fruit as well.",
		["Actions"] = {},
		["Model"] = l_PetAssets_0:FindFirstChild("Ankylosaurus"),
		["DefaultHunger"] = 40000,
		["WeldOffset"] = CFrame.Angles(0, 0, 0),
		["ToolWeldOffset"] = true,
		["TwoHanded"] = true,
		["ModelScalePerLevel"] = 0.005,
		["Icon"] = "rbxassetid://128962631009648",
		["HungerFruitMultipliers"] = {},
		["MovementType"] = "Grounded",
		["MovementSpeed"] = 8,
		["SellPrice"] = 20000000,
		["Rarity"] = "Mythical",
		["Passives"] = { "Apex Predator" },
		["YHeightScaler"] = 0.05,
		["Animations"] = {
			["Idle"] = l_PetAnimations_0.Ankylosaurus.Idle,
			["Walk"] = l_PetAnimations_0.Ankylosaurus.Walk
		}
	},
	--]]
	
	["Spinosaurus"] = {
		["Description"] = "Occasionally, devours a random mutation from random fruits in your garden each, roars and applies it to 1 other random fruit in your garden!",
		["Actions"] = {},
		["Model"] = l_PetAssets_0:FindFirstChild("Spinosaurus"),
		["DefaultHunger"] = 25000,
		["WeldOffset"] = CFrame.Angles(0, 0, 0),
		["ToolWeldOffset"] = true,
		["TwoHanded"] = true,
		["ModelScalePerLevel"] = 0.005,
		["Icon"] = "rbxassetid://78132119445447",
		["HungerFruitMultipliers"] = {},
		["MovementType"] = "Grounded",
		["MovementSpeed"] = 9,
		["SellPrice"] = 40000000,
		["Rarity"] = "Divine",
		["Passives"] = { "Apex Predator" },
		["YHeightScaler"] = 0.05,
		["Animations"] = {
			["Idle"] = l_PetAnimations_0.Spinosaurus.Idle,
			["Walk"] = l_PetAnimations_0.Spinosaurus.Walk,
			["Eat"] = l_PetAnimations_0.Spinosaurus.Eat,
			["Roar"] = l_PetAnimations_0.Spinosaurus.Roar
		}
	},
    ["Orange Tabby"] = {
        Description = "Orange Tabby: Orange Tabby naps in a random spot in your farm, emitting an aura that boosts nearby fruit size", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Cat"), 
        Variant = "Orange Tabby", 
        DefaultHunger = 1500, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://103360220936666", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 12, 
        SellPrice = 660000, 
        Rarity = "Common", 
        Passives = {
            "Lazy Nap"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.Cat.Idle, 
            Walk = l_PetAnimations_0.Cat.Walk, 
            Nap = l_PetAnimations_0.Cat.Nap
        }, 
        States = {}
    }, 
    Deer = {
        Description = "Forester: When harvesting berry plants, there is a chance the fruit will remain", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Deer"), 
        DefaultHunger = 2500, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.009, 
        Icon = "rbxassetid://91926785467809", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 12, 
        SellPrice = 165000, 
        Rarity = "Uncommon", 
        Passives = {
            "Forester"
        }, 
        YHeightScaler = 0.65, 
        Animations = {
            Idle = l_PetAnimations_0.Deer.Idle, 
            Walk = l_PetAnimations_0.Deer.Walk, 
            Stomp = l_PetAnimations_0.Deer.Stomp
        }, 
        States = {}
    }, 
    ["Spotted Deer"] = {
        Description = "Forester: When harvesting berry plants, there is a chance the fruit will remain", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Spotted Deer"), 
        DefaultHunger = 2500, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.009, 
        Icon = "rbxassetid://126439207915258", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 12, 
        SellPrice = 660000, 
        Rarity = "Rare", 
        Passives = {
            "Spotted Forester"
        }, 
        YHeightScaler = 0.65, 
        Animations = {
            Idle = l_PetAnimations_0.Deer.Idle, 
            Walk = l_PetAnimations_0.Deer.Walk, 
            Stomp = l_PetAnimations_0.Deer.Stomp
        }, 
        States = {}
    }, 
    Monkey = {
        Description = "Cheeky Refund: 3% chance to get your fruit back when you sell it", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Monkey"), 
        DefaultHunger = 7400, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://113881196210664", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 10, 
        SellPrice = 660000, 
        Rarity = "Rare", 
        Passives = {
            "Cheeky Refund"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.Monkey.Idle, 
            Walk = l_PetAnimations_0.Monkey.Walk, 
            Steal = l_PetAnimations_0.Monkey.Steal
        }, 
        States = {}
    }, 
    ["Silver Monkey"] = {
        Description = "Cheeky Refund: 3% chance to get your fruit back when you sell it", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Monkey"), 
        Variant = "Silver Monkey", 
        DefaultHunger = 8000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://136985272620600", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 10, 
        SellPrice = 3300000, 
        Rarity = "Legendary", 
        Passives = {
            "Premium Cheeky Refund"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.Monkey.Idle, 
            Walk = l_PetAnimations_0.Monkey.Walk, 
            Steal = l_PetAnimations_0.Monkey.Steal
        }, 
        States = {}
    }, 
    Chicken = {
        Description = "Eggcelerator: Decreases the time needed to hatch other eggs", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Chicken"), 
        DefaultHunger = 3400, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://108080824427369", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 10, 
        SellPrice = 165000, 
        Rarity = "Uncommon", 
        Passives = {
            "Eggcelerator"
        }, 
        YHeightScaler = 0.15, 
        Animations = {
            Idle = l_PetAnimations_0.Chicken.Idle, 
            Walk = l_PetAnimations_0.Chicken.Walk
        }, 
        States = {}
    }, 
    Rooster = {
        Description = "Eggcelerator: Decreases the time needed to hatch other eggs", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Rooster"), 
        DefaultHunger = 4000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://137107493326109", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 10, 
        SellPrice = 650000, 
        Rarity = "Rare", 
        Passives = {
            "Better Eggcelerator"
        }, 
        YHeightScaler = 0.15, 
        Animations = {
            Idle = l_PetAnimations_0.Chicken.Idle, 
            Walk = l_PetAnimations_0.Chicken.Walk
        }, 
        States = {}
    }, 
    Pig = {
        Description = "Fertilizer Frenzy: Occasionally releases a fertilizing AOE boosting plant size and mutation chance", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Pig"), 
        DefaultHunger = 5000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.008, 
        Icon = "rbxassetid://134476443266448", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 8, 
        SellPrice = 650000, 
        Rarity = "Uncommon", 
        Passives = {
            "Fertilizer Frenzy"
        }, 
        YHeightScaler = 0.6, 
        Animations = {
            Idle = l_PetAnimations_0.Pig.Idle, 
            Walk = l_PetAnimations_0.Pig.Walk, 
            Frenzy = l_PetAnimations_0.Pig.Frenzy
        }, 
        States = {}
    }, 
    Turtle = {
        Description = "Turtle Tinkerer: Slowing aura that makes sprinklers last longer", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Turtle"), 
        DefaultHunger = 10000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.004, 
        Icon = "rbxassetid://92906330087175", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 5, 
        SellPrice = 3300000, 
        Rarity = "Legendary", 
        Passives = {
            "Turtle Tinkerer"
        }, 
        YHeightScaler = -0.32, 
        Animations = {
            Idle = l_PetAnimations_0.Turtle.Idle, 
            Walk = l_PetAnimations_0.Turtle.Walk
        }, 
        States = {}
    }, 
    Cow = {
        Description = "Milk of the Land: Fertilizing aura that boosts nearby plant growth speed ", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Cow"), 
        DefaultHunger = 9500, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://118832676475537", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 8, 
        SellPrice = 3300000, 
        Rarity = "Legendary", 
        YHeightScaler = 0.5, 
        Passives = {
            "Milk of the Land"
        }, 
        Animations = {
            Idle = l_PetAnimations_0.Cow.Idle, 
            Walk = l_PetAnimations_0.Cow.Walk
        }, 
        States = {}
    }, 
    Snail = {
        Description = "Slow and Steady: Increased lucky harvest chance", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Snail"), 
        DefaultHunger = 12000, 
        WeldOffset = CFrame.Angles(0, 1.5707963267948966, 3.141592653589793), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://80970021440625", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 3, 
        SellPrice = 60000000, 
        Rarity = "Mythical", 
        Passives = {
            "Slow and Steady"
        }, 
        YHeightScaler = 0, 
        Animations = {
            Idle = l_PetAnimations_0.Snail.Idle, 
            Walk = l_PetAnimations_0.Snail.Walk
        }, 
        States = {}
    }, 
    ["Giant Ant"] = {
        Description = "For the Blue Colony: Small chance to duplicate harvested plant & Candy Harvester: Increased chance to duplicate harvested candy type plant", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Giant Ant"), 
        DefaultHunger = 18000, 
        WeldOffset = CFrame.Angles(0, 1.5707963267948966, 3.141592653589793), 
        ModelScalePerLevel = 0.003, 
        Icon = "rbxassetid://71413253805996", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 5, 
        SellPrice = 60000000, 
        Rarity = "Mythical", 
        Passives = {
            "For the Blue Colony", 
            "Candy Harvester"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.Ant.Idle, 
            Walk = l_PetAnimations_0.Ant.Walk, 
            Grab = l_PetAnimations_0.Ant.Grab
        }
    }, 
    Dragonfly = {
        Description = "Transmutation: Every now and then turns a fruit to gold", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Dragonfly"), 
        DefaultHunger = 100000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 0), 
        ModelScalePerLevel = 0.008, 
        Icon = "rbxassetid://118484611393651", 
        HungerFruitMultipliers = {}, 
        MovementType = "Flight", 
        MovementSpeed = 9, 
        SellPrice = 60000000, 
        Rarity = "Divine", 
        Passives = {
            "Transmutation"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.Dragonfly.Idle, 
            Walk = l_PetAnimations_0.Dragonfly.Walk
        }
    }, 
    ["Polar Bear"] = {
        Description = "Polar Express: Occasionally sets a random nearby fruit cold, turning it into Chilled with a small chance for Frozen", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Polar Bear"), 
        DefaultHunger = 20000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://72209118254193", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 8, 
        SellPrice = 3300000, 
        Rarity = "Legendary", 
        Passives = {
            "Polar Express"
        }, 
        YHeightScaler = 0.35, 
        Animations = {
            Idle = l_PetAnimations_0.Bear.Idle, 
            Walk = l_PetAnimations_0.Bear.Walk, 
            Roar = l_PetAnimations_0.Bear.Roar
        }
    }, 
    Panda = {
        Description = "Bamboozle: Waddles to bamboo, eats it, and grants bonus sheckles (more than normal value)", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Panda"), 
        DefaultHunger = 20000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://107090327345246", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 8, 
        SellPrice = 165000, 
        Rarity = "Legendary", 
        Passives = {
            "Bamboozle"
        }, 
        YHeightScaler = 0.35, 
        Animations = {
            Idle = l_PetAnimations_0.Panda.Idle, 
            Walk = l_PetAnimations_0.Panda.Walk, 
            Chomp = l_PetAnimations_0.Panda.Chomp
        }
    }, 
    ["Sea Otter"] = {
        Description = "Water Spray: Water's plants randomly like a watering can", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Sea Otter"), 
        DefaultHunger = 30000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://94422445572440", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 9, 
        SellPrice = 3300000, 
        Rarity = "Legendary", 
        Passives = {
            "Water Spray"
        }, 
        YHeightScaler = -0.25, 
        Animations = {
            Idle = l_PetAnimations_0.Otter.Idle, 
            Walk = l_PetAnimations_0.Otter.Walk, 
            Spray = l_PetAnimations_0.Otter.Spray
        }
    }, 
    Caterpillar = {
        Description = "Leaf Lover Passive: Boost nearby Leafy plants growth rate", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Caterpillar"), 
        DefaultHunger = 25000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.0065, 
        Icon = "rbxassetid://119651461526366", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 6, 
        SellPrice = 60000000, 
        Rarity = "Mythical", 
        Passives = {
            "Leaf Lover"
        }, 
        YHeightScaler = -0.55, 
        Animations = {
            Idle = l_PetAnimations_0.Caterpillar.Idle, 
            Walk = l_PetAnimations_0.Caterpillar.Walk
        }
    }, 
    ["Praying Mantis"] = {
        Description = "Zen Zone: Prays, then gives plants in AOE Buff that increases the chance of gold fruit from plants", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Praying Mantis"), 
        DefaultHunger = 55000, 
        WeldOffset = CFrame.Angles(0, 1.5707963267948966, 3.141592653589793), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://121485029406440", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 8, 
        SellPrice = 60000000, 
        Rarity = "Mythical", 
        Passives = {
            "Zen Zone"
        }, 
        YHeightScaler = 0.1, 
        Animations = {
            Idle = l_PetAnimations_0.Mantis.Idle, 
            Walk = l_PetAnimations_0.Mantis.Walk, 
            Pray = l_PetAnimations_0.Mantis.Pray
        }
    }, 
    Hedgehog = {
        Description = "Prickly Lover: Makes prickly fruit grow bigger", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Hedgehog"), 
        DefaultHunger = 30000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://83544966481425", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 7, 
        SellPrice = 1000000, 
        Rarity = "Rare", 
        Passives = {
            "Prickly Lover"
        }, 
        YHeightScaler = 0.1, 
        Animations = {
            Idle = l_PetAnimations_0.Hedgehog.Idle, 
            Walk = l_PetAnimations_0.Hedgehog.Walk, 
            Curl = l_PetAnimations_0.Hedgehog.Curl, 
            CurlLoop = l_PetAnimations_0.Hedgehog.CurlLoop
        }
    }, 
    Kiwi = {
        Description = "Nocturnal Nursery: Occasionally reduces the hatch time of the egg with the most hatch time left", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Kiwi"), 
        DefaultHunger = 50000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://104651906442347", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 8, 
        SellPrice = 1000000, 
        Rarity = "Rare", 
        Passives = {
            "Nocturnal Nursery"
        }, 
        YHeightScaler = 0.9, 
        Animations = {
            Idle = l_PetAnimations_0.Kiwi.Idle, 
            Walk = l_PetAnimations_0.Kiwi.Walk, 
            Nurse = l_PetAnimations_0.Kiwi.Nurse
        }
    }, 
    Mole = {
        Description = "Treasure Hunter: Will occasionally dig down to find gear or sheckles", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Mole"), 
        DefaultHunger = 50000, 
        WeldOffset = CFrame.Angles(0, 1.5707963267948966, 0), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://79089804794269", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 6, 
        SellPrice = 2000000, 
        Rarity = "Legendary", 
        Passives = {
            "Treasure Hunter"
        }, 
        YHeightScaler = 0.1, 
        Animations = {
            Idle = l_PetAnimations_0.Mole.Idle, 
            Walk = l_PetAnimations_0.Mole.Walk, 
            DigDown = l_PetAnimations_0.Mole.DigDown, 
            DigUp = l_PetAnimations_0.Mole.DigUp, 
            DigWalk = l_PetAnimations_0.Mole.DigWalk
        }
    }, 
    Frog = {
        Description = "Croak: Will occasionally advance a nearby plant's growth by 24 hours", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Frog"), 
        DefaultHunger = 50000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://96930166899467", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 8, 
        SellPrice = 3000000, 
        Rarity = "Legendary", 
        Passives = {
            "Croak"
        }, 
        YHeightScaler = 0.1, 
        Animations = {
            Idle = l_PetAnimations_0.Frog.Idle, 
            Walk = l_PetAnimations_0.Frog.Walk, 
            Croak = l_PetAnimations_0.Frog.Croak
        }
    }, 
    ["Echo Frog"] = {
        Description = "Echo Croak: Will occasionally advance a nearby plant's growth by 24 hours", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Echo Frog"), 
        DefaultHunger = 50000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://102271225890686", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 8, 
        SellPrice = 5000000, 
        Rarity = "Mythical", 
        Passives = {
            "Echo Croak"
        }, 
        YHeightScaler = 0.1, 
        Animations = {
            Idle = l_PetAnimations_0.Frog.Idle, 
            Walk = l_PetAnimations_0.Frog.Walk, 
            Croak = l_PetAnimations_0.Frog.Croak
        }
    }, 
    Owl = {
        Description = "Prince of the Night: Grants bonus experience per second gain to all active pets.", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Owl"), 
        DefaultHunger = 50000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://138016343005291", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 8, 
        SellPrice = 8000000, 
        Rarity = "Mythical", 
        Passives = {
            "Prince of the Night", 
            "Movement Variation"
        }, 
        YHeightScaler = 0.5, 
        Animations = {
            Idle = l_PetAnimations_0.Owl.Idle, 
            Walk = l_PetAnimations_0.Owl.Walk, 
            Fly = l_PetAnimations_0.Owl.Fly, 
            FlyUp = l_PetAnimations_0.Owl.FlyUp, 
            FlyDown = l_PetAnimations_0.Owl.FlyDown
        }
    }, 
    ["Night Owl"] = {
        Description = "King of the Night: Grants bonus experience per second gain to all active pets.", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Night Owl"), 
        DefaultHunger = 50000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://101760640498094", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 8, 
        SellPrice = 10000000, 
        Rarity = "Divine", 
        Passives = {
            "King of the Night", 
            "Movement Variation"
        }, 
        YHeightScaler = 0.5, 
        Animations = {
            Idle = l_PetAnimations_0.Owl.Idle, 
            Walk = l_PetAnimations_0.Owl.Walk, 
            Fly = l_PetAnimations_0.Owl.Fly, 
            FlyUp = l_PetAnimations_0.Owl.FlyUp, 
            FlyDown = l_PetAnimations_0.Owl.FlyDown
        }
    }, 
    Raccoon = {
        Description = "Rascal: Occasionally steals (duplicates) fruit from other player's plot and hands it to you", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Raccoon"), 
        DefaultHunger = 45000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://136232391555861", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 16, 
        SellPrice = 10000000, 
        Rarity = "Divine", 
        Passives = {
            "Rascal"
        }, 
        YHeightScaler = 0.1, 
        Animations = {
            Idle = l_PetAnimations_0.Raccoon.Idle, 
            Walk = l_PetAnimations_0.Raccoon.Walk, 
            Steal = l_PetAnimations_0.Raccoon.Steal
        }
    }, 
    ["Grey Mouse"] = {
        Description = "Whisker Wisdom: Occasionally gains bonus experience & Scamper: Increase player movement speed", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Grey Mouse"), 
        DefaultHunger = 15000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://116310390398341", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 10, 
        SellPrice = 4400000, 
        Rarity = "Mythical", 
        Passives = {
            "Whisker Wisdom", 
            "Scamper"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.Mouse.Idle, 
            Walk = l_PetAnimations_0.Mouse.Walk, 
            Think = l_PetAnimations_0.Mouse.Think
        }, 
        States = {}
    }, 
    Squirrel = {
        Description = "Seed Stash: Planting seeds have a small chance to not be consumed. Rarer plants have less chance", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Squirrel"), 
        DefaultHunger = 15000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://96950434895806", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 8, 
        SellPrice = 5500000, 
        Rarity = "Mythical", 
        Passives = {
            "Seed Stash"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.Squirrel.Idle, 
            Walk = l_PetAnimations_0.Squirrel.Walk
        }, 
        States = {}
    }, 
    ["Brown Mouse"] = {
        Description = "Whiskier Wisdom: Occasionally gains bonus experience & Cheese Hop: Increase player jump height", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Grey Mouse"), 
        Variant = "Brown Mouse", 
        DefaultHunger = 15000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://94641319183999", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 9, 
        SellPrice = 5500000, 
        Rarity = "Mythical", 
        Passives = {
            "Whiskier Wisdom", 
            "Cheese Hop"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.Mouse.Idle, 
            Walk = l_PetAnimations_0.Mouse.Walk, 
            Think = l_PetAnimations_0.Mouse.Think
        }, 
        States = {}
    }, 
    ["Red Giant Ant"] = {
        Description = "For the Red Colony: Small chance to duplicate harvested plant & Fruit Harvester: Increased chance to duplicate harvested fruit type plant", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Giant Ant"), 
        Variant = "Red Giant Ant", 
        DefaultHunger = 15000, 
        WeldOffset = CFrame.Angles(0, 1.5707963267948966, 3.141592653589793), 
        ModelScalePerLevel = 0.003, 
        Icon = "rbxassetid://89449712431551", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 5, 
        SellPrice = 6500000, 
        Rarity = "Mythical", 
        Passives = {
            "For the Red Colony", 
            "Fruit Harvester"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.Ant.Idle, 
            Walk = l_PetAnimations_0.Ant.Walk, 
            Grab = l_PetAnimations_0.Ant.Grab
        }
    }, 
    ["Red Fox"] = {
        Description = "Every <Cooldown>m, goes to another player's plot and tries to steal a seed from a random plant. The rarer the plant, the harder it is to succeed!", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Red Fox"), 
        DefaultHunger = 35000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://116662854190616", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 16, 
        SellPrice = 7500000, 
        Rarity = "Mythical", 
        Passives = {
            "Scoundrel"
        }, 
        YHeightScaler = 0.1, 
        Animations = {
            Idle = l_PetAnimations_0.Fox.Idle, 
            Walk = l_PetAnimations_0.Fox.Walk, 
            Steal = l_PetAnimations_0.Fox.Steal
        }
    }, 
    ["Chicken Zombie"] = {
        Description = "Zombify: Occasionally has a chance to zombify a nearby random fruit & Eggcelerator: Decreases the time needed to hatch other eggs", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Chicken Zombie"), 
        DefaultHunger = 35000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://108581559611673", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 8, 
        SellPrice = 600000, 
        Rarity = "Mythical", 
        Passives = {
            "Zombify", 
            "Eggcelerator"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.ChickenZombie.Idle, 
            Walk = l_PetAnimations_0.ChickenZombie.Walk, 
            Zombify = l_PetAnimations_0.ChickenZombie.Zombify
        }, 
        States = {}
    }, 
    ["Blood Hedgehog"] = {
        Description = "Sanguine Spike: Makes prickly fruit have increased variant chance and grow bigger", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Blood Hedgehog"), 
        DefaultHunger = 30000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://76471191139414", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 7, 
        SellPrice = 33000000, 
        Rarity = "Legendary", 
        Passives = {
            "Sanguine Spike", 
            "Prickly Blessing"
        }, 
        YHeightScaler = 0.1, 
        Animations = {
            Idle = l_PetAnimations_0.Hedgehog.Idle, 
            Walk = l_PetAnimations_0.Hedgehog.Walk, 
            Curl = l_PetAnimations_0.Hedgehog.Curl, 
            CurlLoop = l_PetAnimations_0.Hedgehog.CurlLoop
        }
    }, 
    ["Blood Kiwi"] = {
        Description = "Crimson Cradle: Occasionally reduces the egg hatch time and boosts egg hatch speed", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Blood Kiwi"), 
        DefaultHunger = 45000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://87343374343285", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 8, 
        SellPrice = 30000000, 
        Rarity = "Mythical", 
        Passives = {
            "Crimson Cradle", 
            "Better Eggcelerator"
        }, 
        YHeightScaler = 0.9, 
        Animations = {
            Idle = l_PetAnimations_0.Kiwi.Idle, 
            Walk = l_PetAnimations_0.Kiwi.Walk, 
            Nurse = l_PetAnimations_0.Kiwi.Nurse
        }
    }, 
    ["Blood Owl"] = {
        Description = "Monarch of Midnight: Grants bonus experience per second gain to all active pets", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Blood Owl"), 
        DefaultHunger = 50000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://81262783747840", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 8, 
        SellPrice = 70000000, 
        Rarity = "Divine", 
        Passives = {
            "Monarch of Midnight", 
            "Movement Variation"
        }, 
        YHeightScaler = 0.5, 
        Animations = {
            Idle = l_PetAnimations_0.Owl.Idle, 
            Walk = l_PetAnimations_0.Owl.Walk, 
            Fly = l_PetAnimations_0.Owl.Fly, 
            FlyUp = l_PetAnimations_0.Owl.FlyUp, 
            FlyDown = l_PetAnimations_0.Owl.FlyDown
        }
    }, 
    ["Moon Cat"] = {
        Description = "Moon Nap: Moon cat naps in a random spot in your farm, and boosts nearby fruit size & Moon Harvest: Grants chance for Night type plants to replant when harvested ", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Cat"), 
        Variant = "Moon Cat", 
        DefaultHunger = 2400, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://72392850111255", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 10, 
        SellPrice = 25000000, 
        Rarity = "Legendary", 
        Passives = {
            "Moon Nap", 
            "Moon Harvest"
        }, 
        YHeightScaler = 0.4, 
        Animations = {
            Idle = l_PetAnimations_0.Cat.Idle, 
            Walk = l_PetAnimations_0.Cat.Walk, 
            Nap = l_PetAnimations_0.Cat.Nap
        }, 
        States = {}
    }, 
    Bee = {
        Description = "Pollinator: Occasionally pollinates fruit", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Bee"), 
        DefaultHunger = 25000, 
        WeldOffset = CFrame.Angles(0, 1.5707963267948966, -3.141592653589793), 
        ToolWeldOffset = true, 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://114116135745614", 
        HungerFruitMultipliers = {}, 
        MovementType = "Flight", 
        MovementSpeed = 9, 
        SellPrice = 400000, 
        Rarity = "Uncommon", 
        Passives = {
            "Pollinator"
        }, 
        YHeightScaler = 0.2, 
        Animations = {
            Idle = l_PetAnimations_0.Bee.Idle, 
            Walk = l_PetAnimations_0.Bee.Walk, 
            Pollinate = l_PetAnimations_0.Bee.Pollinate
        }
    }, 
    ["Honey Bee"] = {
        Description = "Beeter Pollinator: Occasionally pollinates fruit", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Bee"), 
        Variant = "Honey Bee", 
        DefaultHunger = 25000, 
        WeldOffset = CFrame.Angles(0, 1.5707963267948966, -3.141592653589793), 
        ToolWeldOffset = true, 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://134999468799162", 
        HungerFruitMultipliers = {}, 
        MovementType = "Flight", 
        MovementSpeed = 9, 
        SellPrice = 800000, 
        Rarity = "Rare", 
        Passives = {
            "Beeter Pollinator"
        }, 
        YHeightScaler = 0.2, 
        Animations = {
            Idle = l_PetAnimations_0.Bee.Idle, 
            Walk = l_PetAnimations_0.Bee.Walk, 
            Pollinate = l_PetAnimations_0.Bee.Pollinate
        }
    }, 
    ["Petal Bee"] = {
        Description = "Pollinator: Occasionally pollinates fruit & Flower Harvest: Harvested flowers have a chance to stay", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Bee"), 
        Variant = "Petal Bee", 
        DefaultHunger = 25000, 
        WeldOffset = CFrame.Angles(0, 1.5707963267948966, -3.141592653589793), 
        ToolWeldOffset = true, 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://137924182648564", 
        HungerFruitMultipliers = {}, 
        MovementType = "Flight", 
        MovementSpeed = 9, 
        SellPrice = 1000000, 
        Rarity = "Legendary", 
        Passives = {
            "Pollinator", 
            "Flower Harvest"
        }, 
        YHeightScaler = 0.03, 
        Animations = {
            Idle = l_PetAnimations_0.Bee.Idle, 
            Walk = l_PetAnimations_0.Bee.Walk, 
            Pollinate = l_PetAnimations_0.Bee.Pollinate
        }
    }, 
    ["Bear Bee"] = {
        Description = "Wanna-Bee: Occasionally tries to pollinate fruit, but it just ends up being Honey-Glazed", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Bear Bee"), 
        DefaultHunger = 45000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://97465846056354", 
        HungerFruitMultipliers = {}, 
        MovementType = "Grounded", 
        MovementSpeed = 8, 
        SellPrice = 4000000, 
        Rarity = "Mythical", 
        Passives = {
            "Wanna-Bee"
        }, 
        YHeightScaler = 0.35, 
        Animations = {
            Idle = l_PetAnimations_0.BearBee.Idle, 
            Walk = l_PetAnimations_0.BearBee.Walk, 
            Sit = l_PetAnimations_0.BearBee.Sit
        }
    }, 
    ["Queen Bee"] = {
        Description = "Queen Pollinator: Occasionally pollinates fruit instantly & For the Queen: Occasionally refrehes a random pet's ability", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Queen Bee"), 
        DefaultHunger = 65000, 
        WeldOffset = CFrame.Angles(0, 1.5707963267948966, -2.530727415391778), 
        ToolWeldOffset = true, 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://127281358672581", 
        HungerFruitMultipliers = {}, 
        MovementType = "Flight", 
        MovementSpeed = 5, 
        SellPrice = 8000000, 
        Rarity = "Divine", 
        Passives = {
            "Queen Pollinator", 
            "For the Queen"
        }, 
        YHeightScaler = 10, 
        Animations = {
            Idle = l_PetAnimations_0.QueenBee.Idle, 
            Walk = l_PetAnimations_0.QueenBee.Walk, 
            Spin = l_PetAnimations_0.QueenBee.Spin
        }
    }, 
    Wasp = {
        Description = "Wasp Pollinator: Occasionally pollinates fruit & Stinger: Occasionally stings pet with highest cooldown advancing cooldown", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Wasp"), 
        DefaultHunger = 28000, 
        WeldOffset = CFrame.Angles(0, 0, 0), 
        ToolWeldOffset = true, 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://72767862942880", 
        HungerFruitMultipliers = {}, 
        MovementType = "Flight", 
        MovementSpeed = 12, 
        SellPrice = 1000000, 
        Rarity = "Rare", 
        Passives = {
            "Wasp Pollinator", 
            "Stinger"
        }, 
        YHeightScaler = 0.2, 
        Animations = {
            Idle = l_PetAnimations_0.Wasp.Idle, 
            Walk = l_PetAnimations_0.Wasp.Walk, 
            Pollinate = l_PetAnimations_0.Wasp.Pollinate, 
            Sting = l_PetAnimations_0.Wasp.Sting
        }
    }, 
    ["Tarantula Hawk"] = {
        Description = "Wasp Pollinator: Occasionally pollinates fruit & Tarantula Stinger: Occasionally stings pet with highest cooldown advancing cooldown", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Tarantula Hawk"), 
        DefaultHunger = 28000, 
        WeldOffset = CFrame.Angles(0, 0, 0), 
        ToolWeldOffset = true, 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://126203792467378", 
        HungerFruitMultipliers = {}, 
        MovementType = "Flight", 
        MovementSpeed = 12, 
        SellPrice = 2000000, 
        Rarity = "Legendary", 
        Passives = {
            "Pollinator", 
            "Tarantula Stinger"
        }, 
        YHeightScaler = 0.2, 
        Animations = {
            Idle = l_PetAnimations_0.Wasp.Idle, 
            Walk = l_PetAnimations_0.Wasp.Walk, 
            Pollinate = l_PetAnimations_0.Wasp.Pollinate, 
            Sting = l_PetAnimations_0.Wasp.Sting
        }
    }, 
    Moth = {
        Description = "Silksong: Sings to a random pet and magically restore its hunger", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Moth"), 
        DefaultHunger = 25000, 
        WeldOffset = CFrame.Angles(0, 0, 0), 
        ToolWeldOffset = true, 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://134180528391091", 
        HungerFruitMultipliers = {}, 
        MovementType = "Flight", 
        MovementSpeed = 8, 
        SellPrice = 4000000, 
        Rarity = "Legendary", 
        Passives = {
            "Silksong"
        }, 
        YHeightScaler = 0.2, 
        Animations = {
            Idle = l_PetAnimations_0.Moth.Idle, 
            Walk = l_PetAnimations_0.Moth.Walk, 
            Sing = l_PetAnimations_0.Moth.Sing
        }
    }, 
    Butterfly = {
        Description = "Rainbow Flutter: Occasionally flies to a fruit with 4+ mutations, removes all mutations from it and converts it into rainbow. Ignores favorited fruit", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Butterfly"), 
        DefaultHunger = 26000, 
        WeldOffset = CFrame.Angles(0, 0, 0), 
        ToolWeldOffset = true, 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://119048229505161", 
        HungerFruitMultipliers = {}, 
        MovementType = "Flight", 
        MovementSpeed = 8, 
        SellPrice = 8000000, 
        Rarity = "Mythical", 
        Passives = {
            "Rainbow Flutter"
        }, 
        YHeightScaler = 0.2, 
        Animations = {
            Idle = l_PetAnimations_0.Butterfly.Idle, 
            Walk = l_PetAnimations_0.Butterfly.Walk, 
            Flutter = l_PetAnimations_0.Butterfly.Flutter
        }
    }, 
    ["Disco Bee"] = {
        Description = "Disco Disco: Occasionally has a chance to turn a nearby fruit into Disco", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Disco Bee"), 
        DefaultHunger = 25000, 
        WeldOffset = CFrame.Angles(0, 1.5707963267948966, -3.141592653589793), 
        ToolWeldOffset = true, 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://139406192899443", 
        HungerFruitMultipliers = {}, 
        MovementType = "Flight", 
        MovementSpeed = 9, 
        SellPrice = 12000000, 
        Rarity = "Divine", 
        Passives = {
            "Disco Disco"
        }, 
        YHeightScaler = 0.03, 
        Animations = {
            Idle = l_PetAnimations_0.DiscoBee.Idle, 
            Walk = l_PetAnimations_0.DiscoBee.Walk, 
            Dance = l_PetAnimations_0.DiscoBee.Dance
        }
    }, 
    Firefly = {
        Description = "Lightning Bug: Occasionally strikes a random nearby fruit, with a small chance of turning it Shocked", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Firefly"), 
        DefaultHunger = 25000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 1.5707963267948966), 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://131310748087635", 
        HungerFruitMultipliers = {}, 
        MovementType = "Flight", 
        MovementSpeed = 10, 
        SellPrice = 3300000, 
        Rarity = "Mythical", 
        Passives = {
            "Lightning Bug"
        }, 
        YHeightScaler = 0.5, 
        Animations = {
            Idle = l_PetAnimations_0.Firefly.Idle, 
            Walk = l_PetAnimations_0.Firefly.Walk
        }
    }, 
    ["Red Dragon"] = {
        Description = "Scorched Soil: Occasionally sets a random nearby fruit ablaze, turning it into Burnt", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Red Dragon"), 
        DefaultHunger = 10000, 
        WeldOffset = CFrame.Angles(0, -1.5707963267948966, 0), 
        ModelScalePerLevel = 0.004, 
        Icon = "rbxassetid://140223014467344", 
        HungerFruitMultipliers = {}, 
        MovementType = "Flight", 
        MovementSpeed = 10, 
        SellPrice = 100000, 
        Rarity = "Common", 
        Passives = {
            "Scorched Soil"
        }, 
        YHeightScaler = 0.2, 
        Animations = {}
    }, 
    ["Golden Bee"] = {
        Description = "Pollinator: Occasionally pollinates fruit & Golden Harvest: Harvested fruit has a chance to turn to gold", 
        Actions = {}, 
        Model = l_PetAssets_0:FindFirstChild("Bee"), 
        Variant = "Golden Bee", 
        DefaultHunger = 25000, 
        WeldOffset = CFrame.Angles(0, 1.5707963267948966, -3.141592653589793), 
        ToolWeldOffset = true, 
        ModelScalePerLevel = 0.005, 
        Icon = "rbxassetid://125658111834879", 
        HungerFruitMultipliers = {}, 
        MovementType = "Flight", 
        MovementSpeed = 9, 
        SellPrice = 1000000, 
        Rarity = "Mythical", 
        Passives = {
            "Pollinator", 
            "Golden Harvest"
        }, 
        YHeightScaler = 0.03, 
        Animations = {
            Idle = l_PetAnimations_0.Bee.Idle, 
            Walk = l_PetAnimations_0.Bee.Walk, 
            Pollinate = l_PetAnimations_0.Bee.Pollinate
		},
	};
	Starfish = {
		Description = "You're a Star: Gains additional XP per second";
		Actions = {};
		Model = l_PetAssets_0:FindFirstChild("Starfish");
		DefaultHunger = 1500;
		WeldOffset = CFrame.Angles(0, 0, 0);
		ModelScalePerLevel = 0.005;
		Icon = "rbxassetid://120520383369074";
		HungerFruitMultipliers = {};
		MovementType = "Grounded";
		MovementSpeed = 0.25;
		SellPrice = 50000;
		Rarity = "Common";
		Passives = {"You're a Star"};
		YHeightScaler = 0.1;
		Animations = {
			Idle = l_PetAnimations_0.Starfish.Idle;
			Walk = l_PetAnimations_0.Starfish.Walk;
		};
		States = {};
	};
	Crab = {
		Description = "Pinch Pocket: Occasionally goes to another player and pinches them and grants you a small amount of sheckles";
		Actions = {};
		Model = l_PetAssets_0:FindFirstChild("Crab");
		DefaultHunger = 3000;
		WeldOffset = CFrame.Angles(0, 0, 0);
		ToolWeldOffset = true;
		ModelScalePerLevel = 0.005;
		Icon = "rbxassetid://73259620945174";
		HungerFruitMultipliers = {};
		MovementType = "Grounded";
		MovementSpeed = 4;
		SellPrice = 60000;
		Rarity = "Common";
		Passives = {"Pinch Pocket"};
		YHeightScaler = 0.8;
		Animations = {
			Idle = l_PetAnimations_0.Crab.Idle;
			Walk = l_PetAnimations_0.Crab.Walk;
			Pinch = l_PetAnimations_0.Crab.Pinch;
		};
		States = {};
	};
	Seagull = {
		Description = "Scavenger: Shoveling plants have a chance to drop the equivalent seed. Does not work on fruits";
		Actions = {};
		Model = l_PetAssets_0:FindFirstChild("Seagull");
		DefaultHunger = 3500;
		WeldOffset = CFrame.Angles(0, 0, 0);
		ToolWeldOffset = true;
		ModelScalePerLevel = 0.005;
		Icon = "rbxassetid://125267211322255";
		HungerFruitMultipliers = {};
		MovementType = "Grounded";
		MovementSpeed = 7;
		SellPrice = 70000;
		Rarity = "Common";
		Passives = {"Scavenger", "Movement Variation"};
		YHeightScaler = 0.1;
		Animations = {
			Idle = l_PetAnimations_0.Seagull.Idle;
			Walk = l_PetAnimations_0.Seagull.Walk;
			Fly = l_PetAnimations_0.Seagull.Fly;
			FlyUp = l_PetAnimations_0.Seagull.FlyUp;
			FlyDown = l_PetAnimations_0.Seagull.FlyDown;
		};
		States = {};
	};
	Toucan = {
		Description = "Tropical Lover: Makes all nearby Tropical type plants have increased variant chance and grow bigger";
		Actions = {};
		Model = l_PetAssets_0:FindFirstChild("Toucan");
		DefaultHunger = 9000;
		WeldOffset = CFrame.Angles(0, (-math.pi/2), 0);
		ToolWeldOffset = true;
		ModelScalePerLevel = 0.005;
		Icon = "rbxassetid://118598422473758";
		HungerFruitMultipliers = {};
		MovementType = "Grounded";
		MovementSpeed = 7;
		SellPrice = 880000;
		Rarity = "Rare";
		Passives = {"Tropical Lover", "Tropical Blessing", "Movement Variation"};
		YHeightScaler = 0.9;
		Animations = {
			Idle = l_PetAnimations_0.Toucan.Idle;
			Walk = l_PetAnimations_0.Toucan.Walk;
			Fly = l_PetAnimations_0.Toucan.Fly;
			FlyUp = l_PetAnimations_0.Toucan.FlyUp;
			FlyDown = l_PetAnimations_0.Toucan.FlyDown;
		};
	};
	Flamingo = {
		Description = "Flamboyance: Occasionally stands on one legs and all nearby plants will grow incredibly fast";
		Actions = {};
		Model = l_PetAssets_0:FindFirstChild("Flamingo");
		DefaultHunger = 14000;
		WeldOffset = CFrame.Angles(0, 0, 0);
		ToolWeldOffset = true;
		ModelScalePerLevel = 0.005;
		Icon = "rbxassetid://122573642551827";
		HungerFruitMultipliers = {};
		MovementType = "Grounded";
		MovementSpeed = 8;
		SellPrice = 880000;
		Rarity = "Rare";
		Passives = {"Flamboyance"};
		YHeightScaler = 2.4;
		Animations = {
			Idle = l_PetAnimations_0.Flamingo.Idle;
			Walk = l_PetAnimations_0.Flamingo.Walk;
			Stand = l_PetAnimations_0.Flamingo.Stand;
		};
	};
	["Sea Turtle"] = {
		Description = "Shell Share: Occasionally shares its wisdom to a random active pet granting bonus experience & Water Splash: Occasionally has a chance to Wet a nearby fruit";
		Actions = {};
		Model = l_PetAssets_0:FindFirstChild("Sea Turtle");
		DefaultHunger = 22200;
		WeldOffset = CFrame.Angles(0, 0, 0);
		ToolWeldOffset = true;
		ModelScalePerLevel = 0.004;
		Icon = "rbxassetid://136324651089948";
		HungerFruitMultipliers = {};
		MovementType = "Grounded";
		MovementSpeed = 1.5;
		SellPrice = 880000;
		Rarity = "Rare";
		Passives = {"Shell Share", "Water Splash"};
		YHeightScaler = 0.1;
		Animations = {
			Idle = l_PetAnimations_0.SeaTurtle.Idle;
			Walk = l_PetAnimations_0.SeaTurtle.Walk;
			Flip = l_PetAnimations_0.SeaTurtle.Flip;
			Splash = l_PetAnimations_0.SeaTurtle.Splash;
		};
		States = {};
	};
	Seal = {
		Description = "Seal the Deal: When selling pets, has a small chance to get the pet back as its egg equivalent";
		Actions = {};
		Model = l_PetAssets_0:FindFirstChild("Seal");
		DefaultHunger = 17000;
		WeldOffset = CFrame.Angles(0, (-math.pi/2), 0);
		ToolWeldOffset = true;
		ModelScalePerLevel = 0.005;
		Icon = "rbxassetid://70977930937021";
		HungerFruitMultipliers = {};
		MovementType = "Grounded";
		MovementSpeed = 6;
		SellPrice = 880000;
		Rarity = "Rare";
		Passives = {"Seal the Deal"};
		YHeightScaler = 0.1;
		Animations = {
			Idle = l_PetAnimations_0.Seal.Idle;
			Walk = l_PetAnimations_0.Seal.Walk;
		};
		States = {};
	};
	Orangutan = {
		Description = "Helping Hands: When crafting, each material has a chance for it not to be consumed";
		Actions = {};
		Model = l_PetAssets_0:FindFirstChild("Orangutan");
		DefaultHunger = 55000;
		WeldOffset = CFrame.Angles(0, (-math.pi/2), (math.pi/2));
		ToolWeldOffset = true;
		TwoHanded = true;
		ModelScalePerLevel = 0.005;
		Icon = "rbxassetid://91252752916705";
		HungerFruitMultipliers = {};
		MovementType = "Grounded";
		MovementSpeed = 6;
		SellPrice = 880000;
		Rarity = "Rare";
		Passives = {"Helping Hands"};
		YHeightScaler = 0.1;
		Animations = {
			Idle = l_PetAnimations_0.Orangutan.Idle;
			Walk = l_PetAnimations_0.Orangutan.Walk;
		};
		States = {};
	};
	Peacock = {
		Description = "Utter Beauty: Occasionally fans its feathers and all nearby pets will advance ability cooldowns";
		Actions = {};
		Model = l_PetAssets_0:FindFirstChild("Peacock");
		DefaultHunger = 19000;
		WeldOffset = CFrame.Angles(0, (-math.pi/2), (math.pi/2));
		ToolWeldOffset = true;
		ModelScalePerLevel = 0.005;
		Icon = "rbxassetid://79434662175672";
		HungerFruitMultipliers = {};
		MovementType = "Grounded";
		MovementSpeed = 8;
		SellPrice = 3300000;
		Rarity = "Legendary";
		Passives = {"Utter Beauty"};
		YHeightScaler = 0.8;
		Animations = {
			Idle = l_PetAnimations_0.Peacock.Idle;
			Walk = l_PetAnimations_0.Peacock.Walk;
			Fan = l_PetAnimations_0.Peacock.Fan;
		};
		States = {};
	};
	Capybara = {
		Description = "Chill Zone: Nearby pets' hunger will not go down and they will gain additional xp per second";
		Actions = {};
		Model = l_PetAssets_0:FindFirstChild("Capybara");
		DefaultHunger = 30000;
		WeldOffset = CFrame.Angles(0, 0, 0);
		ToolWeldOffset = true;
		ModelScalePerLevel = 0.005;
		Icon = "rbxassetid://109096250560950";
		HungerFruitMultipliers = {};
		MovementType = "Grounded";
		MovementSpeed = 5;
		SellPrice = 3300000;
		Rarity = "Legendary";
		Passives = {"Chill Zone"};
		YHeightScaler = 1.5;
		Animations = {
			Idle = l_PetAnimations_0.Capybara.Idle;
			Walk = l_PetAnimations_0.Capybara.Walk;
		};
    },
	["Scarlet Macaw"] = {
		Description = "Verdant Bird: Occasionally has a chance to mutate nearby fruits Verdant";
		Actions = {};
		Model = l_PetAssets_0:FindFirstChild("Scarlet Macaw");
		DefaultHunger = 12000;
		WeldOffset = CFrame.Angles(0, (-math.pi/2), 0);
		ToolWeldOffset = true;
		ModelScalePerLevel = 0.005;
		Icon = "rbxassetid://103592675269053";
		HungerFruitMultipliers = {};
		MovementType = "Grounded";
		MovementSpeed = 7;
		SellPrice = 3300000;
		Rarity = "Legendary";
		Passives = {"Verdant Bird", "Movement Variation"};
		YHeightScaler = 0.9;
		Animations = {
			Idle = l_PetAnimations_0.ScarletMacaw.Idle;
			Walk = l_PetAnimations_0.ScarletMacaw.Walk;
			Fly = l_PetAnimations_0.ScarletMacaw.Fly;
			FlyUp = l_PetAnimations_0.ScarletMacaw.FlyUp;
			FlyDown = l_PetAnimations_0.ScarletMacaw.FlyDown;
		};
	};
	Ostrich = {
		Description = "Eggsperience: Grants pets hatched from eggs an age bonus";
		Actions = {};
		Model = l_PetAssets_0:FindFirstChild("Ostrich");
		DefaultHunger = 20000;
		WeldOffset = CFrame.Angles(0, (-math.pi/2), 0);
		ToolWeldOffset = true;
		ModelScalePerLevel = 0.005;
		Icon = "rbxassetid://85113894132517";
		HungerFruitMultipliers = {};
		MovementType = "Grounded";
		MovementSpeed = 8.5;
		SellPrice = 4400000;
		Rarity = "Legendary";
		Passives = {"Eggsperience"};
		YHeightScaler = 2.4;
		Animations = {
			Idle = l_PetAnimations_0.Ostrich.Idle;
			Walk = l_PetAnimations_0.Ostrich.Walk;
		};
      };
	["Mimic Octopus"] = {
		Description = "Mimicry: Mimics and copies an ability from another pet and peforms its ability";
		Actions = {};
		Model = l_PetAssets_0:FindFirstChild("Mimic Octopus");
		DefaultHunger = 25000;
		WeldOffset = CFrame.Angles(0, 0, 0);
		ToolWeldOffset = true;
		ModelScalePerLevel = 0.005;
		Icon = "rbxassetid://118831959038511";
		HungerFruitMultipliers = {};
		MovementType = "Grounded";
		MovementSpeed = 9;
		SellPrice = 10000000;
		Rarity = "Mythical";
		Passives = {"Mimicry"};
		YHeightScaler = 0.1;
		Animations = {
			Idle = l_PetAnimations_0.MimicOctopus.Idle;
			Walk = l_PetAnimations_0.MimicOctopus.Walk;
			
		};
		States = {};
    }
};

for v8, v9 in v3 do
    if not v9.Model and v3:IsServer() then
        warn(v8, "has no attached model defaulting to Dog model")
		v9.Model = l_PetAssets_0:FindFirstChild("Dog")
	end
	
    if v9.Model and v9.Model.Name ~= v8 then
        local v10 = v9.Model:Clone()
        v10.Name = v8
		v10.Parent = l_PetAssets_0
	end
	
    v9.YHeightScaler = v9.YHeightScaler or 0
    v9.States = v9.States or {}
end

return v3;